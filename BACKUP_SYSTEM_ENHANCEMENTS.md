# Enhanced Backup System Documentation

## Overview
The backup system has been significantly enhanced to provide comprehensive backup and restore functionality for the entire application, including system files, databases, dependencies, and build assets.

## Key Enhancements

### 1. Complete File System Backup
**Previous**: Only basic application files were backed up
**Enhanced**: Now includes all critical system components:

- **Application Files**: app, config, database/migrations, resources, routes, plugins
- **Configuration Files**: .env, composer.json, composer.lock, package.json, package-lock.json, vite.config.js, tailwind.config.js
- **Dependencies**: vendor directory (Composer), node_modules directory (NPM)
- **Storage Directories**: storage/app, storage/framework, storage/logs
- **Public Directories**: public/uploads, public/storage, public/build
- **Environment Files**: .env variants, configuration cache, route cache

### 2. Enhanced Database Backup
**Previous**: Basic SQL dump
**Enhanced**: Comprehensive database handling:

- **Complete Database Replacement**: Drops all existing tables before restore
- **Dependency-Aware Restoration**: Sorts CREATE TABLE statements to handle foreign key dependencies
- **Multi-Database Support**: MySQL, SQLite, PostgreSQL
- **Error Handling**: Continues restoration even if individual statements fail
- **SQLite File Backup**: Includes physical database file for SQLite databases

### 3. Dependency Management Integration
**New Feature**: Automatic dependency installation during restore:

- **Composer Dependencies**: Runs `composer install --no-dev --optimize-autoloader`
- **NPM Dependencies**: Runs `npm install`
- **Version Tracking**: Records Composer, Node.js, and NPM versions in backup metadata

### 4. Frontend Asset Building
**New Feature**: Automatic frontend asset compilation:

- **Build Process**: Runs `npm run build` after dependency installation
- **Asset Restoration**: Includes existing build artifacts in backup
- **Vite Configuration**: Backs up vite.config.js and related configuration

### 5. Environment-Specific Configuration
**Enhanced**: Robust environment handling:

- **Sensitive Data Masking**: Creates sanitized .env backup with masked sensitive values
- **Multiple Environment Files**: Backs up .env.example, .env.local, .env.production, etc.
- **Configuration Cache**: Includes Laravel configuration and route cache files
- **Environment Metadata**: Records app environment, URL, timezone, locale in backup

### 6. Enhanced Metadata
**Previous**: Basic backup information
**Enhanced**: Comprehensive system information:

```json
{
  "created_at": "2024-01-01T12:00:00Z",
  "created_by": "<EMAIL>",
  "app_version": "1.0.0",
  "description": "User description",
  "includes_files": true,
  "database_driver": "mysql",
  "php_version": "8.2.0",
  "laravel_version": "10.x",
  "environment": "production",
  "app_url": "https://example.com",
  "timezone": "UTC",
  "locale": "en",
  "includes_vendor": true,
  "includes_node_modules": true,
  "composer_version": "2.6.0",
  "node_version": "v18.17.0",
  "npm_version": "9.6.7"
}
```

### 7. Comprehensive Restore Process
**Previous**: Basic database and cache clearing
**Enhanced**: Complete system restoration:

1. **Database Restoration**: Complete replacement with dependency handling
2. **File Restoration**: Systematic replacement of all application files
3. **Dependency Installation**: Automatic Composer and NPM installation
4. **Asset Building**: Frontend asset compilation
5. **Cache Clearing**: All Laravel caches (config, route, view, cache)
6. **Session Management**: Force re-authentication for security
7. **Permission Seeding**: Ensure roles and permissions are properly set up
8. **Storage Symlink**: Recreate public storage symlink

### 8. Security Enhancements
**New Features**: Enhanced security measures:

- **Sensitive Data Masking**: Passwords, API keys, and secrets are masked in backup metadata
- **Permission Verification**: Ensures proper user roles after restoration
- **Session Clearing**: Forces re-authentication after restore
- **Access Control**: Maintains existing permission checks

## File Structure in Backup

```
backup-2024-01-01_12-00-00.zip
├── database.sql                    # Complete database dump
├── metadata.json                   # Enhanced backup metadata
├── .env                           # Environment configuration
├── composer.json                  # PHP dependencies
├── composer.lock                  # PHP dependency lock
├── package.json                   # Node.js dependencies
├── package-lock.json              # Node.js dependency lock
├── vite.config.js                 # Vite configuration
├── tailwind.config.js             # Tailwind configuration
├── app/                           # Application code
├── config/                        # Laravel configuration
├── database/migrations/           # Database migrations
├── resources/                     # Views, assets, lang files
├── routes/                        # Route definitions
├── plugins/                       # Custom plugins
├── vendor/                        # Composer dependencies
├── node_modules/                  # NPM dependencies
├── storage/app/                   # Application storage
├── storage/framework/             # Framework storage
├── storage/logs/                  # Application logs
├── public/uploads/                # User uploads
├── public/storage/                # Public storage
├── public/build/                  # Built assets
├── environment/                   # Environment files
│   ├── .env.backup               # Sanitized environment
│   ├── .env.example              # Environment template
│   └── ...                       # Other env files
├── cache/                         # Configuration cache
└── database/                      # SQLite database files (if applicable)
```

## Usage Instructions

### Creating a Backup
1. Navigate to Admin Panel → Settings → Backups
2. Click "Create Backup"
3. Enable "Include Files" for complete system backup
4. Add optional description
5. Click "Create"

### Restoring from Backup
⚠️ **Warning**: Restoration completely replaces the current system

1. Navigate to Admin Panel → Settings → Backups
2. Find the desired backup
3. Click "Restore"
4. Confirm the action
5. Wait for the process to complete (may take several minutes)
6. You will be redirected to login page

### Testing the System
Run the test script to verify functionality:
```bash
php test_backup_system.php
```

## Technical Implementation

### Key Methods Added/Enhanced
- `addFilesToBackup()`: Enhanced to include all system files
- `addEnvironmentConfigurations()`: New method for environment handling
- `restoreBackup()`: Completely rewritten for comprehensive restoration
- `installDependencies()`: New method for dependency installation
- `buildFrontendAssets()`: New method for asset building
- `restoreDatabase()`: Enhanced with dependency handling
- `parseSqlStatements()`: Improved SQL parsing
- `sanitizeEnvContent()`: New method for sensitive data masking

### Dependencies
- **PHP Extensions**: ZipArchive (required)
- **System Commands**: composer, node, npm (for full functionality)
- **Permissions**: Write access to storage, vendor, node_modules, public directories

## Maintenance

### Backup Retention
- **Default**: 30 days retention, 10 backup maximum
- **Configurable**: Via `config/settings.php`
- **Automatic Cleanup**: Old backups are automatically removed

### Monitoring
- **Logging**: All operations are logged to Laravel log files
- **Error Handling**: Graceful degradation with detailed error messages
- **Progress Tracking**: Step-by-step logging during restore process

## Troubleshooting

### Common Issues
1. **ZipArchive not available**: Install php-zip extension
2. **Permission denied**: Ensure write permissions on storage directories
3. **Composer/NPM not found**: Install required tools or update PATH
4. **Large backup files**: Consider excluding node_modules for smaller backups
5. **Restore timeout**: Increase PHP max_execution_time for large restorations

### Recovery
If restore fails:
1. Check Laravel logs for detailed error information
2. Manually restore database from backup SQL file
3. Manually copy files from extracted backup
4. Run `composer install` and `npm install` manually
5. Clear caches with `php artisan cache:clear`

## Future Enhancements
- Incremental backup support
- Cloud storage integration
- Backup encryption
- Automated backup scheduling
- Backup verification and integrity checks
- Selective restore options
