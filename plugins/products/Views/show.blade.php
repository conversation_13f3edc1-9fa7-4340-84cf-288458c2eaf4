@extends('layouts.app')

@section('title', $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                @if($product->icon)
                    <i class="{{ $product->icon }} text-4xl text-blue-500 mr-4"></i>
                @else
                    <i class="fas fa-box text-4xl text-gray-400 dark:text-gray-500 mr-4"></i>
                @endif
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $product->name }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $product->slug }}</p>
                    <div class="flex items-center mt-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                            {{ $product->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        @if($product->latest_release)
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                Latest: {{ $product->latest_release->formatted_version }}
                            </span>
                        @endif
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Products
                </a>
                @if(auth()->user()->hasPermission('manage_products'))
                    <a href="{{ route('products.edit', $product) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </a>
                @endif
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Businesses</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['total_businesses'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Documents</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['current_documents'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tag text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Releases</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['published_releases'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-dollar-sign text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Pricing Items</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['pricing_items'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6 transition duration-150 ease-in-out">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <a href="#overview" class="tab-link border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="overview">
                        Overview
                    </a>
                    <a href="#documents" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="documents">
                        Documents ({{ $stats['current_documents'] }})
                    </a>
                    <a href="#releases" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="releases">
                        Releases ({{ $stats['published_releases'] }})
                    </a>
                    <a href="#pricing" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="pricing">
                        Pricing ({{ $stats['pricing_items'] }})
                    </a>
                    <a href="#businesses" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="businesses">
                        Businesses ({{ $stats['total_businesses'] }})
                    </a>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Product Information -->
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Information</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Basic details and description</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-0">
                            <dl class="sm:divide-y sm:divide-gray-200 dark:divide-gray-700">
                                @if($product->description)
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Description</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $product->description }}</dd>
                                    </div>
                                @endif
                                @if($product->target_audience)
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Target Audience</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $product->target_audience }}</dd>
                                    </div>
                                @endif
                                @if($product->use_cases)
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Use Cases</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $product->use_cases }}</dd>
                                    </div>
                                @endif
                                @if($product->reference_links && count($product->reference_links) > 0)
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Reference Links</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                            <ul class="space-y-1">
                                                @foreach($product->reference_links as $link)
                                                    <li>
                                                        <a href="{{ $link }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                                            {{ $link }} <i class="fas fa-external-link-alt ml-1"></i>
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </dd>
                                    </div>
                                @endif
                                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Created By</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                        {{ $product->creator->name }} on {{ $product->created_at->format('M d, Y') }}
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Documents Tab -->
            <div id="documents-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Documents</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Current version documents</p>
                        </div>
                        @if(auth()->user()->hasPermission('manage_product_documents'))
                            <a href="{{ route('products.documents.create', $product) }}" 
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Upload Document
                            </a>
                        @endif
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if($product->currentDocuments->count() > 0)
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($product->currentDocuments as $document)
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="{{ $document->file_icon }} mr-3"></i>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $document->name }}</p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        {{ $document->category_label }} • {{ $document->formatted_file_size }} • v{{ $document->version }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('view_product_documents'))
                                                    <a href="{{ route('products.documents.download', [$product, $document]) }}" 
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm">
                                                        Download
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="{{ route('products.documents.index', $product) }}" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    View All Documents →
                                </a>
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-file text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No documents yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Upload your first product document to get started.</p>
                                @if(auth()->user()->hasPermission('manage_product_documents'))
                                    <a href="{{ route('products.documents.create', $product) }}" 
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Upload Document
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Releases Tab -->
            <div id="releases-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Releases</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Version history and release notes</p>
                        </div>
                        @if(auth()->user()->hasPermission('manage_product_releases'))
                            <a href="{{ route('products.releases.create', $product) }}"
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Create Release
                            </a>
                        @endif
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if($product->publishedReleases->count() > 0)
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($product->publishedReleases as $release)
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $release->release_type_badge_class }}">
                                                        {{ $release->release_type_label }}
                                                    </span>
                                                </div>
                                                <div class="ml-4">
                                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                        {{ $release->formatted_version }} - {{ $release->title }}
                                                    </h4>
                                                    @if($release->description)
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $release->short_description }}</p>
                                                    @endif
                                                    <div class="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        <span>Released {{ $release->release_date->format('M d, Y') }}</span>
                                                        @if($release->total_changes_count > 0)
                                                            <span class="ml-4">{{ $release->total_changes_count }} changes</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('view_product_releases'))
                                                    <a href="{{ route('products.releases.show', [$product, $release]) }}"
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                                        View Details
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="{{ route('products.releases.index', $product) }}"
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    View All Releases →
                                </a>
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-tag text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No releases yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first product release to track version history.</p>
                                @if(auth()->user()->hasPermission('manage_product_releases'))
                                    <a href="{{ route('products.releases.create', $product) }}"
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Create Release
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Pricing Tab -->
            <div id="pricing-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Pricing Items</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Available pricing items and plans</p>
                        </div>
                        @if(auth()->user()->hasPermission('manage_products'))
                            <a href="{{ route('products.pricing.create', $product) }}"
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Add Pricing Item
                            </a>
                        @endif
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if($product->activePricingItems->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Name
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Pricing Model
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Price
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Unit Type
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Currency
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                                        @foreach($product->activePricingItems as $pricing)
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 {{ $pricing->is_popular ? 'bg-blue-50' : '' }} transition duration-150 ease-in-out">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div>
                                                            <div class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                                                                {{ $pricing->name }}
                                                                @if($pricing->is_popular)
                                                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                                                        Popular
                                                                    </span>
                                                                @endif
                                                            </div>
                                                            @if($pricing->description)
                                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 max-w-xs truncate">
                                                                    {{ $pricing->description }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                                        {{ $pricing->pricing_model_label }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white">
                                                        @if($pricing->price !== null)
                                                            {{ $pricing->formatted_price }}
                                                        @else
                                                            <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500">Contact for pricing</span>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white">
                                                        @if($pricing->unit_type)
                                                            {{ $pricing->unit_type_label }}
                                                        @else
                                                            <span class="text-gray-400 dark:text-gray-500">-</span>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white">{{ $pricing->currency }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $pricing->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                                        {{ $pricing->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    @if(auth()->user()->hasPermission('manage_products'))
                                                        <a href="{{ route('products.pricing.edit', [$product, $pricing]) }}"
                                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">
                                                            Edit
                                                        </a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="{{ route('products.pricing.index', $product) }}"
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    Manage All Pricing →
                                </a>
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-dollar-sign text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No pricing items yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first pricing item to define product costs.</p>
                                @if(auth()->user()->hasPermission('manage_products'))
                                    <a href="{{ route('products.pricing.create', $product) }}"
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Add Pricing Item
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Businesses Tab -->
            <div id="businesses-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Assigned Businesses</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Businesses using this product</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if($product->businesses->count() > 0)
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($product->businesses as $business)
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $business->pivot->status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' }} transition duration-150 ease-in-out">
                                                        {{ ucfirst($business->pivot->status) }}
                                                    </span>
                                                </div>
                                                <div class="ml-4">
                                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ $business->name }}</h4>
                                                    <div class="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        @if($business->pivot->start_date)
                                                            <span>Started {{ \Carbon\Carbon::parse($business->pivot->start_date)->format('M d, Y') }}</span>
                                                        @endif
                                                        @if($business->pivot->product_version)
                                                            <span class="ml-4">Version {{ $business->pivot->product_version }}</span>
                                                        @endif
                                                    </div>
                                                    @if($business->pivot->notes)
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $business->pivot->notes }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('view_businesses'))
                                                    <a href="{{ route('business.show', $business) }}"
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                                        View Business
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-building text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No businesses assigned</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">This product hasn't been assigned to any businesses yet.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active classes from all tabs
            tabLinks.forEach(l => {
                l.classList.remove('border-blue-500', 'text-blue-600 dark:text-blue-400');
                l.classList.add('border-transparent', 'text-gray-500 dark:text-gray-400 dark:text-gray-500');
            });
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            
            // Add active class to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500 dark:text-gray-400 dark:text-gray-500');
            this.classList.add('border-blue-500', 'text-blue-600 dark:text-blue-400');
            
            // Show corresponding tab content
            const tabId = this.getAttribute('data-tab') + '-tab';
            const tabContent = document.getElementById(tabId);
            if (tabContent) {
                tabContent.classList.remove('hidden');
            }
        });
    });
});
</script>
@endsection
