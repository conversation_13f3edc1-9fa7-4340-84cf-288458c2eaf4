@extends('layouts.app')

@section('title', 'Create Release - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create New Release</h1>
                <p class="text-gray-600 mt-1">{{ $product->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.releases.index', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Releases
                </a>
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Back to Product
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.releases.store', $product) }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Release Information</h3>
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700">
                            Version <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="version" id="version" value="{{ old('version', $suggestedVersion) }}" required
                               placeholder="e.g., 1.0.0, 2.1.3"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('version') border-red-300 @enderror">
                        @error('version')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Use semantic versioning (e.g., 1.0.0)</p>
                    </div>

                    <!-- Release Type -->
                    <div>
                        <label for="release_type" class="block text-sm font-medium text-gray-700">
                            Release Type <span class="text-red-500">*</span>
                        </label>
                        <select name="release_type" id="release_type" required onchange="suggestVersion()"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('release_type') border-red-300 @enderror">
                            @foreach($releaseTypes as $typeKey => $typeLabel)
                                <option value="{{ $typeKey }}" {{ old('release_type', 'minor') === $typeKey ? 'selected' : '' }}>
                                    {{ $typeLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('release_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose the type of release</p>
                    </div>

                    <!-- Title -->
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700">
                            Release Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="{{ old('title') }}" required
                               placeholder="e.g., New Dashboard Features, Bug Fixes and Improvements"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('title') border-red-300 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Release Date -->
                    <div>
                        <label for="release_date" class="block text-sm font-medium text-gray-700">
                            Release Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="release_date" id="release_date" value="{{ old('release_date', date('Y-m-d')) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('release_date') border-red-300 @enderror">
                        @error('release_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Published Status -->
                    <div class="flex items-center">
                        <div class="flex items-center h-5">
                            <input type="checkbox" name="is_published" id="is_published" value="1" {{ old('is_published') ? 'checked' : '' }}
                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="is_published" class="font-medium text-gray-700">Publish immediately</label>
                            <p class="text-gray-500">Make this release visible to users</p>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="4"
                                  placeholder="Describe what's new in this release..."
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Changelog -->
                    <div class="md:col-span-2">
                        <label for="changelog" class="block text-sm font-medium text-gray-700">
                            Detailed Changelog
                        </label>
                        <textarea name="changelog" id="changelog" rows="6"
                                  placeholder="Detailed technical changelog for developers..."
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('changelog') border-red-300 @enderror">{{ old('changelog') }}</textarea>
                        @error('changelog')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Technical details for developers and advanced users</p>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">New Features</h3>
                    <div id="features-container">
                        @if(old('features'))
                            @foreach(old('features') as $index => $feature)
                                <div class="feature-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="features[]" value="{{ $feature }}"
                                           placeholder="Describe a new feature..."
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" onclick="removeFeature(this)" 
                                            class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="feature-item flex items-center space-x-2 mb-2">
                                <input type="text" name="features[]" placeholder="Describe a new feature..."
                                       class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" onclick="removeFeature(this)" 
                                        class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addFeature()"
                            class="mt-2 bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded">
                        <i class="fas fa-plus mr-2"></i>Add Feature
                    </button>
                </div>

                <!-- Bug Fixes Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Bug Fixes</h3>
                    <div id="bug-fixes-container">
                        @if(old('bug_fixes'))
                            @foreach(old('bug_fixes') as $index => $bugFix)
                                <div class="bug-fix-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="bug_fixes[]" value="{{ $bugFix }}"
                                           placeholder="Describe a bug fix..."
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" onclick="removeBugFix(this)"
                                            class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="bug-fix-item flex items-center space-x-2 mb-2">
                                <input type="text" name="bug_fixes[]" placeholder="Describe a bug fix..."
                                       class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" onclick="removeBugFix(this)"
                                        class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addBugFix()"
                            class="mt-2 bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded">
                        <i class="fas fa-plus mr-2"></i>Add Bug Fix
                    </button>
                </div>

                <!-- Breaking Changes Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Breaking Changes</h3>
                    <p class="text-sm text-gray-600 mb-4">List any changes that might break existing functionality</p>
                    <div id="breaking-changes-container">
                        @if(old('breaking_changes'))
                            @foreach(old('breaking_changes') as $index => $breakingChange)
                                <div class="breaking-change-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="breaking_changes[]" value="{{ $breakingChange }}"
                                           placeholder="Describe a breaking change..."
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" onclick="removeBreakingChange(this)"
                                            class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="breaking-change-item flex items-center space-x-2 mb-2">
                                <input type="text" name="breaking_changes[]" placeholder="Describe a breaking change..."
                                       class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" onclick="removeBreakingChange(this)"
                                        class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addBreakingChange()"
                            class="mt-2 bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded">
                        <i class="fas fa-plus mr-2"></i>Add Breaking Change
                    </button>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.releases.index', $product) }}"
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Release
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function addFeature() {
    const container = document.getElementById('features-container');
    const newItem = document.createElement('div');
    newItem.className = 'feature-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="features[]" placeholder="Describe a new feature..."
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeFeature(this)"
                class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeFeature(button) {
    const container = document.getElementById('features-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function addBugFix() {
    const container = document.getElementById('bug-fixes-container');
    const newItem = document.createElement('div');
    newItem.className = 'bug-fix-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="bug_fixes[]" placeholder="Describe a bug fix..."
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeBugFix(this)"
                class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeBugFix(button) {
    const container = document.getElementById('bug-fixes-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function addBreakingChange() {
    const container = document.getElementById('breaking-changes-container');
    const newItem = document.createElement('div');
    newItem.className = 'breaking-change-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="breaking_changes[]" placeholder="Describe a breaking change..."
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeBreakingChange(this)"
                class="bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeBreakingChange(button) {
    const container = document.getElementById('breaking-changes-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function suggestVersion() {
    // This function could make an AJAX call to suggest the next version
    // For now, it's just a placeholder
    console.log('Suggesting version based on release type...');
}
</script>
@endsection
