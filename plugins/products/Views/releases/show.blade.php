@extends('layouts.app')

@section('title', $release->formatted_version . ' - ' . $release->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $release->release_type_badge_class }} mr-4">
                    {{ $release->release_type_label }}
                </span>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $release->formatted_version }}</h1>
                    <h2 class="text-xl text-gray-700 mt-1">{{ $release->title }}</h2>
                    <p class="text-gray-600 mt-1">{{ $product->name }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $release->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ $release->is_published ? 'Published' : 'Draft' }}
                        </span>
                        @if($release->release_date)
                            <span class="text-sm text-gray-500">
                                Released {{ $release->release_date->format('M d, Y') }}
                            </span>
                        @endif
                        <span class="text-sm text-gray-500">
                            by {{ $release->creator->name }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.releases.index', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Releases
                </a>
                @if(auth()->user()->hasPermission('manage_product_releases'))
                    <a href="{{ route('products.releases.edit', [$product, $release]) }}" 
                       class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                    @if(!$release->is_published)
                        <form method="POST" action="{{ route('products.releases.publish', [$product, $release]) }}" 
                              class="inline">
                            @csrf
                            <button type="submit" 
                                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                                    onclick="return confirm('Are you sure you want to publish this release?')">
                                <i class="fas fa-rocket mr-2"></i>Publish
                            </button>
                        </form>
                    @endif
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Release Description -->
                @if($release->description)
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Release Notes</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">What's new in this release</p>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                            <div class="prose max-w-none">
                                {!! nl2br(e($release->description)) !!}
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Changelog -->
                @if($release->hasChanges())
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Changelog</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Detailed list of changes in this release</p>
                        </div>
                        <div class="border-t border-gray-200">
                            @if($release->new_features && count($release->new_features_array) > 0)
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                                    <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                        <i class="fas fa-plus-circle text-green-500 mr-2"></i>
                                        New Features
                                    </h4>
                                    <ul class="space-y-2">
                                        @foreach($release->new_features_array as $feature)
                                            <li class="flex items-start">
                                                <i class="fas fa-check text-green-500 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700">{{ $feature }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if($release->improvements && count($release->improvements_array) > 0)
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                                    <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                        <i class="fas fa-arrow-up text-blue-500 mr-2"></i>
                                        Improvements
                                    </h4>
                                    <ul class="space-y-2">
                                        @foreach($release->improvements_array as $improvement)
                                            <li class="flex items-start">
                                                <i class="fas fa-arrow-right text-blue-500 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700">{{ $improvement }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if($release->bug_fixes && count($release->bug_fixes_array) > 0)
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                                    <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                        <i class="fas fa-bug text-red-500 mr-2"></i>
                                        Bug Fixes
                                    </h4>
                                    <ul class="space-y-2">
                                        @foreach($release->bug_fixes_array as $fix)
                                            <li class="flex items-start">
                                                <i class="fas fa-times text-red-500 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700">{{ $fix }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if($release->breaking_changes && count($release->breaking_changes_array) > 0)
                                <div class="px-4 py-5 sm:px-6">
                                    <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                        Breaking Changes
                                    </h4>
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                        <ul class="space-y-2">
                                            @foreach($release->breaking_changes_array as $change)
                                                <li class="flex items-start">
                                                    <i class="fas fa-exclamation text-yellow-600 mt-1 mr-2 text-sm"></i>
                                                    <span class="text-sm text-gray-700">{{ $change }}</span>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Migration Notes -->
                @if($release->migration_notes)
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Migration Notes</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Important information for upgrading</p>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                <div class="prose max-w-none text-sm">
                                    {!! nl2br(e($release->migration_notes)) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Release Information -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Release Information</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Version</dt>
                                <dd class="text-lg font-semibold text-gray-900">{{ $release->formatted_version }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Release Type</dt>
                                <dd class="text-sm text-gray-900">{{ $release->release_type_label }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $release->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $release->is_published ? 'Published' : 'Draft' }}
                                    </span>
                                </dd>
                            </div>
                            @if($release->release_date)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Release Date</dt>
                                    <dd class="text-sm text-gray-900">{{ $release->release_date->format('M d, Y \a\t g:i A') }}</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                <dd class="text-sm text-gray-900">{{ $release->creator->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="text-sm text-gray-900">{{ $release->created_at->format('M d, Y \a\t g:i A') }}</dd>
                            </div>
                            @if($release->updated_at != $release->created_at)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900">{{ $release->updated_at->format('M d, Y \a\t g:i A') }}</dd>
                                </div>
                            @endif
                        </dl>
                    </div>
                </div>

                <!-- Release Statistics -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Statistics</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Changes</dt>
                                <dd class="text-lg font-semibold text-gray-900">{{ $release->total_changes_count }}</dd>
                            </div>
                            @if($release->new_features_count > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">New Features</dt>
                                    <dd class="text-sm text-gray-900">{{ $release->new_features_count }}</dd>
                                </div>
                            @endif
                            @if($release->improvements_count > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Improvements</dt>
                                    <dd class="text-sm text-gray-900">{{ $release->improvements_count }}</dd>
                                </div>
                            @endif
                            @if($release->bug_fixes_count > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Bug Fixes</dt>
                                    <dd class="text-sm text-gray-900">{{ $release->bug_fixes_count }}</dd>
                                </div>
                            @endif
                            @if($release->breaking_changes_count > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Breaking Changes</dt>
                                    <dd class="text-sm text-red-600 font-medium">{{ $release->breaking_changes_count }}</dd>
                                </div>
                            @endif
                        </dl>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Navigation</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <div class="space-y-3">
                            @if($previousRelease)
                                <a href="{{ route('products.releases.show', [$product, $previousRelease]) }}" 
                                   class="flex items-center text-sm text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-chevron-left mr-2"></i>
                                    Previous: {{ $previousRelease->formatted_version }}
                                </a>
                            @endif
                            @if($nextRelease)
                                <a href="{{ route('products.releases.show', [$product, $nextRelease]) }}" 
                                   class="flex items-center text-sm text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-chevron-right mr-2"></i>
                                    Next: {{ $nextRelease->formatted_version }}
                                </a>
                            @endif
                            <a href="{{ route('products.show', $product) }}" 
                               class="flex items-center text-sm text-gray-600 hover:text-gray-900">
                                <i class="fas fa-box mr-2"></i>
                                Back to Product
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
