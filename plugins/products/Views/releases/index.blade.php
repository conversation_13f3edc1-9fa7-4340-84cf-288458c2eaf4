@extends('layouts.app')

@section('title', 'Releases - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Releases</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $product->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
                @if(auth()->user()->hasPermission('manage_product_releases'))
                    <a href="{{ route('products.releases.create', $product) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create Release
                    </a>
                @endif
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tag text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Releases</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['total'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Published</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['published'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Draft</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['draft'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-code-branch text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Latest Version</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['latest_version'] ?? 'N/A' }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
            <div class="p-6">
                <form method="GET" action="{{ route('products.releases.index', $product) }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}"
                                   placeholder="Search releases..."
                                   class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <select name="status" id="status"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                                <option value="">All Statuses</option>
                                <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                                <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                            </select>
                        </div>

                        <!-- Release Type Filter -->
                        <div>
                            <label for="release_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Release Type</label>
                            <select name="release_type" id="release_type"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                                <option value="">All Types</option>
                                @foreach($releaseTypes as $typeKey => $typeLabel)
                                    <option value="{{ $typeKey }}" {{ request('release_type') === $typeKey ? 'selected' : '' }}>
                                        {{ $typeLabel }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-end">
                            <button type="submit" 
                                    class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded mr-2 transition duration-150 ease-in-out">
                                Apply Filters
                            </button>
                            <a href="{{ route('products.releases.index', $product) }}" 
                               class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Releases List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            @if($releases->count() > 0)
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($releases as $release)
                        <li class="px-4 py-6 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $release->release_type_badge_class }}">
                                            {{ $release->release_type_label }}
                                        </span>
                                    </div>
                                    <div class="ml-6 flex-1">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    <a href="{{ route('products.releases.show', [$product, $release]) }}" 
                                                       class="hover:text-blue-600 dark:text-blue-400">
                                                        {{ $release->formatted_version }} - {{ $release->title }}
                                                    </a>
                                                </h3>
                                                @if($release->description)
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $release->short_description }}</p>
                                                @endif
                                                <div class="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 space-x-4">
                                                    <span class="flex items-center">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        {{ $release->release_date ? $release->release_date->format('M d, Y') : 'Not set' }}
                                                    </span>
                                                    @if($release->total_changes_count > 0)
                                                        <span class="flex items-center">
                                                            <i class="fas fa-list mr-1"></i>
                                                            {{ $release->total_changes_count }} changes
                                                        </span>
                                                    @endif
                                                    <span class="flex items-center">
                                                        <i class="fas fa-user mr-1"></i>
                                                        {{ $release->creator->name }}
                                                    </span>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $release->is_published ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' }} transition duration-150 ease-in-out">
                                                        {{ $release->is_published ? 'Published' : 'Draft' }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if(auth()->user()->hasPermission('view_product_releases'))
                                        <a href="{{ route('products.releases.show', [$product, $release]) }}" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                            View
                                        </a>
                                    @endif
                                    @if(auth()->user()->hasPermission('manage_product_releases'))
                                        <a href="{{ route('products.releases.edit', [$product, $release]) }}" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm font-medium">
                                            Edit
                                        </a>
                                        @if(!$release->is_published)
                                            <form method="POST" action="{{ route('products.releases.publish', [$product, $release]) }}" 
                                                  class="inline">
                                                @csrf
                                                <button type="submit" 
                                                        class="text-green-600 dark:text-green-400 hover:text-green-900 text-sm font-medium"
                                                        onclick="return confirm('Are you sure you want to publish this release?')">
                                                    Publish
                                                </button>
                                            </form>
                                        @else
                                            <form method="POST" action="{{ route('products.releases.unpublish', [$product, $release]) }}" 
                                                  class="inline">
                                                @csrf
                                                <button type="submit" 
                                                        class="text-orange-600 dark:text-orange-400 hover:text-orange-900 text-sm font-medium"
                                                        onclick="return confirm('Are you sure you want to unpublish this release?')">
                                                    Unpublish
                                                </button>
                                            </form>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>

                <!-- Pagination -->
                @if($releases->hasPages())
                    <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700 sm:px-6 transition duration-150 ease-in-out">
                        {{ $releases->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-12">
                    <i class="fas fa-tag text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No releases found</h3>
                    <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first product release to track version history.</p>
                    @if(auth()->user()->hasPermission('manage_product_releases'))
                        <a href="{{ route('products.releases.create', $product) }}" 
                           class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-plus mr-2"></i>Create Release
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
