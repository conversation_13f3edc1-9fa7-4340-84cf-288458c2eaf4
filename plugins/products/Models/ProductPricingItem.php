<?php

namespace Plugins\Products\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductPricingItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'name',
        'description',
        'price',
        'pricing_model',
        'unit_type',
        'custom_unit_name',
        'unit_label',
        'min_quantity',
        'max_quantity',
        'category',
        'currency',
        'billing_cycle',
        'features',
        'metadata',
        'include_in_quotations',
        'quotation_description',
        'is_active',
        'sort_order',
        'is_popular',
        'button_text',
        'notes',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'min_quantity' => 'integer',
        'max_quantity' => 'integer',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
        'include_in_quotations' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Billing cycle labels
     */
    public static function getBillingCycles(): array
    {
        return [
            'one-time' => 'One-time',
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
            'per_consumption' => 'Per Consumption',
            'custom' => 'Custom',
        ];
    }

    /**
     * Currency options
     */
    public static function getCurrencies(): array
    {
        return [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'SAR' => 'Saudi Riyal (ر.س)',
            'AED' => 'UAE Dirham (د.إ)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
            'JPY' => 'Japanese Yen (¥)',
        ];
    }

    /**
     * Pricing model options
     */
    public static function getPricingModels(): array
    {
        return [
            'fixed' => 'Fixed Price',
            'per_unit' => 'Per Unit',
            'tiered' => 'Tiered Pricing',
            'usage_based' => 'Usage-based',
            'custom' => 'Custom',
        ];
    }

    /**
     * Pricing categories
     */
    public static function getCategories(): array
    {
        return [
            'messaging' => 'Messaging',
            'subscription' => 'Subscription',
            'setup' => 'Setup & Installation',
            'support' => 'Support & Maintenance',
            'add-ons' => 'Add-ons & Features',
            'integration' => 'Integration Services',
            'training' => 'Training & Consulting',
            'other' => 'Other',
        ];
    }

    /**
     * Unit types for per-unit pricing
     */
    public static function getUnitTypes(): array
    {
        return [
            'message' => 'Message',
            'user' => 'User',
            'api_call' => 'API Call',
            'transaction' => 'Transaction',
            'contact' => 'Contact',
            'document' => 'Document',
            'hour' => 'Hour',
            'month' => 'Month',
            'year' => 'Year',
            'custom' => 'Custom Unit',
        ];
    }

    /**
     * Get the product that owns this pricing item
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get billing cycle label
     */
    public function getBillingCycleLabelAttribute(): string
    {
        return static::getBillingCycles()[$this->billing_cycle] ?? $this->billing_cycle;
    }

    /**
     * Get currency label
     */
    public function getCurrencyLabelAttribute(): string
    {
        return static::getCurrencies()[$this->currency] ?? $this->currency;
    }

    /**
     * Get currency symbol
     */
    public function getCurrencySymbolAttribute(): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
        ];

        return $symbols[$this->currency] ?? $this->currency;
    }

    /**
     * Get formatted price with currency
     */
    public function getFormattedPriceAttribute(): string
    {
        if (is_null($this->price)) {
            return 'Contact for pricing';
        }

        $symbol = $this->currency_symbol;
        $price = number_format($this->price, 2);
        
        // For some currencies, symbol goes after the number
        $symbolAfter = ['SAR', 'AED'];
        
        if (in_array($this->currency, $symbolAfter)) {
            return "{$price} {$symbol}";
        }
        
        return "{$symbol}{$price}";
    }

    /**
     * Get formatted price with billing cycle
     */
    public function getFullPriceDisplayAttribute(): string
    {
        $price = $this->formatted_price;
        
        if ($this->billing_cycle === 'one-time') {
            return $price;
        }
        
        return "{$price} / {$this->billing_cycle_label}";
    }

    /**
     * Get features as array
     */
    public function getFeaturesArrayAttribute(): array
    {
        if (empty($this->features)) {
            return [];
        }

        // If features is already an array (JSON), return it
        if (is_array($this->features)) {
            return $this->features;
        }

        // If features is a string, split by newlines
        return array_filter(array_map('trim', explode("\n", $this->features)));
    }

    /**
     * Get default button text based on pricing
     */
    public function getDefaultButtonTextAttribute(): string
    {
        if (!empty($this->button_text)) {
            return $this->button_text;
        }

        if (is_null($this->price)) {
            return 'Contact Sales';
        }

        if ($this->billing_cycle === 'one-time') {
            return 'Purchase Now';
        }

        return 'Get Started';
    }

    /**
     * Check if this is a free tier
     */
    public function isFree(): bool
    {
        return $this->price === 0.00 || $this->price === null;
    }

    /**
     * Check if this is a contact-for-pricing tier
     */
    public function isContactForPricing(): bool
    {
        return $this->price === null;
    }

    /**
     * Get popular badge class
     */
    public function getPopularBadgeClassAttribute(): string
    {
        return $this->is_popular ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-800';
    }

    /**
     * Scope to get active pricing items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get popular pricing items
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to filter by billing cycle
     */
    public function scopeOfBillingCycle($query, string $cycle)
    {
        return $query->where('billing_cycle', $cycle);
    }

    /**
     * Scope to filter by currency
     */
    public function scopeOfCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Scope to search pricing items
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('features', 'like', "%{$search}%");
        });
    }

    /**
     * Get pricing model label
     */
    public function getPricingModelLabelAttribute(): string
    {
        return static::getPricingModels()[$this->pricing_model] ?? $this->pricing_model;
    }

    /**
     * Get category label
     */
    public function getCategoryLabelAttribute(): string
    {
        return static::getCategories()[$this->category] ?? $this->category;
    }

    /**
     * Get unit type label
     */
    public function getUnitTypeLabelAttribute(): string
    {
        return static::getUnitTypes()[$this->unit_type] ?? $this->unit_type;
    }

    /**
     * Get formatted price with unit information
     */
    public function getFormattedPriceWithUnitAttribute(): string
    {
        if (is_null($this->price)) {
            return 'Contact for pricing';
        }

        $price = $this->formatted_price;

        if ($this->pricing_model === 'per_unit' && $this->unit_label) {
            return "{$price} {$this->unit_label}";
        }

        return $price;
    }

    /**
     * Get full display price including setup fee
     */
    public function getFullDisplayPriceAttribute(): string
    {
        $price = $this->formatted_price_with_unit;

        if ($this->setup_fee && $this->setup_fee > 0) {
            $setupFee = $this->currency_symbol . number_format($this->setup_fee, 2);
            $price .= " + {$setupFee} setup fee";
        }

        if ($this->billing_cycle && $this->billing_cycle !== 'one-time' && $this->pricing_model !== 'per_unit') {
            $price .= " / {$this->billing_cycle_label}";
        }

        return $price;
    }

    /**
     * Check if this pricing item is suitable for quotations
     */
    public function getIsQuotationReadyAttribute(): bool
    {
        return $this->include_in_quotations &&
               $this->is_active &&
               ($this->price !== null || $this->pricing_model === 'custom');
    }

    /**
     * Get quotation display description
     */
    public function getQuotationDisplayDescriptionAttribute(): string
    {
        return $this->quotation_description ?: $this->description ?: $this->name;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign sort order if not provided
        static::creating(function ($pricingItem) {
            if (is_null($pricingItem->sort_order)) {
                $maxOrder = static::where('product_id', $pricingItem->product_id)->max('sort_order');
                $pricingItem->sort_order = ($maxOrder ?? 0) + 1;
            }
        });
    }
}
