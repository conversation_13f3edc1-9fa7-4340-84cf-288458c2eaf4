<?php

namespace Plugins\Products\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Support\Str;
use App\Models\User;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'target_audience',
        'use_cases',
        'reference_links',
        'terms_and_conditions',
        'service_level_agreement',
        'privacy_policy',
        'usage_policy',
        'quotation_notes',
        'implementation_notes',
        'default_contract_duration_months',
        'default_discount_percentage',
        'support_details',
        'warranty_details',
        'maintenance_details',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'reference_links' => 'array',
        'default_contract_duration_months' => 'integer',
        'default_discount_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
            $product->ensureUniqueSlug();
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
                $product->ensureUniqueSlug();
            }
        });
    }

    /**
     * Get available product icons
     */
    public static function getAvailableIcons(): array
    {
        return [
            // Communication & Messaging
            'fab fa-whatsapp' => 'WhatsApp Business',
            'fas fa-comments' => 'Chat/Messaging',
            'fas fa-robot' => 'Chatbot',
            'fas fa-headset' => 'Live Chat Support',
            'fas fa-brain' => 'AI Chatbot',
            'fas fa-phone' => 'Call Bot/Phone Services',
            'fas fa-microphone' => 'Voice Services',
            'fas fa-envelope' => 'Email Marketing',
            'fas fa-sms' => 'SMS Services',
            
            // Analytics & Reporting
            'fas fa-chart-line' => 'Analytics',
            'fas fa-chart-bar' => 'Reports',
            'fas fa-chart-pie' => 'Dashboard',
            'fas fa-tachometer-alt' => 'Performance Monitoring',
            'fas fa-search' => 'Search Analytics',
            
            // Automation & Workflow
            'fas fa-cogs' => 'Automation',
            'fas fa-magic' => 'Workflow Automation',
            'fas fa-sync' => 'Integration Services',
            'fas fa-tasks' => 'Task Management',
            'fas fa-calendar' => 'Scheduling',
            
            // Marketing & Sales
            'fas fa-bullhorn' => 'Marketing',
            'fas fa-funnel-dollar' => 'Sales Funnel',
            'fas fa-ad' => 'Advertising',
            'fas fa-users' => 'CRM',
            'fas fa-handshake' => 'Lead Management',
            
            // Security & Compliance
            'fas fa-shield-alt' => 'Security',
            'fas fa-lock' => 'Data Protection',
            'fas fa-user-shield' => 'Access Control',
            'fas fa-certificate' => 'Compliance',
            
            // Technical & Development
            'fas fa-code' => 'API Services',
            'fas fa-database' => 'Data Management',
            'fas fa-cloud' => 'Cloud Services',
            'fas fa-server' => 'Infrastructure',
            'fas fa-mobile-alt' => 'Mobile App',
            'fas fa-desktop' => 'Desktop Application',
            'fas fa-globe' => 'Web Platform',
            
            // General
            'fas fa-box' => 'General Product',
            'fas fa-star' => 'Premium Service',
            'fas fa-rocket' => 'Growth/Optimization',
            'fas fa-puzzle-piece' => 'Integration Services',
            'fas fa-tools' => 'Utilities',
            'fas fa-lightbulb' => 'Innovation',
        ];
    }

    /**
     * Get the user who created this product
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the businesses that have this product
     */
    public function businesses()
    {
        return $this->belongsToMany(\Plugins\Business\Models\Business::class, 'business_products')
                    ->withPivot([
                        'status', 'start_date', 'end_date', 'implementation_date',
                        'contract_value', 'renewal_date', 'product_version', 'notes',
                        'custom_features', 'assigned_by'
                    ])
                    ->withTimestamps();
    }

    /**
     * Get the documents for this product
     */
    public function documents()
    {
        return $this->hasMany(ProductDocument::class);
    }

    /**
     * Get current version documents only
     */
    public function currentDocuments()
    {
        return $this->hasMany(ProductDocument::class)->where('is_current_version', true);
    }

    /**
     * Get the releases for this product
     */
    public function releases()
    {
        return $this->hasMany(ProductRelease::class);
    }

    /**
     * Get published releases only
     */
    public function publishedReleases()
    {
        return $this->hasMany(ProductRelease::class)->where('is_published', true);
    }

    /**
     * Get the pricing items for this product
     */
    public function pricingItems()
    {
        return $this->hasMany(ProductPricingItem::class)->orderBy('sort_order');
    }

    /**
     * Get active pricing items only
     */
    public function activePricingItems()
    {
        return $this->hasMany(ProductPricingItem::class)
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    /**
     * Get icon label
     */
    public function getIconLabelAttribute(): string
    {
        return static::getAvailableIcons()[$this->icon] ?? 'General Product';
    }

    /**
     * Get latest release
     */
    public function getLatestReleaseAttribute()
    {
        return $this->publishedReleases()->latest('release_date')->first();
    }

    /**
     * Scope to get active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search products
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('target_audience', 'like', "%{$search}%")
              ->orWhere('use_cases', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to get products with business count
     */
    public function scopeWithBusinessCount($query)
    {
        return $query->withCount('businesses');
    }

    /**
     * Scope to get popular products
     */
    public function scopePopular($query, int $limit = 10)
    {
        return $query->withCount('businesses')
                    ->orderBy('businesses_count', 'desc')
                    ->limit($limit);
    }

    /**
     * Ensure unique slug
     */
    public function ensureUniqueSlug(): void
    {
        $originalSlug = $this->slug;
        $counter = 1;

        while (static::where('slug', $this->slug)->where('id', '!=', $this->id)->exists()) {
            $this->slug = $originalSlug . '-' . $counter;
            $counter++;
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(): array
    {
        return [
            'total_businesses' => $this->businesses()->count(),
            'active_assignments' => $this->businesses()->wherePivot('status', 'active')->count(),
            'total_documents' => $this->documents()->count(),
            'current_documents' => $this->currentDocuments()->count(),
            'total_releases' => $this->releases()->count(),
            'published_releases' => $this->publishedReleases()->count(),
            'pricing_items' => $this->pricingItems()->count(),
        ];
    }

    /**
     * Check if product has any documents
     */
    public function hasDocuments(): bool
    {
        return $this->documents()->exists();
    }

    /**
     * Check if product has any releases
     */
    public function hasReleases(): bool
    {
        return $this->releases()->exists();
    }

    /**
     * Check if product has pricing
     */
    public function hasPricing(): bool
    {
        return $this->pricingItems()->exists();
    }
}
