<?php

namespace Plugins\Products\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class ProductDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'category',
        'name',
        'description',
        'file_name',
        'original_name',
        'file_path',
        'mime_type',
        'file_size',
        'version',
        'is_current_version',
        'uploaded_by',
        'upload_date',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_current_version' => 'boolean',
        'upload_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Document category labels
     */
    public static function getCategories(): array
    {
        return [
            'manual' => 'Manual',
            'brochure' => 'Brochure',
            'policies' => 'Policies',
            'other' => 'Other',
        ];
    }

    /**
     * Get the product that owns this document
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who uploaded this document
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get all versions of this document
     */
    public function allVersions()
    {
        return static::where('product_id', $this->product_id)
                    ->where('name', $this->name)
                    ->where('category', $this->category)
                    ->orderBy('version', 'desc');
    }

    /**
     * Get previous versions of this document
     */
    public function previousVersions()
    {
        return $this->allVersions()->where('id', '!=', $this->id);
    }

    /**
     * Get category label
     */
    public function getCategoryLabelAttribute(): string
    {
        return static::getCategories()[$this->category] ?? $this->category;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file exists
     */
    public function fileExists(): bool
    {
        return Storage::disk('local')->exists($this->file_path);
    }

    /**
     * Get file URL for download
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('products.documents.download', [
            'product' => $this->product_id,
            'document' => $this->id
        ]);
    }

    /**
     * Get file URL for viewing
     */
    public function getViewUrlAttribute(): string
    {
        return route('products.documents.view', [
            'product' => $this->product_id,
            'document' => $this->id
        ]);
    }

    /**
     * Check if document is viewable in browser
     */
    public function isViewableInBrowser(): bool
    {
        $viewableMimeTypes = [
            'application/pdf',
            'text/plain',
            'text/html',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
        ];

        return in_array($this->mime_type, $viewableMimeTypes);
    }

    /**
     * Get file extension
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->original_name, PATHINFO_EXTENSION);
    }

    /**
     * Get icon class based on file type
     */
    public function getFileIconAttribute(): string
    {
        $extension = strtolower($this->file_extension);
        
        $iconMap = [
            'pdf' => 'fas fa-file-pdf text-red-500',
            'doc' => 'fas fa-file-word text-blue-500',
            'docx' => 'fas fa-file-word text-blue-500',
            'xls' => 'fas fa-file-excel text-green-500',
            'xlsx' => 'fas fa-file-excel text-green-500',
            'ppt' => 'fas fa-file-powerpoint text-orange-500',
            'pptx' => 'fas fa-file-powerpoint text-orange-500',
            'txt' => 'fas fa-file-alt text-gray-500',
            'jpg' => 'fas fa-file-image text-purple-500',
            'jpeg' => 'fas fa-file-image text-purple-500',
            'png' => 'fas fa-file-image text-purple-500',
            'gif' => 'fas fa-file-image text-purple-500',
            'zip' => 'fas fa-file-archive text-yellow-500',
            'rar' => 'fas fa-file-archive text-yellow-500',
            '7z' => 'fas fa-file-archive text-yellow-500',
        ];

        return $iconMap[$extension] ?? 'fas fa-file text-gray-400';
    }

    /**
     * Create new version of this document
     */
    public function createNewVersion(array $fileData, User $uploader): static
    {
        // Mark current version as not current
        $this->allVersions()->update(['is_current_version' => false]);

        // Generate new version number
        $latestVersion = $this->allVersions()->max('version');
        $newVersion = $this->incrementVersion($latestVersion);

        // Create new version
        return static::create([
            'product_id' => $this->product_id,
            'category' => $this->category,
            'name' => $this->name,
            'description' => $fileData['description'] ?? $this->description,
            'file_name' => $fileData['file_name'],
            'original_name' => $fileData['original_name'],
            'file_path' => $fileData['file_path'],
            'mime_type' => $fileData['mime_type'],
            'file_size' => $fileData['file_size'],
            'version' => $newVersion,
            'is_current_version' => true,
            'uploaded_by' => $uploader->id,
            'upload_date' => now(),
        ]);
    }

    /**
     * Increment version number
     */
    private function incrementVersion(string $version): string
    {
        $parts = explode('.', $version);
        
        if (count($parts) === 1) {
            // Simple integer version
            return (string) ((int) $parts[0] + 1);
        } elseif (count($parts) === 2) {
            // Major.Minor
            $parts[1] = (string) ((int) $parts[1] + 1);
            return implode('.', $parts);
        } else {
            // Major.Minor.Patch
            $parts[2] = (string) ((int) $parts[2] + 1);
            return implode('.', $parts);
        }
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($document) {
            if ($document->fileExists()) {
                Storage::disk('local')->delete($document->file_path);
            }
        });
    }

    /**
     * Scope to filter by category
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get current versions only
     */
    public function scopeCurrentVersions($query)
    {
        return $query->where('is_current_version', true);
    }

    /**
     * Scope to get recent documents
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('upload_date', '>=', now()->subDays($days));
    }

    /**
     * Scope to search documents
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('original_name', 'like', "%{$search}%");
        });
    }
}
