<?php

namespace Plugins\Products\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class ProductRelease extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'version',
        'title',
        'description',
        'changelog',
        'release_date',
        'is_published',
        'release_type',
        'features',
        'bug_fixes',
        'breaking_changes',
        'created_by',
        'published_at',
    ];

    protected $casts = [
        'release_date' => 'date',
        'is_published' => 'boolean',
        'features' => 'array',
        'bug_fixes' => 'array',
        'breaking_changes' => 'array',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Release type labels
     */
    public static function getReleaseTypes(): array
    {
        return [
            'major' => 'Major Release',
            'minor' => 'Minor Release',
            'patch' => 'Patch Release',
            'hotfix' => 'Hotfix',
        ];
    }

    /**
     * Get the product that owns this release
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who created this release
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get release type label
     */
    public function getReleaseTypeLabelAttribute(): string
    {
        return static::getReleaseTypes()[$this->release_type] ?? $this->release_type;
    }

    /**
     * Get release type badge class
     */
    public function getReleaseTypeBadgeClassAttribute(): string
    {
        $classes = [
            'major' => 'bg-red-100 text-red-800',
            'minor' => 'bg-blue-100 text-blue-800',
            'patch' => 'bg-green-100 text-green-800',
            'hotfix' => 'bg-yellow-100 text-yellow-800',
        ];

        return $classes[$this->release_type] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Check if release is published
     */
    public function isPublished(): bool
    {
        return $this->is_published;
    }

    /**
     * Check if release is draft
     */
    public function isDraft(): bool
    {
        return !$this->is_published;
    }

    /**
     * Publish the release
     */
    public function publish(): bool
    {
        $this->is_published = true;
        $this->published_at = now();
        return $this->save();
    }

    /**
     * Unpublish the release
     */
    public function unpublish(): bool
    {
        $this->is_published = false;
        $this->published_at = null;
        return $this->save();
    }

    /**
     * Get formatted version for display
     */
    public function getFormattedVersionAttribute(): string
    {
        return "v{$this->version}";
    }

    /**
     * Get short description (first 150 characters)
     */
    public function getShortDescriptionAttribute(): string
    {
        if (empty($this->description)) {
            return '';
        }

        return strlen($this->description) > 150 
            ? substr($this->description, 0, 150) . '...'
            : $this->description;
    }

    /**
     * Check if release has features
     */
    public function hasFeatures(): bool
    {
        return !empty($this->features) && is_array($this->features) && count($this->features) > 0;
    }

    /**
     * Check if release has bug fixes
     */
    public function hasBugFixes(): bool
    {
        return !empty($this->bug_fixes) && is_array($this->bug_fixes) && count($this->bug_fixes) > 0;
    }

    /**
     * Check if release has breaking changes
     */
    public function hasBreakingChanges(): bool
    {
        return !empty($this->breaking_changes) && is_array($this->breaking_changes) && count($this->breaking_changes) > 0;
    }

    /**
     * Get total changes count
     */
    public function getTotalChangesCountAttribute(): int
    {
        $count = 0;
        
        if ($this->hasFeatures()) {
            $count += count($this->features);
        }
        
        if ($this->hasBugFixes()) {
            $count += count($this->bug_fixes);
        }
        
        if ($this->hasBreakingChanges()) {
            $count += count($this->breaking_changes);
        }
        
        return $count;
    }

    /**
     * Get next version suggestion based on release type
     */
    public static function suggestNextVersion(Product $product, string $releaseType): string
    {
        $latestRelease = $product->releases()->orderBy('release_date', 'desc')->first();
        
        if (!$latestRelease) {
            return '1.0.0';
        }

        $version = $latestRelease->version;
        $parts = explode('.', $version);
        
        // Ensure we have at least 3 parts (major.minor.patch)
        while (count($parts) < 3) {
            $parts[] = '0';
        }

        switch ($releaseType) {
            case 'major':
                $parts[0] = (string) ((int) $parts[0] + 1);
                $parts[1] = '0';
                $parts[2] = '0';
                break;
            case 'minor':
                $parts[1] = (string) ((int) $parts[1] + 1);
                $parts[2] = '0';
                break;
            case 'patch':
            case 'hotfix':
                $parts[2] = (string) ((int) $parts[2] + 1);
                break;
        }

        return implode('.', $parts);
    }

    /**
     * Scope to get published releases
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get draft releases
     */
    public function scopeDraft($query)
    {
        return $query->where('is_published', false);
    }

    /**
     * Scope to filter by release type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('release_type', $type);
    }

    /**
     * Scope to get recent releases
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('release_date', '>=', now()->subDays($days));
    }

    /**
     * Scope to search releases
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('version', 'like', "%{$search}%")
              ->orWhere('changelog', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to order by version (semantic versioning)
     * Compatible with both MySQL and SQLite
     */
    public function scopeOrderByVersion($query, string $direction = 'desc')
    {
        // Get all releases and sort them in PHP for database compatibility
        $releases = $query->get();

        $sorted = $releases->sort(function ($a, $b) use ($direction) {
            $versionA = $this->parseVersion($a->version);
            $versionB = $this->parseVersion($b->version);

            // Compare major version
            if ($versionA['major'] !== $versionB['major']) {
                $result = $versionA['major'] <=> $versionB['major'];
                return $direction === 'desc' ? -$result : $result;
            }

            // Compare minor version
            if ($versionA['minor'] !== $versionB['minor']) {
                $result = $versionA['minor'] <=> $versionB['minor'];
                return $direction === 'desc' ? -$result : $result;
            }

            // Compare patch version
            $result = $versionA['patch'] <=> $versionB['patch'];
            return $direction === 'desc' ? -$result : $result;
        });

        // Return a new query with the sorted IDs
        $sortedIds = $sorted->pluck('id')->toArray();

        if (empty($sortedIds)) {
            return $query->whereRaw('1 = 0'); // Return empty result
        }

        // Use FIELD() for MySQL or custom ordering for SQLite
        $driver = $query->getConnection()->getDriverName();

        if ($driver === 'mysql') {
            return $query->orderByRaw('FIELD(id, ' . implode(',', $sortedIds) . ')');
        } else {
            // For SQLite and other databases, use CASE WHEN ordering
            $orderCases = [];
            foreach ($sortedIds as $index => $id) {
                $orderCases[] = "WHEN id = {$id} THEN {$index}";
            }
            $orderBy = 'CASE ' . implode(' ', $orderCases) . ' END';
            return $query->orderByRaw($orderBy);
        }
    }

    /**
     * Parse version string into components
     */
    private function parseVersion(string $version): array
    {
        $parts = explode('.', $version);

        return [
            'major' => (int) ($parts[0] ?? 0),
            'minor' => (int) ($parts[1] ?? 0),
            'patch' => (int) ($parts[2] ?? 0),
        ];
    }
}
