<?php

namespace Plugins\Products\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Products\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    public function __construct()
    {
        // Apply permission middleware
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user) {
                abort(403, 'Authentication required.');
            }

            // Check permissions based on the action
            $action = $request->route()->getActionMethod();
            
            switch ($action) {
                case 'index':
                case 'show':
                case 'allProducts':
                case 'api':
                    if (!$user->hasPermission('view_products')) {
                        abort(403, 'Access denied. You do not have permission to view products.');
                    }
                    break;
                case 'create':
                case 'store':
                case 'edit':
                case 'update':
                case 'destroy':
                    if (!$user->hasPermission('manage_products')) {
                        abort(403, 'Access denied. You do not have permission to manage products.');
                    }
                    break;
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of products
     */
    public function index(Request $request): View
    {
        $query = Product::with(['creator', 'businesses'])
                       ->withCount(['businesses', 'documents', 'releases', 'pricingItems']);

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Sort options
        $sortBy = $request->get('sort', 'name');
        $sortDirection = $request->get('direction', 'asc');

        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortDirection);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortDirection);
                break;
            case 'businesses_count':
                $query->orderBy('businesses_count', $sortDirection);
                break;
            default:
                $query->orderBy('name', 'asc');
        }

        $products = $query->paginate(15)->withQueryString();

        // Get statistics
        $stats = [
            'total' => Product::count(),
            'active' => Product::active()->count(),
            'inactive' => Product::where('is_active', false)->count(),
            'with_businesses' => Product::has('businesses')->count(),
        ];

        return view('plugins.products::index', compact('products', 'stats'));
    }

    /**
     * Show the form for creating a new product
     */
    public function create(): View
    {
        $icons = Product::getAvailableIcons();
        
        return view('plugins.products::create', compact('icons'));
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'target_audience' => 'nullable|string',
            'use_cases' => 'nullable|string',
            'reference_links' => 'nullable|array',
            'reference_links.*' => 'url',
            'is_active' => 'boolean',
        ]);

        $product = Product::create([
            'name' => $request->name,
            'slug' => $request->slug ?: Str::slug($request->name),
            'description' => $request->description,
            'icon' => $request->icon,
            'target_audience' => $request->target_audience,
            'use_cases' => $request->use_cases,
            'reference_links' => array_filter($request->reference_links ?? []),
            'is_active' => $request->boolean('is_active', true),
            'created_by' => Auth::id(),
        ]);

        return redirect()->route('products.show', $product)
                        ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product
     */
    public function show(Product $product): View
    {
        $product->load([
            'creator',
            'businesses' => function ($query) {
                $query->withPivot(['status', 'start_date', 'end_date', 'assigned_by'])
                      ->with('creator');
            },
            'currentDocuments.uploader',
            'publishedReleases' => function ($query) {
                $query->latest('release_date')->limit(5);
            },
            'activePricingItems'
        ]);

        $stats = $product->getUsageStats();

        return view('plugins.products::show', compact('product', 'stats'));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product): View
    {
        $icons = Product::getAvailableIcons();
        
        return view('plugins.products::edit', compact('product', 'icons'));
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, Product $product): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug,' . $product->id,
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'target_audience' => 'nullable|string',
            'use_cases' => 'nullable|string',
            'reference_links' => 'nullable|array',
            'reference_links.*' => 'url',
            'is_active' => 'boolean',
        ]);

        $product->update([
            'name' => $request->name,
            'slug' => $request->slug ?: Str::slug($request->name),
            'description' => $request->description,
            'icon' => $request->icon,
            'target_audience' => $request->target_audience,
            'use_cases' => $request->use_cases,
            'reference_links' => array_filter($request->reference_links ?? []),
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('products.show', $product)
                        ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product
     */
    public function destroy(Product $product): RedirectResponse
    {
        // Check if product is assigned to any businesses
        if ($product->businesses()->exists()) {
            return redirect()->route('products.show', $product)
                           ->with('error', 'Cannot delete product that is assigned to businesses. Please remove all business assignments first.');
        }

        $product->delete();

        return redirect()->route('products.index')
                        ->with('success', 'Product deleted successfully.');
    }

    /**
     * Search products (AJAX endpoint)
     */
    public function search(Request $request)
    {
        $query = Product::active();

        if ($request->filled('q')) {
            $query->search($request->q);
        }

        $products = $query->limit(10)->get(['id', 'name', 'icon', 'description']);

        return response()->json($products);
    }

    /**
     * Get all products for business integration
     */
    public function allProducts(Request $request): View
    {
        $products = Product::active()
                          ->withCount('businesses')
                          ->orderBy('name')
                          ->get();

        return view('plugins.products::all-products', compact('products'));
    }



    /**
     * API endpoint for products
     */
    public function api(Request $request)
    {
        $query = Product::active()->with(['pricingItems' => function ($q) {
            $q->active()->ordered();
        }]);

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $products = $query->get();

        return response()->json([
            'products' => $products,
            'total' => $products->count(),
        ]);
    }
}
