<?php

namespace Plugins\Products\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Products\Models\Product;
use Plugins\Products\Models\ProductRelease;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class ProductReleaseController extends Controller
{
    public function __construct()
    {
        // Apply permission middleware
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user) {
                abort(403, 'Authentication required.');
            }

            // Check permissions based on the action
            $action = $request->route()->getActionMethod();
            
            switch ($action) {
                case 'index':
                case 'show':
                    if (!$user->hasPermission('view_product_releases')) {
                        abort(403, 'Access denied. You do not have permission to view product releases.');
                    }
                    break;
                case 'create':
                case 'store':
                case 'edit':
                case 'update':
                case 'destroy':
                case 'publish':
                case 'unpublish':
                    if (!$user->hasPermission('manage_product_releases')) {
                        abort(403, 'Access denied. You do not have permission to manage product releases.');
                    }
                    break;
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of product releases
     */
    public function index(Product $product, Request $request): View
    {
        $query = $product->releases()->with('creator');

        // Filter by publication status
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->draft();
            }
        }

        // Filter by release type
        if ($request->filled('type')) {
            $query->ofType($request->type);
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Sort by version (semantic versioning)
        $query->orderByVersion('desc');

        $releases = $query->paginate(15)->withQueryString();

        $releaseTypes = ProductRelease::getReleaseTypes();

        // Get statistics
        $stats = [
            'total' => $product->releases()->count(),
            'published' => $product->publishedReleases()->count(),
            'draft' => $product->releases()->draft()->count(),
            'by_type' => $product->releases()
                               ->selectRaw('release_type, COUNT(*) as count')
                               ->groupBy('release_type')
                               ->pluck('count', 'release_type')
                               ->toArray(),
        ];

        return view('plugins.products::releases.index', compact('product', 'releases', 'releaseTypes', 'stats'));
    }

    /**
     * Show the form for creating a new release
     */
    public function create(Product $product, Request $request): View
    {
        $releaseTypes = ProductRelease::getReleaseTypes();
        
        // Suggest next version based on release type
        $suggestedVersion = '';
        if ($request->filled('type')) {
            $suggestedVersion = ProductRelease::suggestNextVersion($product, $request->type);
        }
        
        return view('plugins.products::releases.create', compact('product', 'releaseTypes', 'suggestedVersion'));
    }

    /**
     * Store a newly created release
     */
    public function store(Request $request, Product $product): RedirectResponse
    {
        $request->validate([
            'version' => 'required|string|max:50|unique:product_releases,version,NULL,id,product_id,' . $product->id,
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'changelog' => 'nullable|string',
            'release_date' => 'required|date',
            'release_type' => 'required|in:major,minor,patch,hotfix',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'bug_fixes' => 'nullable|array',
            'bug_fixes.*' => 'string',
            'breaking_changes' => 'nullable|array',
            'breaking_changes.*' => 'string',
            'is_published' => 'boolean',
        ]);

        $release = ProductRelease::create([
            'product_id' => $product->id,
            'version' => $request->version,
            'title' => $request->title,
            'description' => $request->description,
            'changelog' => $request->changelog,
            'release_date' => $request->release_date,
            'release_type' => $request->release_type,
            'features' => array_filter($request->features ?? []),
            'bug_fixes' => array_filter($request->bug_fixes ?? []),
            'breaking_changes' => array_filter($request->breaking_changes ?? []),
            'is_published' => $request->boolean('is_published', false),
            'created_by' => Auth::id(),
            'published_at' => $request->boolean('is_published', false) ? now() : null,
        ]);

        return redirect()->route('products.releases.show', [$product, $release])
                        ->with('success', 'Release created successfully.');
    }

    /**
     * Display the specified release
     */
    public function show(Product $product, ProductRelease $release): View
    {
        $release->load(['creator', 'product']);

        return view('plugins.products::releases.show', compact('product', 'release'));
    }

    /**
     * Show the form for editing the specified release
     */
    public function edit(Product $product, ProductRelease $release): View
    {
        $releaseTypes = ProductRelease::getReleaseTypes();
        
        return view('plugins.products::releases.edit', compact('product', 'release', 'releaseTypes'));
    }

    /**
     * Update the specified release
     */
    public function update(Request $request, Product $product, ProductRelease $release): RedirectResponse
    {
        $request->validate([
            'version' => 'required|string|max:50|unique:product_releases,version,' . $release->id . ',id,product_id,' . $product->id,
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'changelog' => 'nullable|string',
            'release_date' => 'required|date',
            'release_type' => 'required|in:major,minor,patch,hotfix',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'bug_fixes' => 'nullable|array',
            'bug_fixes.*' => 'string',
            'breaking_changes' => 'nullable|array',
            'breaking_changes.*' => 'string',
        ]);

        $release->update([
            'version' => $request->version,
            'title' => $request->title,
            'description' => $request->description,
            'changelog' => $request->changelog,
            'release_date' => $request->release_date,
            'release_type' => $request->release_type,
            'features' => array_filter($request->features ?? []),
            'bug_fixes' => array_filter($request->bug_fixes ?? []),
            'breaking_changes' => array_filter($request->breaking_changes ?? []),
        ]);

        return redirect()->route('products.releases.show', [$product, $release])
                        ->with('success', 'Release updated successfully.');
    }

    /**
     * Remove the specified release
     */
    public function destroy(Product $product, ProductRelease $release): RedirectResponse
    {
        $release->delete();

        return redirect()->route('products.releases.index', $product)
                        ->with('success', 'Release deleted successfully.');
    }

    /**
     * Publish the specified release
     */
    public function publish(Product $product, ProductRelease $release): RedirectResponse
    {
        if ($release->isPublished()) {
            return redirect()->route('products.releases.show', [$product, $release])
                           ->with('warning', 'Release is already published.');
        }

        $release->publish();

        return redirect()->route('products.releases.show', [$product, $release])
                        ->with('success', 'Release published successfully.');
    }

    /**
     * Unpublish the specified release
     */
    public function unpublish(Product $product, ProductRelease $release): RedirectResponse
    {
        if ($release->isDraft()) {
            return redirect()->route('products.releases.show', [$product, $release])
                           ->with('warning', 'Release is already unpublished.');
        }

        $release->unpublish();

        return redirect()->route('products.releases.show', [$product, $release])
                        ->with('success', 'Release unpublished successfully.');
    }
}
