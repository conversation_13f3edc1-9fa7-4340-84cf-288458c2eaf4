<?php

namespace Plugins\Products\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Products\Models\Product;
use Plugins\Products\Models\ProductDocument;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductDocumentController extends Controller
{
    public function __construct()
    {
        // Apply permission middleware
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user) {
                abort(403, 'Authentication required.');
            }

            // Check permissions based on the action
            $action = $request->route()->getActionMethod();
            
            switch ($action) {
                case 'index':
                case 'show':
                case 'view':
                case 'download':
                case 'downloadVersion':
                case 'versions':
                    if (!$user->hasPermission('view_product_documents')) {
                        abort(403, 'Access denied. You do not have permission to view product documents.');
                    }
                    break;
                case 'create':
                case 'store':
                case 'edit':
                case 'update':
                case 'destroy':
                case 'newVersion':
                    if (!$user->hasPermission('manage_product_documents')) {
                        abort(403, 'Access denied. You do not have permission to manage product documents.');
                    }
                    break;
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of product documents
     */
    public function index(Product $product, Request $request): View
    {
        $query = $product->documents()->with('uploader');

        // Filter by category
        if ($request->filled('category')) {
            $query->ofCategory($request->category);
        }

        // Filter by current versions only
        if ($request->boolean('current_only', true)) {
            $query->currentVersions();
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Sort options
        $sortBy = $request->get('sort', 'upload_date');
        $sortDirection = $request->get('direction', 'desc');

        $query->orderBy($sortBy, $sortDirection);

        $documents = $query->paginate(15)->withQueryString();

        $categories = ProductDocument::getCategories();

        // Get statistics
        $stats = [
            'total' => $product->documents()->count(),
            'current_versions' => $product->currentDocuments()->count(),
            'by_category' => $product->documents()
                                   ->selectRaw('category, COUNT(*) as count')
                                   ->groupBy('category')
                                   ->pluck('count', 'category')
                                   ->toArray(),
        ];

        return view('plugins.products::documents.index', compact('product', 'documents', 'categories', 'stats'));
    }

    /**
     * Show the form for creating a new document
     */
    public function create(Product $product): View
    {
        $categories = ProductDocument::getCategories();
        
        return view('plugins.products::documents.create', compact('product', 'categories'));
    }

    /**
     * Store a newly created document
     */
    public function store(Request $request, Product $product): RedirectResponse
    {
        $request->validate([
            'category' => 'required|in:manual,brochure,policies,other',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'required|file|max:10240', // 10MB max
            'version' => 'nullable|string|max:20',
        ]);

        $file = $request->file('file');
        $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $filePath = "products/{$product->id}/documents/{$fileName}";

        // Store the file
        $file->storeAs('products/' . $product->id . '/documents', $fileName, 'local');

        // Create document record
        $document = ProductDocument::create([
            'product_id' => $product->id,
            'category' => $request->category,
            'name' => $request->name,
            'description' => $request->description,
            'file_name' => $fileName,
            'original_name' => $file->getClientOriginalName(),
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'version' => $request->version ?: '1.0',
            'is_current_version' => true,
            'uploaded_by' => Auth::id(),
            'upload_date' => now(),
        ]);

        return redirect()->route('products.documents.index', $product)
                        ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Display the specified document
     */
    public function show(Product $product, ProductDocument $document): View
    {
        $document->load(['uploader', 'product']);
        
        // Get all versions of this document
        $versions = $document->allVersions()->with('uploader')->get();

        return view('plugins.products::documents.show', compact('product', 'document', 'versions'));
    }

    /**
     * Show the form for editing the specified document
     */
    public function edit(Product $product, ProductDocument $document): View
    {
        $categories = ProductDocument::getCategories();
        
        return view('plugins.products::documents.edit', compact('product', 'document', 'categories'));
    }

    /**
     * Update the specified document
     */
    public function update(Request $request, Product $product, ProductDocument $document): RedirectResponse
    {
        $request->validate([
            'category' => 'required|in:manual,brochure,policies,other',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'nullable|string|max:20',
        ]);

        $document->update([
            'category' => $request->category,
            'name' => $request->name,
            'description' => $request->description,
            'version' => $request->version ?: $document->version,
        ]);

        return redirect()->route('products.documents.show', [$product, $document])
                        ->with('success', 'Document updated successfully.');
    }

    /**
     * Remove the specified document
     */
    public function destroy(Product $product, ProductDocument $document): RedirectResponse
    {
        // Delete the file from storage
        if ($document->fileExists()) {
            Storage::disk('local')->delete($document->file_path);
        }

        $document->delete();

        return redirect()->route('products.documents.index', $product)
                        ->with('success', 'Document deleted successfully.');
    }

    /**
     * Download the specified document
     */
    public function download(Product $product, ProductDocument $document)
    {
        if (!$document->fileExists()) {
            abort(404, 'File not found.');
        }

        return response()->download(storage_path('app/' . $document->file_path), $document->original_name);
    }

    /**
     * View the specified document in browser
     */
    public function view(Product $product, ProductDocument $document)
    {
        if (!$document->fileExists()) {
            abort(404, 'File not found.');
        }

        if (!$document->isViewableInBrowser()) {
            return redirect()->route('products.documents.download', [$product, $document]);
        }

        $fileContent = Storage::disk('local')->get($document->file_path);
        
        return response($fileContent)
            ->header('Content-Type', $document->mime_type)
            ->header('Content-Disposition', 'inline; filename="' . $document->original_name . '"');
    }

    /**
     * Create a new version of the document
     */
    public function newVersion(Request $request, Product $product, ProductDocument $document): RedirectResponse
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'description' => 'nullable|string',
            'version' => 'nullable|string|max:20',
        ]);

        $file = $request->file('file');
        $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $filePath = "products/{$product->id}/documents/{$fileName}";

        // Store the new file
        $file->storeAs('products/' . $product->id . '/documents', $fileName, 'local');

        // Create new version
        $newDocument = $document->createNewVersion([
            'file_name' => $fileName,
            'original_name' => $file->getClientOriginalName(),
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'description' => $request->description ?: $document->description,
        ], Auth::user());

        return redirect()->route('products.documents.show', [$product, $newDocument])
                        ->with('success', 'New document version uploaded successfully.');
    }

    /**
     * Show all versions of a document
     */
    public function versions(Product $product, ProductDocument $document): View
    {
        $versions = $document->allVersions()->with('uploader')->get();

        return view('plugins.products::documents.versions', compact('product', 'document', 'versions'));
    }

    /**
     * Download a specific version of the document
     */
    public function downloadVersion(Product $product, ProductDocument $document, string $version)
    {
        $versionDocument = $document->allVersions()->where('version', $version)->first();

        if (!$versionDocument || !$versionDocument->fileExists()) {
            abort(404, 'File version not found.');
        }

        return response()->download(storage_path('app/' . $versionDocument->file_path), $versionDocument->original_name);
    }
}
