<?php

namespace Plugins\Products\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Products\Models\Product;
use Plugins\Products\Models\ProductPricingItem;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ProductPricingController extends Controller
{
    public function __construct()
    {
        // Apply permission middleware
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user) {
                abort(403, 'Authentication required.');
            }

            // Check permissions based on the action
            $action = $request->route()->getActionMethod();
            
            switch ($action) {
                case 'index':
                    if (!$user->hasPermission('view_products')) {
                        abort(403, 'Access denied. You do not have permission to view product pricing.');
                    }
                    break;
                case 'create':
                case 'store':
                case 'edit':
                case 'update':
                case 'destroy':
                    if (!$user->hasPermission('manage_products')) {
                        abort(403, 'Access denied. You do not have permission to manage product pricing.');
                    }
                    break;
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of product pricing items
     */
    public function index(Product $product, Request $request): View
    {
        $query = $product->pricingItems();

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by pricing model
        if ($request->filled('pricing_model')) {
            $query->where('pricing_model', $request->pricing_model);
        }

        // Filter by billing cycle
        if ($request->filled('billing_cycle')) {
            $query->ofBillingCycle($request->billing_cycle);
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $pricingItems = $query->ordered()->get();

        // Get all required data for the view
        $pricingModels = ProductPricingItem::getPricingModels();
        $billingCycles = ProductPricingItem::getBillingCycles();
        $currencies = ProductPricingItem::getCurrencies();

        // Get statistics
        $stats = [
            'total' => $product->pricingItems()->count(),
            'active' => $product->activePricingItems()->count(),
            'inactive' => $product->pricingItems()->where('is_active', false)->count(),
            'popular' => $product->pricingItems()->popular()->count(),
        ];

        return view('plugins.products::pricing.index', compact('product', 'pricingItems', 'pricingModels', 'billingCycles', 'currencies', 'stats'));
    }

    /**
     * Show the form for creating a new pricing item
     */
    public function create(Product $product): View
    {
        $pricingModels = ProductPricingItem::getPricingModels();
        $unitTypes = ProductPricingItem::getUnitTypes();
        $billingCycles = ProductPricingItem::getBillingCycles();
        $currencies = ProductPricingItem::getCurrencies();

        return view('plugins.products::pricing.create', compact('product', 'pricingModels', 'unitTypes', 'billingCycles', 'currencies'));
    }

    /**
     * Store a newly created pricing item
     */
    public function store(Request $request, Product $product): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'pricing_model' => 'required|in:fixed,per_unit,tiered,usage_based,custom',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'currency' => 'required|string|max:3',
            'unit_type' => 'nullable|string|max:255',
            'custom_unit_name' => 'nullable|string|max:255|required_if:unit_type,custom',
            'billing_cycle' => 'required|in:one-time,monthly,quarterly,yearly,per_consumption,custom',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_popular' => 'boolean',
            'include_in_quotations' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        ProductPricingItem::create([
            'product_id' => $product->id,
            'name' => $request->name,
            'pricing_model' => $request->pricing_model,
            'description' => $request->description,
            'price' => $request->price,
            'currency' => $request->currency,
            'unit_type' => $request->unit_type,
            'custom_unit_name' => $request->custom_unit_name,
            'billing_cycle' => $request->billing_cycle,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->sort_order,
            'is_popular' => $request->boolean('is_popular', false),
            'include_in_quotations' => $request->boolean('include_in_quotations', true),
            'notes' => $request->notes,
        ]);

        return redirect()->route('products.pricing.index', $product)
                        ->with('success', 'Pricing item created successfully.');
    }

    /**
     * Show the form for editing the specified pricing item
     */
    public function edit(Product $product, ProductPricingItem $pricing): View
    {
        $pricingModels = ProductPricingItem::getPricingModels();
        $unitTypes = ProductPricingItem::getUnitTypes();
        $billingCycles = ProductPricingItem::getBillingCycles();
        $currencies = ProductPricingItem::getCurrencies();

        return view('plugins.products::pricing.edit', compact('product', 'pricing', 'pricingModels', 'unitTypes', 'billingCycles', 'currencies'));
    }

    /**
     * Update the specified pricing item
     */
    public function update(Request $request, Product $product, ProductPricingItem $pricing): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'pricing_model' => 'required|in:fixed,per_unit,tiered,usage_based,custom',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'currency' => 'required|string|max:3',
            'unit_type' => 'nullable|string|max:255',
            'custom_unit_name' => 'nullable|string|max:255|required_if:unit_type,custom',
            'billing_cycle' => 'required|in:one-time,monthly,quarterly,yearly,per_consumption,custom',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_popular' => 'boolean',
            'include_in_quotations' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $pricing->update([
            'name' => $request->name,
            'pricing_model' => $request->pricing_model,
            'description' => $request->description,
            'price' => $request->price,
            'currency' => $request->currency,
            'unit_type' => $request->unit_type,
            'custom_unit_name' => $request->custom_unit_name,
            'billing_cycle' => $request->billing_cycle,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->sort_order,
            'is_popular' => $request->boolean('is_popular', false),
            'include_in_quotations' => $request->boolean('include_in_quotations', true),
            'notes' => $request->notes,
        ]);

        return redirect()->route('products.pricing.index', $product)
                        ->with('success', 'Pricing item updated successfully.');
    }

    /**
     * Remove the specified pricing item
     */
    public function destroy(Product $product, ProductPricingItem $pricing): RedirectResponse
    {
        $pricing->delete();

        return redirect()->route('products.pricing.index', $product)
                        ->with('success', 'Pricing item deleted successfully.');
    }
}
