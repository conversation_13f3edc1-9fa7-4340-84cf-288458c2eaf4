<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if products table already exists (from business plugin)
        if (Schema::hasTable('products')) {
            // If it exists, we need to modify it to match our new structure
            Schema::table('products', function (Blueprint $table) {
                // Add description column if it doesn't exist (was removed by simplify migration)
                if (!Schema::hasColumn('products', 'description')) {
                    $table->text('description')->nullable()->after('slug');
                }
                // Add new columns if they don't exist
                if (!Schema::hasColumn('products', 'target_audience')) {
                    $table->text('target_audience')->nullable()->after('description');
                }
                if (!Schema::hasColumn('products', 'use_cases')) {
                    $table->text('use_cases')->nullable()->after('target_audience');
                }
                if (!Schema::hasColumn('products', 'reference_links')) {
                    $table->json('reference_links')->nullable()->after('use_cases');
                }
            });
            return;
        }

        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('icon')->nullable(); // Font Awesome icon class or image path
            $table->text('target_audience')->nullable();
            $table->text('use_cases')->nullable();
            $table->json('reference_links')->nullable(); // Array of reference URLs
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active']);
            $table->index(['created_by']);
            $table->index(['name']);
            $table->index(['slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
