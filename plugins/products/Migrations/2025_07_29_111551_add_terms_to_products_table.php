<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add terms and conditions fields
            $table->longText('terms_and_conditions')->nullable()->after('description');
            $table->longText('service_level_agreement')->nullable()->after('terms_and_conditions');
            $table->longText('privacy_policy')->nullable()->after('service_level_agreement');
            $table->longText('usage_policy')->nullable()->after('privacy_policy');

            // Add quotation-specific fields
            $table->text('quotation_notes')->nullable()->after('usage_policy');
            $table->text('implementation_notes')->nullable()->after('quotation_notes');
            $table->integer('default_contract_duration_months')->nullable()->after('implementation_notes');
            $table->decimal('default_discount_percentage', 5, 2)->nullable()->after('default_contract_duration_months');

            // Add support and warranty information
            $table->text('support_details')->nullable()->after('default_discount_percentage');
            $table->text('warranty_details')->nullable()->after('support_details');
            $table->text('maintenance_details')->nullable()->after('warranty_details');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'terms_and_conditions',
                'service_level_agreement',
                'privacy_policy',
                'usage_policy',
                'quotation_notes',
                'implementation_notes',
                'default_contract_duration_months',
                'default_discount_percentage',
                'support_details',
                'warranty_details',
                'maintenance_details'
            ]);
        });
    }
};
