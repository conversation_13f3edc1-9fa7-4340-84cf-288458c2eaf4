<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->enum('category', ['manual', 'brochure', 'policies', 'other'])->default('other');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('file_name'); // Stored file name
            $table->string('original_name'); // Original uploaded file name
            $table->string('file_path'); // Storage path
            $table->string('mime_type');
            $table->unsignedBigInteger('file_size'); // File size in bytes
            $table->string('version', 20)->default('1.0'); // Version number
            $table->boolean('is_current_version')->default(true);
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('upload_date');
            $table->timestamps();

            // Indexes for performance
            $table->index(['product_id']);
            $table->index(['category']);
            $table->index(['uploaded_by']);
            $table->index(['upload_date']);
            $table->index(['version']);
            $table->index(['is_current_version']);
            
            // Composite index for finding current versions
            $table->index(['product_id', 'is_current_version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_documents');
    }
};
