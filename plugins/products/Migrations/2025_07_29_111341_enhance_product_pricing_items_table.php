<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_pricing_items', function (Blueprint $table) {
            // Add pricing model field
            $table->enum('pricing_model', [
                'fixed',        // Fixed price (one-time or subscription)
                'per_unit',     // Per unit (per message, per user, etc.)
                'tiered',       // Tiered pricing
                'usage_based',  // Usage-based pricing
                'custom'        // Custom pricing model
            ])->default('fixed')->after('price');

            // Add unit information for per-unit pricing
            $table->string('unit_type')->nullable()->after('pricing_model'); // e.g., 'message', 'user', 'api_call'
            $table->string('unit_label')->nullable()->after('unit_type'); // e.g., 'per message', 'per user'

            // Add minimum and maximum quantities for tiered pricing
            $table->integer('min_quantity')->nullable()->after('unit_label');
            $table->integer('max_quantity')->nullable()->after('min_quantity');

            // Add setup fee for services
            $table->decimal('setup_fee', 10, 2)->nullable()->after('max_quantity');

            // Add category for grouping pricing items
            $table->string('category')->nullable()->after('setup_fee'); // e.g., 'messaging', 'subscription', 'add-ons'

            // Add metadata for additional pricing information
            $table->json('metadata')->nullable()->after('category');

            // Add quotation integration fields
            $table->boolean('include_in_quotations')->default(true)->after('metadata');
            $table->text('quotation_description')->nullable()->after('include_in_quotations');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_pricing_items', function (Blueprint $table) {
            $table->dropColumn([
                'pricing_model',
                'unit_type',
                'unit_label',
                'min_quantity',
                'max_quantity',
                'setup_fee',
                'category',
                'metadata',
                'include_in_quotations',
                'quotation_description'
            ]);
        });
    }
};
