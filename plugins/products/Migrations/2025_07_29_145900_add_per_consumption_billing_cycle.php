<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with the new enum values
        if (DB::getDriverName() === 'sqlite') {
            // Create a temporary table with the new enum values
            Schema::create('product_pricing_items_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
                $table->string('name');
                $table->text('description')->nullable();
                $table->decimal('price', 10, 2)->nullable();
                $table->string('currency', 3)->default('USD');
                $table->enum('billing_cycle', ['one-time', 'monthly', 'quarterly', 'yearly', 'per_consumption', 'custom'])->default('monthly');
                $table->text('features')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->boolean('is_popular')->default(false);
                $table->string('button_text')->nullable();
                $table->text('notes')->nullable();
                $table->timestamps();
                
                // Enhanced fields from the enhancement migration
                $table->enum('pricing_model', [
                    'fixed',
                    'per_unit',
                    'tiered',
                    'usage_based',
                    'custom'
                ])->default('fixed')->after('price');
                $table->string('unit_type')->nullable()->after('pricing_model');
                $table->string('unit_label')->nullable()->after('unit_type');
                $table->integer('min_quantity')->nullable()->after('unit_label');
                $table->integer('max_quantity')->nullable()->after('min_quantity');
                $table->decimal('setup_fee', 10, 2)->nullable()->after('max_quantity');
                $table->string('category')->nullable()->after('setup_fee');
                $table->json('metadata')->nullable()->after('category');
                $table->boolean('include_in_quotations')->default(true)->after('metadata');
                $table->text('quotation_description')->nullable()->after('include_in_quotations');
            });

            // Copy data from the original table
            DB::statement('INSERT INTO product_pricing_items_temp SELECT * FROM product_pricing_items');

            // Drop the original table
            Schema::drop('product_pricing_items');

            // Rename the temporary table
            Schema::rename('product_pricing_items_temp', 'product_pricing_items');

            // Recreate indexes
            Schema::table('product_pricing_items', function (Blueprint $table) {
                $table->index(['product_id']);
                $table->index(['is_active']);
                $table->index(['sort_order']);
                $table->index(['billing_cycle']);
                $table->index(['is_popular']);
            });
        } else {
            // For other databases, use ALTER TABLE
            DB::statement("ALTER TABLE product_pricing_items MODIFY COLUMN billing_cycle ENUM('one-time', 'monthly', 'quarterly', 'yearly', 'per_consumption', 'custom') DEFAULT 'monthly'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, we need to recreate the table without the per_consumption option
        if (DB::getDriverName() === 'sqlite') {
            // Create a temporary table with the old enum values
            Schema::create('product_pricing_items_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
                $table->string('name');
                $table->text('description')->nullable();
                $table->decimal('price', 10, 2)->nullable();
                $table->string('currency', 3)->default('USD');
                $table->enum('billing_cycle', ['one-time', 'monthly', 'quarterly', 'yearly', 'custom'])->default('monthly');
                $table->text('features')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->boolean('is_popular')->default(false);
                $table->string('button_text')->nullable();
                $table->text('notes')->nullable();
                $table->timestamps();
                
                // Enhanced fields
                $table->enum('pricing_model', [
                    'fixed',
                    'per_unit',
                    'tiered',
                    'usage_based',
                    'custom'
                ])->default('fixed')->after('price');
                $table->string('unit_type')->nullable()->after('pricing_model');
                $table->string('unit_label')->nullable()->after('unit_type');
                $table->integer('min_quantity')->nullable()->after('unit_label');
                $table->integer('max_quantity')->nullable()->after('min_quantity');
                $table->decimal('setup_fee', 10, 2)->nullable()->after('max_quantity');
                $table->string('category')->nullable()->after('setup_fee');
                $table->json('metadata')->nullable()->after('category');
                $table->boolean('include_in_quotations')->default(true)->after('metadata');
                $table->text('quotation_description')->nullable()->after('include_in_quotations');
            });

            // Copy data from the original table (excluding per_consumption records)
            DB::statement("INSERT INTO product_pricing_items_temp SELECT * FROM product_pricing_items WHERE billing_cycle != 'per_consumption'");

            // Drop the original table
            Schema::drop('product_pricing_items');

            // Rename the temporary table
            Schema::rename('product_pricing_items_temp', 'product_pricing_items');

            // Recreate indexes
            Schema::table('product_pricing_items', function (Blueprint $table) {
                $table->index(['product_id']);
                $table->index(['is_active']);
                $table->index(['sort_order']);
                $table->index(['billing_cycle']);
                $table->index(['is_popular']);
            });
        } else {
            // For other databases, use ALTER TABLE
            DB::statement("ALTER TABLE product_pricing_items MODIFY COLUMN billing_cycle ENUM('one-time', 'monthly', 'quarterly', 'yearly', 'custom') DEFAULT 'monthly'");
        }
    }
};
