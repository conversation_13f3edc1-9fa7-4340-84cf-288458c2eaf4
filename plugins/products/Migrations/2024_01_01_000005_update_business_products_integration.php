<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if business_products table exists (from business plugin)
        if (Schema::hasTable('business_products')) {
            // If it exists, we'll add any missing columns that might be needed
            Schema::table('business_products', function (Blueprint $table) {
                // Add implementation_date if it doesn't exist
                if (!Schema::hasColumn('business_products', 'implementation_date')) {
                    $table->date('implementation_date')->nullable()->after('end_date');
                }
                
                // Add contract_value if it doesn't exist
                if (!Schema::hasColumn('business_products', 'contract_value')) {
                    $table->decimal('contract_value', 12, 2)->nullable()->after('implementation_date');
                }
                
                // Add renewal_date if it doesn't exist
                if (!Schema::hasColumn('business_products', 'renewal_date')) {
                    $table->date('renewal_date')->nullable()->after('contract_value');
                }
                
                // Add product_version if it doesn't exist
                if (!Schema::hasColumn('business_products', 'product_version')) {
                    $table->string('product_version')->nullable()->after('renewal_date');
                }
            });
        } else {
            // If business_products table doesn't exist, create it
            Schema::create('business_products', function (Blueprint $table) {
                $table->id();
                $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
                $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
                $table->decimal('custom_price', 10, 2)->nullable();
                $table->string('pricing_model')->nullable(); // Override product default
                $table->enum('status', ['active', 'inactive', 'pending', 'cancelled'])->default('active');
                $table->date('start_date')->nullable();
                $table->date('end_date')->nullable();
                $table->date('implementation_date')->nullable();
                $table->decimal('contract_value', 12, 2)->nullable();
                $table->date('renewal_date')->nullable();
                $table->string('product_version')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                // Unique constraint to prevent duplicate assignments
                $table->unique(['business_id', 'product_id']);
                
                // Indexes for performance
                $table->index(['business_id']);
                $table->index(['product_id']);
                $table->index(['status']);
                $table->index(['assigned_by']);
                $table->index(['start_date']);
                $table->index(['end_date']);
                $table->index(['implementation_date']);
                $table->index(['renewal_date']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop columns we added, don't drop the entire table
        // as it might be used by the business plugin
        if (Schema::hasTable('business_products')) {
            Schema::table('business_products', function (Blueprint $table) {
                if (Schema::hasColumn('business_products', 'implementation_date')) {
                    $table->dropColumn('implementation_date');
                }
                if (Schema::hasColumn('business_products', 'contract_value')) {
                    $table->dropColumn('contract_value');
                }
                if (Schema::hasColumn('business_products', 'renewal_date')) {
                    $table->dropColumn('renewal_date');
                }
                if (Schema::hasColumn('business_products', 'product_version')) {
                    $table->dropColumn('product_version');
                }
            });
        }
    }
};
