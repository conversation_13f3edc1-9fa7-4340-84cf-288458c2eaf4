<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_pricing_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->nullable(); // Price amount
            $table->string('currency', 3)->default('USD'); // Currency code (USD, EUR, etc.)
            $table->enum('billing_cycle', ['one-time', 'monthly', 'quarterly', 'yearly', 'custom'])->default('monthly');
            $table->text('features')->nullable(); // Features included in this pricing tier
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0); // For ordering pricing items
            $table->boolean('is_popular')->default(false); // Highlight as popular option
            $table->string('button_text')->nullable(); // Custom button text (e.g., "Get Started", "Contact Sales")
            $table->text('notes')->nullable(); // Additional notes or conditions
            $table->timestamps();

            // Indexes for performance
            $table->index(['product_id']);
            $table->index(['is_active']);
            $table->index(['sort_order']);
            $table->index(['billing_cycle']);
            $table->index(['is_popular']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_pricing_items');
    }
};
