<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_releases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->string('version', 50); // Version number (e.g., 1.0.0, 2.1.3)
            $table->string('title');
            $table->text('description')->nullable();
            $table->longText('changelog')->nullable(); // Detailed changelog
            $table->date('release_date');
            $table->boolean('is_published')->default(false);
            $table->enum('release_type', ['major', 'minor', 'patch', 'hotfix'])->default('minor');
            $table->json('features')->nullable(); // New features in this release
            $table->json('bug_fixes')->nullable(); // Bug fixes in this release
            $table->json('breaking_changes')->nullable(); // Breaking changes
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['product_id']);
            $table->index(['version']);
            $table->index(['release_date']);
            $table->index(['is_published']);
            $table->index(['release_type']);
            $table->index(['created_by']);
            $table->index(['published_at']);
            
            // Unique constraint for version per product
            $table->unique(['product_id', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_releases');
    }
};
