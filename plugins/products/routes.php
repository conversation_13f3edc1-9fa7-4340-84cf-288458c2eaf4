<?php

use Illuminate\Support\Facades\Route;
use Plugins\Products\Controllers\ProductController;
use Plugins\Products\Controllers\ProductDocumentController;
use Plugins\Products\Controllers\ProductReleaseController;
use Plugins\Products\Controllers\ProductPricingController;

// Product Management Routes
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('index');
    Route::get('/create', [ProductController::class, 'create'])->name('create');
    Route::post('/', [ProductController::class, 'store'])->name('store');
    Route::get('/{product}', [ProductController::class, 'show'])->name('show');
    Route::get('/{product}/edit', [ProductController::class, 'edit'])->name('edit');
    Route::put('/{product}', [ProductController::class, 'update'])->name('update');
    Route::delete('/{product}', [ProductController::class, 'destroy'])->name('destroy');
    Route::get('/search', [ProductController::class, 'search'])->name('search');

    // Product Document Management Routes
    Route::prefix('/{product}/documents')->name('documents.')->group(function () {
        Route::get('/', [ProductDocumentController::class, 'index'])->name('index');
        Route::get('/create', [ProductDocumentController::class, 'create'])->name('create');
        Route::post('/', [ProductDocumentController::class, 'store'])->name('store');
        Route::get('/{document}', [ProductDocumentController::class, 'show'])->name('show');
        Route::get('/{document}/edit', [ProductDocumentController::class, 'edit'])->name('edit');
        Route::put('/{document}', [ProductDocumentController::class, 'update'])->name('update');
        Route::delete('/{document}', [ProductDocumentController::class, 'destroy'])->name('destroy');
        Route::get('/{document}/download', [ProductDocumentController::class, 'download'])->name('download');
        Route::get('/{document}/view', [ProductDocumentController::class, 'view'])->name('view');
        Route::post('/{document}/new-version', [ProductDocumentController::class, 'newVersion'])->name('new-version');
        Route::get('/{document}/versions', [ProductDocumentController::class, 'versions'])->name('versions');
        Route::get('/{document}/versions/{version}/download', [ProductDocumentController::class, 'downloadVersion'])->name('version-download');
    });

    // Product Release Management Routes
    Route::prefix('/{product}/releases')->name('releases.')->group(function () {
        Route::get('/', [ProductReleaseController::class, 'index'])->name('index');
        Route::get('/create', [ProductReleaseController::class, 'create'])->name('create');
        Route::post('/', [ProductReleaseController::class, 'store'])->name('store');
        Route::get('/{release}', [ProductReleaseController::class, 'show'])->name('show');
        Route::get('/{release}/edit', [ProductReleaseController::class, 'edit'])->name('edit');
        Route::put('/{release}', [ProductReleaseController::class, 'update'])->name('update');
        Route::delete('/{release}', [ProductReleaseController::class, 'destroy'])->name('destroy');
        Route::post('/{release}/publish', [ProductReleaseController::class, 'publish'])->name('publish');
        Route::post('/{release}/unpublish', [ProductReleaseController::class, 'unpublish'])->name('unpublish');
    });

    // Product Pricing Management Routes
    Route::prefix('/{product}/pricing')->name('pricing.')->group(function () {
        Route::get('/', [ProductPricingController::class, 'index'])->name('index');
        Route::get('/create', [ProductPricingController::class, 'create'])->name('create');
        Route::post('/', [ProductPricingController::class, 'store'])->name('store');
        Route::get('/{pricing}/edit', [ProductPricingController::class, 'edit'])->name('edit');
        Route::put('/{pricing}', [ProductPricingController::class, 'update'])->name('update');
        Route::delete('/{pricing}', [ProductPricingController::class, 'destroy'])->name('destroy');
    });
});

// Global Product Routes (for business integration)
Route::get('/all-products', [ProductController::class, 'allProducts'])->name('all-products.index');
Route::get('/products-api', [ProductController::class, 'api'])->name('products.api');
