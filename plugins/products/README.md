# Products Plugin

A comprehensive product management system for the Laravel application that provides document versioning, release tracking, and seamless integration with the business management system.

## Features

- **Product Management**: Create, read, update, and delete product entities with rich metadata
- **Document Management**: Upload and manage product documents with version control
- **Release Tracking**: Track product updates, releases, and version history
- **Pricing Management**: Flexible pricing structure with multiple pricing items per product
- **Business Integration**: Seamless integration with the business plugin for product assignments
- **Permission-Based Access**: Granular permissions for different product operations

## Dependencies

- **users** plugin (required) - Provides authentication and role management

## Permissions

The plugin defines six specific permissions:

1. **manage_products** - Create, edit, and delete product entities
2. **view_products** - Read-only access to product data
3. **manage_product_documents** - Upload, edit, and manage product documents
4. **view_product_documents** - Read-only access to product documents
5. **manage_product_releases** - Create and manage product releases and updates
6. **view_product_releases** - Read-only access to product release information

## Installation

1. The plugin is installed in the `plugins/products` directory
2. Enable the plugin through the Plugin Manager interface
3. Run migrations to create the required database tables
4. Run the permission seeder to add product permissions to the database

### Manual Installation Steps

```bash
# Run migrations
php artisan migrate --path=plugins/products/Migrations

# Run seeders
php artisan db:seed --class=Plugins\\Products\\Seeds\\ProductPermissionSeeder
php artisan db:seed --class=Plugins\\Products\\Seeds\\SampleProductSeeder
```

## Database Structure

### Products Table
- `id` - Primary key
- `name` - Product name
- `slug` - URL-friendly identifier
- `description` - Product description
- `icon` - Product icon/image
- `target_audience` - Target audience description
- `use_cases` - Product use cases
- `reference_links` - JSON array of reference links
- `is_active` - Product status
- `created_by` - Foreign key to users table
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### Product Documents Table
- `id` - Primary key
- `product_id` - Foreign key to products table
- `category` - Document category (Manual, Brochure, Policies, Other)
- `name` - Document name
- `description` - Document description
- `file_name` - Stored file name
- `original_name` - Original file name
- `file_path` - File storage path
- `mime_type` - File MIME type
- `file_size` - File size in bytes
- `version` - Document version number
- `is_current_version` - Boolean flag for current version
- `uploaded_by` - Foreign key to users table
- `created_at` - Upload timestamp
- `updated_at` - Last update timestamp

### Product Releases Table
- `id` - Primary key
- `product_id` - Foreign key to products table
- `version` - Release version number
- `title` - Release title
- `description` - Release description
- `changelog` - Release changelog
- `release_date` - Release date
- `is_published` - Publication status
- `created_by` - Foreign key to users table
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### Product Pricing Items Table
- `id` - Primary key
- `product_id` - Foreign key to products table
- `name` - Pricing item name
- `description` - Pricing item description
- `price` - Price amount
- `currency` - Currency code
- `billing_cycle` - Billing cycle (monthly, yearly, one-time)
- `is_active` - Pricing item status
- `sort_order` - Display order
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## Usage

### Accessing Product Management

Once the plugin is enabled and permissions are assigned:

1. Navigate to `/products` to view all products
2. Use the interface to create, edit, and manage products
3. Upload and manage documents with version control
4. Track product releases and updates
5. Configure pricing structures

### Document Categories

Products support four document categories:
- **Manual** - User manuals and documentation
- **Brochure** - Marketing materials and brochures
- **Policies** - Terms of service, privacy policies, etc.
- **Other** - Miscellaneous documents

### Version Control

- Documents support version control with automatic versioning
- Previous versions are maintained and accessible
- Version history tracking with upload timestamps and user information

## Integration with Business Plugin

The Products plugin integrates seamlessly with the Business plugin:
- Businesses can be assigned multiple products
- Product assignments are tracked with timestamps and user information
- Business forms include product selection functionality
- Product usage analytics across businesses

## API Endpoints

- `GET /products-api` - Get all products as JSON
- `GET /all-products` - Global product listing for integration

## Security

- All routes are protected by authentication middleware
- Permission checks are enforced at the controller level
- File uploads are validated and stored securely
- Document access is controlled by permissions

## Future Enhancements

- Advanced analytics and reporting
- Product comparison features
- Integration with external systems
- Advanced pricing models
- Product recommendations
- Bulk operations for document management
