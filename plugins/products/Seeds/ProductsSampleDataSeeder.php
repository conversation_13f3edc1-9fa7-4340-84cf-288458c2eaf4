<?php

namespace Plugins\Products\Seeds;

use Illuminate\Database\Seeder;
use Plugins\Products\Models\Product;
use Plugins\Products\Models\ProductPricingItem;
use Plugins\Products\Models\ProductRelease;
use App\Models\User;

class ProductsSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user or create a default user
        $user = User::whereHas('role', function ($query) {
            $query->where('name', 'admin');
        })->first();

        if (!$user) {
            $user = User::first();
        }

        if (!$user) {
            $this->command->warn("No users found. Please create users first.");
            return;
        }

        $products = [
            [
                'name' => 'WhatsApp Business API',
                'slug' => 'whatsapp-business-api',
                'description' => 'Comprehensive WhatsApp Business API solution for automated messaging, customer support, and marketing campaigns.',
                'icon' => 'fab fa-whatsapp',
                'target_audience' => 'Businesses looking to automate customer communication through WhatsApp, including e-commerce, customer service, and marketing teams.',
                'use_cases' => 'Order confirmations, customer support automation, marketing campaigns, appointment reminders, lead qualification, and customer onboarding.',
                'reference_links' => [
                    'https://developers.facebook.com/docs/whatsapp',
                    'https://business.whatsapp.com/api'
                ],
                'pricing' => [
                    [
                        'name' => 'Starter',
                        'description' => 'Perfect for small businesses getting started with WhatsApp automation',
                        'price' => 99.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Up to 1,000 messages per month\nBasic automation templates\nEmail support\nAPI access",
                        'is_popular' => false,
                    ],
                    [
                        'name' => 'Professional',
                        'description' => 'Ideal for growing businesses with higher messaging volumes',
                        'price' => 299.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Up to 10,000 messages per month\nAdvanced automation workflows\nPriority support\nAnalytics dashboard\nCustom integrations",
                        'is_popular' => true,
                    ],
                    [
                        'name' => 'Enterprise',
                        'description' => 'For large organizations with complex requirements',
                        'price' => null,
                        'currency' => 'USD',
                        'billing_cycle' => 'custom',
                        'features' => "Unlimited messages\nCustom development\nDedicated account manager\n24/7 phone support\nSLA guarantees",
                        'button_text' => 'Contact Sales',
                        'is_popular' => false,
                    ],
                ],
                'releases' => [
                    [
                        'version' => '2.1.0',
                        'title' => 'Enhanced Media Support',
                        'description' => 'Added support for video messages and improved image handling',
                        'release_type' => 'minor',
                        'features' => ['Video message support', 'Improved image compression', 'Media analytics'],
                        'bug_fixes' => ['Fixed message delivery status', 'Resolved webhook timeout issues'],
                        'is_published' => true,
                    ],
                ],
            ],
            [
                'name' => 'AI Chatbot Platform',
                'slug' => 'ai-chatbot-platform',
                'description' => 'Advanced AI-powered chatbot platform with natural language processing, multi-channel support, and intelligent conversation flows.',
                'icon' => 'fas fa-robot',
                'target_audience' => 'Customer service teams, sales departments, and businesses looking to automate customer interactions across multiple channels.',
                'use_cases' => 'Customer support automation, lead qualification, FAQ handling, appointment booking, product recommendations, and 24/7 customer assistance.',
                'reference_links' => [
                    'https://docs.example.com/chatbot-api',
                    'https://example.com/chatbot-demo'
                ],
                'pricing' => [
                    [
                        'name' => 'Basic',
                        'description' => 'Essential chatbot features for small teams',
                        'price' => 49.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Up to 500 conversations per month\nBasic NLP\nEmail integration\nStandard templates",
                        'is_popular' => false,
                    ],
                    [
                        'name' => 'Advanced',
                        'description' => 'Full-featured chatbot with AI capabilities',
                        'price' => 199.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Up to 5,000 conversations per month\nAdvanced AI/NLP\nMulti-channel support\nCustom training\nAnalytics",
                        'is_popular' => true,
                    ],
                ],
                'releases' => [
                    [
                        'version' => '3.0.0',
                        'title' => 'AI Engine Upgrade',
                        'description' => 'Major upgrade to the AI engine with improved natural language understanding',
                        'release_type' => 'major',
                        'features' => ['New AI engine', 'Sentiment analysis', 'Multi-language support'],
                        'breaking_changes' => ['API v2 deprecated', 'New authentication method required'],
                        'is_published' => true,
                    ],
                ],
            ],
            [
                'name' => 'Business Analytics Suite',
                'slug' => 'business-analytics-suite',
                'description' => 'Comprehensive business intelligence and analytics platform with real-time dashboards, custom reports, and data visualization tools.',
                'icon' => 'fas fa-chart-line',
                'target_audience' => 'Business analysts, executives, data teams, and organizations looking to make data-driven decisions.',
                'use_cases' => 'Performance monitoring, sales analytics, customer behavior analysis, financial reporting, operational insights, and strategic planning.',
                'reference_links' => [
                    'https://docs.example.com/analytics-api',
                    'https://example.com/analytics-demo'
                ],
                'pricing' => [
                    [
                        'name' => 'Standard',
                        'description' => 'Core analytics features for small to medium businesses',
                        'price' => 149.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Up to 10 dashboards\nStandard reports\nData export\nEmail alerts",
                        'is_popular' => false,
                    ],
                    [
                        'name' => 'Premium',
                        'description' => 'Advanced analytics with custom reporting',
                        'price' => 399.00,
                        'currency' => 'USD',
                        'billing_cycle' => 'monthly',
                        'features' => "Unlimited dashboards\nCustom reports\nReal-time data\nAPI access\nAdvanced visualizations",
                        'is_popular' => true,
                    ],
                ],
            ],
        ];

        foreach ($products as $productData) {
            // Create product
            $product = Product::create([
                'name' => $productData['name'],
                'slug' => $productData['slug'],
                'description' => $productData['description'],
                'icon' => $productData['icon'],
                'target_audience' => $productData['target_audience'],
                'use_cases' => $productData['use_cases'],
                'reference_links' => $productData['reference_links'],
                'is_active' => true,
                'created_by' => $user->id,
            ]);

            $this->command->info("Created product: {$product->name}");

            // Create pricing items
            if (isset($productData['pricing'])) {
                foreach ($productData['pricing'] as $index => $pricingData) {
                    ProductPricingItem::create([
                        'product_id' => $product->id,
                        'name' => $pricingData['name'],
                        'description' => $pricingData['description'],
                        'price' => $pricingData['price'],
                        'currency' => $pricingData['currency'],
                        'billing_cycle' => $pricingData['billing_cycle'],
                        'features' => $pricingData['features'],
                        'is_active' => true,
                        'sort_order' => $index + 1,
                        'is_popular' => $pricingData['is_popular'] ?? false,
                        'button_text' => $pricingData['button_text'] ?? null,
                    ]);
                }
                $this->command->info("Created pricing items for: {$product->name}");
            }

            // Create releases
            if (isset($productData['releases'])) {
                foreach ($productData['releases'] as $releaseData) {
                    ProductRelease::create([
                        'product_id' => $product->id,
                        'version' => $releaseData['version'],
                        'title' => $releaseData['title'],
                        'description' => $releaseData['description'],
                        'release_date' => now()->subDays(rand(1, 30)),
                        'release_type' => $releaseData['release_type'],
                        'features' => $releaseData['features'] ?? [],
                        'bug_fixes' => $releaseData['bug_fixes'] ?? [],
                        'breaking_changes' => $releaseData['breaking_changes'] ?? [],
                        'is_published' => $releaseData['is_published'] ?? false,
                        'created_by' => $user->id,
                        'published_at' => $releaseData['is_published'] ? now()->subDays(rand(1, 30)) : null,
                    ]);
                }
                $this->command->info("Created releases for: {$product->name}");
            }
        }

        $this->command->info("Sample products seeded successfully!");
    }
}
