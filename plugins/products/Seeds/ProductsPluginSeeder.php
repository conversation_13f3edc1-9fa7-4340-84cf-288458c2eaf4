<?php

namespace Plugins\Products\Seeds;

use Illuminate\Database\Seeder;

class ProductsPluginSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Products Plugin...');

        // Run permission seeder first
        $this->call(ProductPermissionSeeder::class);

        // Then run sample data seeder
        $this->call(ProductsSampleDataSeeder::class);

        $this->command->info('Products Plugin seeded successfully!');
    }
}
