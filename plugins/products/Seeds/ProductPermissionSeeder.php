<?php

namespace Plugins\Products\Seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            [
                'name' => 'manage_products',
                'display_name' => 'Manage Products',
                'description' => 'Create, edit, and delete product entities',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_products',
                'display_name' => 'View Products',
                'description' => 'Read-only access to product data',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_product_documents',
                'display_name' => 'Manage Product Documents',
                'description' => 'Upload, edit, and manage product documents',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_product_documents',
                'display_name' => 'View Product Documents',
                'description' => 'Read-only access to product documents',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_product_releases',
                'display_name' => 'Manage Product Releases',
                'description' => 'Create and manage product releases and updates',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_product_releases',
                'display_name' => 'View Product Releases',
                'description' => 'Read-only access to product release information',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($permissions as $permission) {
            // Check if permission already exists
            $exists = DB::table('permissions')
                       ->where('name', $permission['name'])
                       ->exists();

            if (!$exists) {
                DB::table('permissions')->insert($permission);
                $this->command->info("Created permission: {$permission['name']}");
            } else {
                $this->command->info("Permission already exists: {$permission['name']}");
            }
        }

        // Assign permissions to admin role if it exists
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        
        if ($adminRole) {
            foreach ($permissions as $permission) {
                $permissionRecord = DB::table('permissions')
                                     ->where('name', $permission['name'])
                                     ->first();

                if ($permissionRecord) {
                    $exists = DB::table('permission_role')
                               ->where('role_id', $adminRole->id)
                               ->where('permission_id', $permissionRecord->id)
                               ->exists();

                    if (!$exists) {
                        DB::table('permission_role')->insert([
                            'role_id' => $adminRole->id,
                            'permission_id' => $permissionRecord->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $this->command->info("Assigned {$permission['name']} to admin role");
                    }
                }
            }
        } else {
            $this->command->warn("Admin role not found. Please assign product permissions manually.");
        }
    }
}
