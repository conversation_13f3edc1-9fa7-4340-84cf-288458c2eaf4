@extends('layouts.app')

@section('title', 'Business Documents')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Documents: {{ $business->name }}</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">Manage documents for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.documents.create', $business) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Upload Document
                    </a>
                @endif
            </div>
        </div>

        <!-- Document Type Filter -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6 transition duration-150 ease-in-out">
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-4">Filter by type:</span>
                <a href="{{ route('business.documents.index', $business) }}" 
                   class="px-3 py-1 text-xs font-medium rounded-full {{ !request('type') ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:bg-gray-600' }} transition duration-150 ease-in-out">
                    All ({{ $documents->count() }})
                </a>
                @foreach($documentTypes as $key => $label)
                    @php $count = $documents->where('document_type', $key)->count(); @endphp
                    @if($count > 0)
                        <a href="{{ route('business.documents.index', $business) }}?type={{ $key }}" 
                           class="px-3 py-1 text-xs font-medium rounded-full {{ request('type') === $key ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:bg-gray-600' }} transition duration-150 ease-in-out">
                            {{ $label }} ({{ $count }})
                        </a>
                    @endif
                @endforeach
            </div>
        </div>

        <!-- Documents List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            @if($documents->count() > 0)
                @php
                    $filteredDocuments = request('type') 
                        ? $documents->where('document_type', request('type')) 
                        : $documents;
                @endphp
                
                @if($filteredDocuments->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        @foreach($filteredDocuments as $document)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center">
                                        <i class="{{ $document->file_icon }} text-2xl text-gray-600 dark:text-gray-400 dark:text-gray-500 mr-3"></i>
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                {{ $document->original_name }}
                                            </h3>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $document->document_type_label }}</p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-1">
                                        @if($document->isImage() || $document->isPdf())
                                            <a href="{{ route('business.documents.view', [$business, $document]) }}" 
                                               target="_blank"
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-xs">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                        <a href="{{ route('business.documents.download', [$business, $document]) }}" 
                                           class="text-green-600 dark:text-green-400 hover:text-green-800 dark:text-green-200 text-xs">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <form method="POST" action="{{ route('business.documents.destroy', [$business, $document]) }}" 
                                                  onsubmit="return confirm('Are you sure you want to delete this document?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 text-xs">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                                
                                @if($document->description)
                                    <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 mb-3">{{ $document->description }}</p>
                                @endif
                                
                                <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 space-y-1">
                                    <div class="flex justify-between">
                                        <span>Size:</span>
                                        <span>{{ $document->formatted_file_size }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Uploaded:</span>
                                        <span>{{ $document->upload_date->format('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>By:</span>
                                        <span>{{ $document->uploader->name ?? 'Unknown' }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No documents found for the selected type.</div>
                        <a href="{{ route('business.documents.index', $business) }}" 
                           class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                            View all documents
                        </a>
                    </div>
                @endif
            @else
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No documents found for this business.</div>
                    @if(auth()->user()->hasPermission('manage_businesses'))
                        <a href="{{ route('business.documents.create', $business) }}" 
                           class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Upload First Document
                        </a>
                    @endif
                </div>
            @endif
        </div>

        <!-- Document Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Documents</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $documents->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-pdf text-red-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">PDF Documents</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $documents->where('mime_type', 'application/pdf')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-image text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Images</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $documents->filter(fn($doc) => str_starts_with($doc->mime_type, 'image/'))->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-hdd text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Size</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    @php
                                        $totalSize = $documents->sum('file_size');
                                        if ($totalSize >= 1048576) {
                                            echo number_format($totalSize / 1048576, 1) . ' MB';
                                        } elseif ($totalSize >= 1024) {
                                            echo number_format($totalSize / 1024, 1) . ' KB';
                                        } else {
                                            echo $totalSize . ' bytes';
                                        }
                                    @endphp
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
