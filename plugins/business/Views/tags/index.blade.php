@extends('layouts.app')

@section('title', 'Tag Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Tag Management</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">Manage tags for organizing businesses</p>
            </div>
            <div class="flex space-x-3">
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('tags.create') }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Tag
                    </a>
                @endif
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 transition duration-150 ease-in-out">
            <form method="GET" action="{{ route('tags.index') }}" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search tags..." 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
                </div>
                <div>
                    <select name="status" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Search
                </button>
                @if(request()->hasAny(['search', 'status']))
                    <a href="{{ route('tags.index') }}" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Clear
                    </a>
                @endif
            </form>
        </div>

        <!-- Tags List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            @if($tags->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                    @foreach($tags as $tag)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $tag->color }};"></div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ $tag->name }}</h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $tag->businesses_count }} businesses</p>
                                    </div>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="{{ route('tags.show', $tag) }}" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-xs">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(auth()->user()->hasPermission('manage_businesses'))
                                        <a href="{{ route('tags.edit', $tag) }}" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 text-xs">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($tag->businesses_count == 0)
                                            <form method="POST" action="{{ route('tags.destroy', $tag) }}" 
                                                  onsubmit="return confirm('Are you sure you want to delete this tag?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 text-xs">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    @endif
                                </div>
                            </div>
                            
                            @if($tag->description)
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 mb-3">{{ $tag->description }}</p>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $tag->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                    {{ $tag->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    Created {{ $tag->created_at->diffForHumans() }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    {{ $tags->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No tags found.</div>
                    @if(auth()->user()->hasPermission('manage_businesses'))
                        <a href="{{ route('tags.create') }}" 
                           class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Create First Tag
                        </a>
                    @endif
                </div>
            @endif
        </div>

        <!-- Tag Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tags text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $tags->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Active Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $tags->where('is_active', true)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Used Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $tags->where('businesses_count', '>', 0)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-palette text-orange-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Colors Used</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $tags->pluck('color')->unique()->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Tags -->
        @if($tags->count() > 0)
            <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Most Popular Tags</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Tags with the most business assignments</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex flex-wrap gap-2">
                        @foreach($tags->sortByDesc('businesses_count')->take(10) as $tag)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium" 
                                  style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                {{ $tag->name }} ({{ $tag->businesses_count }})
                            </span>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
