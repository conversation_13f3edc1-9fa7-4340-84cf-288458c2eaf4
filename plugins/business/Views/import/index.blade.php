@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Import Data</h1>
            <p class="text-gray-600 mt-2">Import businesses and contacts from Excel files</p>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <div class="flex">
                    <i class="fas fa-check-circle mr-2 mt-1"></i>
                    <div>
                        <strong>Success!</strong> {{ session('success') }}
                    </div>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex">
                    <i class="fas fa-exclamation-circle mr-2 mt-1"></i>
                    <div>
                        <strong>Error!</strong> {{ session('error') }}
                    </div>
                </div>
            </div>
        @endif

        @if(session('import_errors') && count(session('import_errors')) > 0)
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
                <div class="flex">
                    <i class="fas fa-exclamation-triangle mr-2 mt-1"></i>
                    <div>
                        <strong>Import Warnings:</strong>
                        <ul class="mt-2 list-disc list-inside">
                            @foreach(session('import_errors') as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <!-- Import Cards -->
        <div class="grid md:grid-cols-2 gap-8">
            <!-- Business Import Card -->
            <div class="bg-white rounded-lg shadow-md border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-100 p-3 rounded-lg mr-4">
                            <i class="fas fa-building text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Import Businesses</h2>
                            <p class="text-gray-600 text-sm">Upload Excel file with business data</p>
                        </div>
                    </div>

                    <!-- Sample Download -->
                    <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-700">Need a template?</p>
                                <p class="text-xs text-gray-500">Download sample Excel file with correct format</p>
                            </div>
                            <a href="{{ route('business.import.business-sample') }}" 
                               class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm font-medium">
                                <i class="fas fa-download mr-1"></i>
                                Download Sample
                            </a>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <form action="{{ route('business.import.businesses') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Excel File
                            </label>
                            <input type="file" 
                                   name="file" 
                                   accept=".xlsx,.xls,.csv"
                                   required
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <p class="text-xs text-gray-500 mt-1">Supported formats: .xlsx, .xls, .csv (Max: 10MB)</p>
                        </div>
                        
                        <button type="submit" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-upload mr-2"></i>
                            Import Businesses
                        </button>
                    </form>

                    <!-- Format Info -->
                    <div class="mt-4 text-xs text-gray-500">
                        <p class="font-medium mb-1">Required columns:</p>
                        <p>name, email</p>
                        <p class="font-medium mb-1 mt-2">Optional columns:</p>
                        <p>brand_name, legal_name, description, phone, address, city, state, country, postal_code, website, tax_id, status</p>
                    </div>
                </div>
            </div>

            <!-- Contact Import Card -->
            <div class="bg-white rounded-lg shadow-md border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-green-100 p-3 rounded-lg mr-4">
                            <i class="fas fa-users text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Import Contacts</h2>
                            <p class="text-gray-600 text-sm">Upload Excel file with contact data</p>
                        </div>
                    </div>

                    <!-- Sample Download -->
                    <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-700">Need a template?</p>
                                <p class="text-xs text-gray-500">Download sample Excel file with correct format</p>
                            </div>
                            <a href="{{ route('business.import.contact-sample') }}" 
                               class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm font-medium">
                                <i class="fas fa-download mr-1"></i>
                                Download Sample
                            </a>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <form action="{{ route('business.import.contacts') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Excel File
                            </label>
                            <input type="file" 
                                   name="file" 
                                   accept=".xlsx,.xls,.csv"
                                   required
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100">
                            <p class="text-xs text-gray-500 mt-1">Supported formats: .xlsx, .xls, .csv (Max: 10MB)</p>
                        </div>
                        
                        <button type="submit" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-upload mr-2"></i>
                            Import Contacts
                        </button>
                    </form>

                    <!-- Format Info -->
                    <div class="mt-4 text-xs text-gray-500">
                        <p class="font-medium mb-1">Required columns:</p>
                        <p>name, business_name OR business_email</p>
                        <p class="font-medium mb-1 mt-2">Optional columns:</p>
                        <p>position, department, email, phone, is_primary, notes</p>
                        <p class="text-yellow-600 mt-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Note: Businesses must exist before importing contacts
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">
                <i class="fas fa-info-circle mr-2"></i>
                Import Instructions
            </h3>
            <div class="grid md:grid-cols-2 gap-6 text-sm text-blue-800">
                <div>
                    <h4 class="font-semibold mb-2">Before You Start:</h4>
                    <ul class="space-y-1 list-disc list-inside">
                        <li>Download the sample files to see the correct format</li>
                        <li>Ensure your data matches the column headers exactly</li>
                        <li>Remove any empty rows from your file</li>
                        <li>For contacts, make sure businesses exist first</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">Supported Formats:</h4>
                    <ul class="space-y-1 list-disc list-inside">
                        <li>Excel files (.xlsx, .xls)</li>
                        <li>CSV files (.csv)</li>
                        <li>Maximum file size: 10MB</li>
                        <li>First row must contain column headers</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-8 text-center">
            <a href="{{ route('business.index') }}" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Businesses
            </a>
        </div>
    </div>
</div>
@endsection
