@extends('layouts.app')

@section('title', 'Business Details')

@push('styles')
<!-- Quill.js Rich Text Editor -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    /* Tab styling */
    .tab-button {
        transition: all 0.2s ease-in-out;
        position: relative;
    }

    .tab-button:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Mobile responsive tabs */
    @media (max-width: 640px) {
        .tab-button {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            font-size: 14px;
        }

        .tab-button i {
            display: block;
            margin-bottom: 4px;
            margin-right: 0 !important;
        }
    }

    /* Tab content animation */
    .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Hover effects for tab content items */
    .tab-pane .hover\:bg-gray-50:hover {
        transition: background-color 0.15s ease-in-out;
    }

    /* Chat Message Styles */
    .message-bubble {
        max-width: 70%;
        word-wrap: break-word;
        animation: messageSlideIn 0.3s ease-out;
    }

    .message-bubble.user-message {
        margin-left: auto;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-radius: 18px 18px 4px 18px;
    }

    .message-bubble.other-message {
        margin-right: auto;
        background: white;
        color: #374151;
        border: 1px solid #e5e7eb;
        border-radius: 18px 18px 18px 4px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .message-bubble.system-message {
        margin: 0 auto;
        background: #f3f4f6;
        color: #6b7280;
        border-radius: 12px;
        text-align: center;
        font-size: 0.875rem;
        max-width: 80%;
    }

    .message-content {
        padding: 8px 12px;
        line-height: 1.4;
    }

    .message-content p {
        margin: 0;
    }

    .message-content p + p {
        margin-top: 4px;
    }

    .message-timestamp {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 4px;
    }

    .user-message .message-timestamp {
        color: rgba(255, 255, 255, 0.8);
    }

    .other-message .message-timestamp,
    .system-message .message-timestamp {
        color: #9ca3af;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 600;
        color: white;
        flex-shrink: 0;
    }

    .message-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
        position: absolute;
        top: 0;
        right: -80px;
        display: flex;
        gap: 4px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 4px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .message-wrapper:hover .message-actions {
        opacity: 1;
    }

    .message-wrapper {
        position: relative;
    }

    .message-status {
        font-size: 0.75rem;
        margin-top: 2px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .user-message .message-status {
        color: rgba(255, 255, 255, 0.8);
        justify-content: flex-end;
    }

    .message-edited {
        font-style: italic;
        opacity: 0.8;
    }

    .message-reply-to {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 6px 8px;
        margin-bottom: 6px;
        font-size: 0.875rem;
        border-left: 3px solid rgba(255, 255, 255, 0.5);
    }

    .other-message .message-reply-to {
        background: #f3f4f6;
        border-left-color: #d1d5db;
    }

    .message-attachments {
        margin-top: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .message-attachment {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
        text-decoration: none;
        color: inherit;
        transition: background-color 0.2s ease;
    }

    .message-attachment:hover {
        background: rgba(0, 0, 0, 0.15);
    }

    .other-message .message-attachment {
        background: #f3f4f6;
        color: #374151;
    }

    .other-message .message-attachment:hover {
        background: #e5e7eb;
    }

    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Auto-resize textarea */
    #message-input {
        resize: none;
        overflow: hidden;
    }

    /* Emoji picker styles */
    .emoji-picker {
        position: absolute;
        bottom: 100%;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 50;
        max-height: 200px;
        overflow-y: auto;
    }

    .emoji-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 4px;
    }

    .emoji-item {
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
        text-align: center;
        transition: background-color 0.2s ease;
    }

    .emoji-item:hover {
        background: #f3f4f6;
    }

    /* Scrollbar styling for messages container */
    #messages-container::-webkit-scrollbar {
        width: 6px;
    }

    #messages-container::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    #messages-container::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    #messages-container::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Chat interface improvements */
    .chat-container {
        min-height: 600px;
        max-height: 80vh;
    }

    .message-bubble + .message-bubble {
        margin-top: 1rem;
    }

    .message-input-container {
        background: linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,1));
        backdrop-filter: blur(10px);
    }

    .message-type-selector {
        min-width: 140px;
    }

    /* Improved scrollbar */
    .messages-container::-webkit-scrollbar {
        width: 8px;
    }

    .messages-container::-webkit-scrollbar-track {
        background: #f8fafc;
        border-radius: 4px;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .message-bubble {
            max-width: 85%;
        }

        .message-actions {
            right: -60px;
        }

        .message-avatar {
            width: 28px;
            height: 28px;
            font-size: 0.75rem;
        }

        .chat-container {
            min-height: 500px;
        }

        .message-type-selector {
            min-width: 120px;
        }
    }
</style>
@endpush

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">{{ $business->name }}</h1>
            <div class="flex space-x-3">
                @if(auth()->user()->hasPermission('view_business_reports'))
                    <a href="{{ route('business.reports', $business) }}"
                       class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-chart-bar mr-2"></i>
                        View Reports
                    </a>
                @endif
                <a href="{{ route('business.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Businesses
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.edit', $business) }}"
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Business
                    </a>
                @endif
            </div>
        </div>

        <!-- Business Information Tabs -->
        <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Business Details</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Complete business information, contacts, and documents</p>
            </div>

            <!-- Tab Navigation -->
            <div class="border-t border-gray-200">
                <nav class="flex sm:space-x-8 px-6" aria-label="Tabs" role="tablist">
                    <button onclick="switchTab('business-info')"
                            id="business-info-tab"
                            class="tab-button flex-1 sm:flex-none py-4 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600 text-center sm:text-left"
                            role="tab"
                            aria-selected="true"
                            aria-controls="business-info-content">
                        <i class="fas fa-building mr-2 sm:mr-2"></i>
                        <span class="hidden sm:inline">Business Info</span>
                        <span class="sm:hidden">Business<br><small>Info</small></span>
                    </button>
                    <button onclick="switchTab('contacts')"
                            id="contacts-tab"
                            class="tab-button flex-1 sm:flex-none py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-center sm:text-left"
                            role="tab"
                            aria-selected="false"
                            aria-controls="contacts-content">
                        <i class="fas fa-users mr-2 sm:mr-2"></i>
                        <span class="hidden sm:inline">Contacts ({{ $business->contacts()->count() }})</span>
                        <span class="sm:hidden">Contacts<br><small>({{ $business->contacts()->count() }})</small></span>
                    </button>
                    <button onclick="switchTab('documents')"
                            id="documents-tab"
                            class="tab-button flex-1 sm:flex-none py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-center sm:text-left"
                            role="tab"
                            aria-selected="false"
                            aria-controls="documents-content">
                        <i class="fas fa-file-alt mr-2 sm:mr-2"></i>
                        <span class="hidden sm:inline">Documents ({{ $business->documents()->count() }})</span>
                        <span class="sm:hidden">Documents<br><small>({{ $business->documents()->count() }})</small></span>
                    </button>
                    <button onclick="switchTab('activity')"
                            id="activity-tab"
                            class="tab-button flex-1 sm:flex-none py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-center sm:text-left"
                            role="tab"
                            aria-selected="false"
                            aria-controls="activity-content">
                        <i class="fas fa-history mr-2 sm:mr-2"></i>
                        <span class="hidden sm:inline">Activity ({{ $business->activities()->count() }})</span>
                        <span class="sm:hidden">Activity<br><small>({{ $business->activities()->count() }})</small></span>
                    </button>

                </nav>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Business Info Tab -->
                <div id="business-info-content" class="tab-pane" role="tabpanel" aria-labelledby="business-info-tab">
                    <div class="p-6">
                        <!-- Main Business Information -->
                        <div class="w-full">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Business Information</h4>
                                <dl class="space-y-4">
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Business Name</dt>
                                        <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->name }}</dd>
                                    </div>
                                    @if($business->description)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->description }}</dd>
                                        </div>
                                    @endif
                                    @if($business->brand_name)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Brand Name</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->brand_name }}</dd>
                                        </div>
                                    @endif
                                    @if($business->legal_name)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Legal Name</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->legal_name }}</dd>
                                        </div>
                                    @endif
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900 sm:col-span-2">
                                            @if($business->email)
                                                <a href="mailto:{{ $business->email }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $business->email }}
                                                </a>
                                            @else
                                                Not provided
                                            @endif
                                        </dd>
                                    </div>
                                    @if($business->primary_phone)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Primary Phone</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <a href="tel:{{ $business->primary_phone }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $business->primary_phone }}
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->phone)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Secondary Phone</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <a href="tel:{{ $business->phone }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $business->phone }}
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->website_url)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Primary Website</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <a href="{{ $business->website_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                                    {{ $business->website_url }}
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->website)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Secondary Website</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <a href="{{ $business->website }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                                    {{ $business->website }}
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->full_address)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Address</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->full_address }}</dd>
                                        </div>
                                    @endif
                                    @if($business->tax_id)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Tax ID</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->tax_id }}</dd>
                                        </div>
                                    @endif
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Business Status</dt>
                                        <dd class="text-sm sm:col-span-2">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $business->status_color }}">
                                                {{ $business->status_label }}
                                            </span>
                                        </dd>
                                    </div>
                                    @if($business->status === 'churned' && $business->churned_at)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Churn Date</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <i class="fas fa-calendar-times text-red-500 mr-2"></i>
                                                Churned on {{ $business->churned_at->format('F j, Y \a\t g:i A') }}
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->status === 'lost' && $business->lost_at)
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                            <dt class="text-sm font-medium text-gray-500">Lost Date</dt>
                                            <dd class="text-sm text-gray-900 sm:col-span-2">
                                                <i class="fas fa-heart-broken text-gray-500 mr-2"></i>
                                                Lost on {{ $business->lost_at->format('F j, Y \a\t g:i A') }}
                                            </dd>
                                        </div>
                                    @endif


                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                        <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->creator->name }}</dd>
                                    </div>
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                                        <dd class="text-sm text-gray-900 sm:col-span-2">{{ $business->created_at->format('M d, Y') }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <!-- Tags and Products Section -->
                            <div class="mt-6 bg-gray-50 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Tags & Products</h4>

                                <!-- Tags -->
                                <div class="mb-6">
                                    <div class="flex justify-between items-center mb-3">
                                        <h5 class="text-sm font-medium text-gray-700">Business Tags</h5>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <a href="{{ route('business.tags.index', $business) }}"
                                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                Manage Tags
                                            </a>
                                        @endif
                                    </div>
                                    @php $businessTags = $business->tags()->limit(10)->get(); @endphp
                                    @if($businessTags->count() > 0)
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($businessTags as $tag)
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                                      style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                                    <i class="fas fa-tag mr-1 text-xs"></i>
                                                    {{ $tag->name }}
                                                </span>
                                            @endforeach
                                            @if($business->tags()->count() > 10)
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                                    +{{ $business->tags()->count() - 10 }} more
                                                </span>
                                            @endif
                                        </div>
                                    @else
                                        <p class="text-sm text-gray-500">No tags assigned</p>
                                    @endif
                                </div>

                                <!-- Products -->
                                <div class="mb-6">
                                    <div class="flex justify-between items-center mb-3">
                                        <h5 class="text-sm font-medium text-gray-700">Business Products</h5>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <a href="{{ route('business.products.index', $business) }}"
                                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                Manage Products
                                            </a>
                                        @endif
                                    </div>
                                    @php $businessProducts = $business->products()->limit(10)->get(); @endphp
                                    @if($businessProducts->count() > 0)
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($businessProducts as $product)
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                                    <i class="{{ $product->icon ?? 'fas fa-box' }} mr-1 text-xs"></i>
                                                    {{ $product->name }}
                                                </span>
                                            @endforeach
                                            @if($business->products()->count() > 10)
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                                    +{{ $business->products()->count() - 10 }} more
                                                </span>
                                            @endif
                                        </div>
                                    @else
                                        <p class="text-sm text-gray-500">No products assigned</p>
                                    @endif
                                </div>

                            </div>
                        </div>

                        <!-- WhatsApp Business Information Section -->
                        @if($business->whatsapp_enabled)
                            <div class="bg-white shadow rounded-lg mt-6">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                            <i class="fab fa-whatsapp text-green-500 mr-3 text-xl"></i>
                                            WhatsApp Business Integration
                                        </h3>
                                        @php $whatsappBadge = $business->getWhatsAppStatusBadge(); @endphp
                                        <span class="px-3 py-1 rounded-full text-sm font-medium {{ $whatsappBadge['class'] }}">
                                            {{ $whatsappBadge['text'] }}
                                        </span>
                                    </div>
                                </div>
                                <div class="px-6 py-4">

                                        <div class="space-y-3 text-sm">
                                            @if($business->whatsapp_provider)
                                                <div class="flex justify-between">
                                                    <span class="text-gray-500">Provider:</span>
                                                    <span class="font-medium">{{ \Plugins\Business\Models\Business::getWhatsAppProviders()[$business->whatsapp_provider] ?? $business->whatsapp_provider }}</span>
                                                </div>
                                            @endif

                                            @if($business->messaging_tier)
                                                <div class="flex justify-between">
                                                    <span class="text-gray-500">Messaging Tier:</span>
                                                    <span class="font-medium">{{ \Plugins\Business\Models\Business::getMessagingTiers()[$business->messaging_tier] ?? $business->messaging_tier }}</span>
                                                </div>
                                            @endif

                                            @if($business->message_quality)
                                                <div class="flex justify-between">
                                                    <span class="text-gray-500">Message Quality:</span>
                                                    <span class="font-medium">{{ $business->message_quality }}</span>
                                                </div>
                                            @endif

                                            @if($business->whatsapp_id)
                                                <div class="flex justify-between">
                                                    <span class="text-gray-500">WhatsApp ID:</span>
                                                    <span class="font-mono text-xs">{{ Str::limit($business->whatsapp_id, 20) }}</span>
                                                </div>
                                            @endif

                                            @if($business->meta_business_id)
                                                <div class="flex justify-between">
                                                    <span class="text-gray-500">Meta Business ID:</span>
                                                    <span class="font-mono text-xs">{{ Str::limit($business->meta_business_id, 20) }}</span>
                                                </div>
                                            @endif

                                            <!-- Verification Status -->
                                            <div class="border-t pt-3 mt-3">
                                                <div class="space-y-2">
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-gray-500">Meta Business:</span>
                                                        @if($business->meta_business_verified)
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                <i class="fas fa-times-circle mr-1"></i>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </div>

                                                    <div class="flex items-center justify-between">
                                                        <span class="text-gray-500">WhatsApp Business:</span>
                                                        @if($business->whatsapp_business_verified)
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                <i class="fas fa-times-circle mr-1"></i>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>

                                            @if($business->whatsapp_verified_at)
                                                <div class="border-t pt-3 mt-3">
                                                    <div class="text-xs text-green-600">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Integration verified on {{ $business->whatsapp_verified_at->format('M j, Y \a\t g:i A') }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endif

                                <!-- Danger Zone -->
                                @if(auth()->user()->hasPermission('manage_businesses'))
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mt-6">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h3 class="text-lg font-medium text-red-900 flex items-center">
                                                    <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                                    Danger Zone
                                                </h3>
                                                <p class="mt-1 text-sm text-red-700">
                                                    Permanently delete this business and all associated data. This action cannot be undone.
                                                </p>
                                            </div>
                                            <form method="POST" action="{{ route('business.destroy', $business) }}"
                                                  onsubmit="return confirm('Are you sure you want to delete this business? This action cannot be undone and will permanently remove all associated data including contacts, documents, and activity history.')"
                                                  class="ml-6">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded border border-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                                    <i class="fas fa-trash mr-2"></i>
                                                    Delete Business
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Contacts Tab -->
                <div id="contacts-content" class="tab-pane hidden" role="tabpanel" aria-labelledby="contacts-tab">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <div class="flex space-x-4">
                            <a href="{{ route('business.contacts.index', $business) }}"
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-list mr-1"></i>
                                View All Contacts
                            </a>
                        </div>
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <a href="{{ route('business.contacts.create', $business) }}"
                               class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>
                                Add Contact
                            </a>
                        @endif
                    </div>

                    @php $recentContacts = $business->contacts()->latest()->limit(5)->get(); @endphp
                    @if($recentContacts->count() > 0)
                        <div class="divide-y divide-gray-200">
                            @foreach($recentContacts as $contact)
                                <div class="px-6 py-4 hover:bg-gray-50">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center mr-4">
                                                <i class="fas fa-user text-gray-600"></i>
                                            </div>
                                            <div>
                                                <div class="flex items-center">
                                                    <p class="text-sm font-medium text-gray-900">{{ $contact->name }}</p>
                                                    @if($contact->is_primary)
                                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Primary
                                                        </span>
                                                    @endif
                                                </div>
                                                <p class="text-sm text-gray-500">{{ $contact->position ?: 'No position specified' }}</p>
                                                <div class="flex items-center space-x-4 mt-1">
                                                    @if($contact->email)
                                                        <span class="text-xs text-gray-400">
                                                            <i class="fas fa-envelope mr-1"></i>{{ $contact->email }}
                                                        </span>
                                                    @endif
                                                    @if($contact->phone)
                                                        <span class="text-xs text-gray-400">
                                                            <i class="fas fa-phone mr-1"></i>{{ $contact->phone }}
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if($contact->email)
                                                <a href="mailto:{{ $contact->email }}"
                                                   class="text-blue-600 hover:text-blue-800" title="Send Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            @endif
                                            @if($contact->phone)
                                                <a href="tel:{{ $contact->phone }}"
                                                   class="text-green-600 hover:text-green-800" title="Call">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            @endif
                                            @if(auth()->user()->hasPermission('manage_businesses'))
                                                <a href="{{ route('business.contacts.edit', [$business, $contact]) }}"
                                                   class="text-indigo-600 hover:text-indigo-800" title="Edit Contact">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if($business->contacts()->count() > 5)
                            <div class="px-6 py-3 bg-gray-50 text-center">
                                <a href="{{ route('business.contacts.index', $business) }}"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    View {{ $business->contacts()->count() - 5 }} more contacts
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No contacts added yet.</p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <a href="{{ route('business.contacts.create', $business) }}"
                                   class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add First Contact
                                </a>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Documents Tab -->
                <div id="documents-content" class="tab-pane hidden" role="tabpanel" aria-labelledby="documents-tab">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <div class="flex space-x-4">
                            <a href="{{ route('business.documents.index', $business) }}"
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-list mr-1"></i>
                                View All Documents
                            </a>
                        </div>
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <a href="{{ route('business.documents.create', $business) }}"
                               class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <i class="fas fa-upload mr-2"></i>
                                Upload Document
                            </a>
                        @endif
                    </div>

                    @php $recentDocuments = $business->documents()->latest()->limit(5)->get(); @endphp
                    @if($recentDocuments->count() > 0)
                        <div class="divide-y divide-gray-200">
                            @foreach($recentDocuments as $document)
                                <div class="px-6 py-4 hover:bg-gray-50">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center mr-4">
                                                @if(str_starts_with($document->mime_type, 'image/'))
                                                    <i class="fas fa-image text-green-600"></i>
                                                @elseif($document->mime_type === 'application/pdf')
                                                    <i class="fas fa-file-pdf text-red-600"></i>
                                                @elseif(str_contains($document->mime_type, 'word'))
                                                    <i class="fas fa-file-word text-blue-600"></i>
                                                @elseif(str_contains($document->mime_type, 'excel') || str_contains($document->mime_type, 'spreadsheet'))
                                                    <i class="fas fa-file-excel text-green-600"></i>
                                                @else
                                                    <i class="fas fa-file text-gray-600"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">{{ $document->original_name }}</p>
                                                <div class="flex items-center space-x-2 mt-1">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {{ $document->document_type_label }}
                                                    </span>
                                                    <span class="text-xs text-gray-500">{{ $document->formatted_file_size }}</span>
                                                </div>
                                                <p class="text-xs text-gray-400 mt-1">
                                                    Uploaded {{ $document->created_at->diffForHumans() }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if(str_starts_with($document->mime_type, 'image/') || $document->mime_type === 'application/pdf')
                                                <a href="{{ route('business.documents.view', [$business, $document]) }}"
                                                   class="text-blue-600 hover:text-blue-800" title="View Document" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            @endif
                                            <a href="{{ route('business.documents.download', [$business, $document]) }}"
                                               class="text-green-600 hover:text-green-800" title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            @if(auth()->user()->hasPermission('manage_businesses'))
                                                <form method="POST" action="{{ route('business.documents.destroy', [$business, $document]) }}"
                                                      onsubmit="return confirm('Are you sure you want to delete this document?')" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-800" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if($business->documents()->count() > 5)
                            <div class="px-6 py-3 bg-gray-50 text-center">
                                <a href="{{ route('business.documents.index', $business) }}"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    View {{ $business->documents()->count() - 5 }} more documents
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No documents uploaded yet.</p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <a href="{{ route('business.documents.create', $business) }}"
                                   class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-upload mr-2"></i>
                                    Upload First Document
                                </a>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Activity Tab - Modern Chat Interface -->
                <div id="activity-content" class="tab-pane hidden" role="tabpanel" aria-labelledby="activity-tab">
                    <!-- Chat Header -->
                    <div class="bg-white border-b border-gray-200 px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-comments text-blue-500 text-lg"></i>
                                    <h4 class="text-lg font-semibold text-gray-900">Business Communication</h4>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ $business->activities()->count() }} messages
                                </span>
                            </div>

                            <!-- Header Controls -->
                            <div class="flex items-center space-x-2">
                                <!-- Search Toggle -->
                                <button type="button" id="search-toggle-btn"
                                        class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>

                                <!-- Filter Toggle -->
                                <button type="button" id="filter-toggle-btn"
                                        class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                                    <i class="fas fa-filter"></i>
                                </button>

                                <!-- Settings -->
                                <button type="button" id="chat-settings-btn"
                                        class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Search Bar (Hidden by default) -->
                        <div id="search-bar" class="hidden mt-3">
                            <div class="relative">
                                <input type="text" id="message-search"
                                       placeholder="Search messages..."
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <button type="button" id="clear-search"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Filter Panel (Hidden by default) -->
                        <div id="filter-panel" class="hidden mt-3 p-3 bg-gray-50 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <!-- Message Type Filter -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Message Type</label>
                                    <select id="message-type-filter" class="w-full text-sm border-gray-300 rounded-md">
                                        <option value="">All Messages</option>
                                        <optgroup label="Business Updates">
                                            <option value="status_change">📊 Status Changes</option>
                                            <option value="lead_to_deal">🔄 Lead → Deal</option>
                                            <option value="deal_to_customer">✅ Deal → Customer</option>
                                            <option value="customer_to_partner">🤝 Customer → Partner</option>
                                            <option value="churned">❌ Churned</option>
                                            <option value="lost">💔 Lost</option>
                                        </optgroup>
                                        <optgroup label="Communications">
                                            <option value="chat">💬 Chat</option>
                                            <option value="email">📧 Email</option>
                                            <option value="phone">☎️ Phone</option>
                                            <option value="meeting">🤝 Meeting</option>
                                            <option value="visit">🏢 Visit</option>
                                            <option value="whatsapp">📱 WhatsApp</option>
                                            <option value="sms">📱 SMS</option>
                                        </optgroup>
                                        <optgroup label="Other">
                                            <option value="comment">💬 Comments</option>
                                            <option value="document">📄 Documents</option>
                                            <option value="system">⚙️ System</option>
                                        </optgroup>
                                    </select>
                                </div>

                                <!-- Date Range Filter -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Date Range</label>
                                    <select id="date-range-filter" class="w-full text-sm border-gray-300 rounded-md">
                                        <option value="">All Time</option>
                                        <option value="today">Today</option>
                                        <option value="yesterday">Yesterday</option>
                                        <option value="week">This Week</option>
                                        <option value="month">This Month</option>
                                        <option value="custom">Custom Range</option>
                                    </select>
                                </div>

                                <!-- User Filter -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">User</label>
                                    <select id="user-filter" class="w-full text-sm border-gray-300 rounded-md">
                                        <option value="">All Users</option>
                                        <option value="system">System Messages</option>
                                        <!-- Users will be populated dynamically -->
                                    </select>
                                </div>
                            </div>

                            <!-- Filter Actions -->
                            <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                                <button type="button" id="clear-filters"
                                        class="text-sm text-gray-600 hover:text-gray-800">
                                    Clear Filters
                                </button>
                                <div class="flex space-x-2">
                                    <button type="button" id="apply-filters"
                                            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                        Apply
                                    </button>
                                </div>
                            </div>
                        </div>

                    <!-- Chat Messages Container -->
                    <div class="flex flex-col bg-gray-50 border border-gray-200 rounded-lg" style="height: 600px;">
                        <!-- Messages Area -->
                        <div id="messages-container" class="flex-1 overflow-y-auto p-6 space-y-4">
                            <!-- Loading State -->
                            <div id="messages-loading" class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                                    <p class="text-sm text-gray-500">Loading messages...</p>
                                </div>
                            </div>

                            <!-- Messages will be loaded here dynamically -->
                            <div id="messages-list" class="hidden space-y-3">
                                <!-- Sample message structure (will be replaced by AJAX) -->
                            </div>

                            <!-- Empty State -->
                            <div id="messages-empty" class="hidden flex items-center justify-center h-full">
                                <div class="text-center">
                                    <i class="fas fa-comments text-gray-300 text-4xl mb-3"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-1">No messages yet</h3>
                                    <p class="text-sm text-gray-500">Start the conversation by sending a message below</p>
                                </div>
                            </div>

                            <!-- Load More Button -->
                            <div id="load-more-container" class="hidden text-center py-3">
                                <button type="button" id="load-more-btn"
                                        class="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-up mr-1"></i>
                                    Load older messages
                                </button>
                            </div>
                        </div>

                        <!-- Typing Indicator -->
                        <div id="typing-indicator" class="hidden px-4 py-2 border-t border-gray-200 bg-white">
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                </div>
                                <span>Someone is typing...</span>
                            </div>
                        </div>

                        <!-- Message Input Area -->
                        <div class="border-t border-gray-200 bg-white p-6">
                            <!-- File Preview Area -->
                            <div id="file-preview-area" class="hidden mb-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Attachments</span>
                                    <button type="button" id="clear-attachments"
                                            class="text-sm text-red-600 hover:text-red-800">
                                        Clear all
                                    </button>
                                </div>
                                <div id="file-previews" class="space-y-2">
                                    <!-- File previews will be added here -->
                                </div>
                            </div>

                            <!-- Message Composition -->
                            <form id="message-form" class="space-y-3">
                                @csrf
                                <input type="hidden" id="editing-message-id" value="">
                                <input type="hidden" id="reply-to-message-id" value="">

                                <!-- Reply Preview -->
                                <div id="reply-preview" class="hidden p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-reply text-blue-600"></i>
                                            <span class="text-sm font-medium text-blue-800">Replying to</span>
                                            <span id="reply-to-user" class="text-sm text-blue-600"></span>
                                        </div>
                                        <button type="button" id="cancel-reply"
                                                class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <p id="reply-to-content" class="text-sm text-gray-600 mt-1 truncate"></p>
                                </div>

                                <!-- Edit Preview -->
                                <div id="edit-preview" class="hidden p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-edit text-yellow-600"></i>
                                            <span class="text-sm font-medium text-yellow-800">Editing message</span>
                                        </div>
                                        <button type="button" id="cancel-edit"
                                                class="text-yellow-600 hover:text-yellow-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Message Input -->
                                <div class="flex items-end space-x-4">
                                    <!-- Message Type Selector -->
                                    <div class="flex-shrink-0">
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Type</label>
                                        <select id="message-type-selector" name="message_type"
                                                class="text-sm border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 py-2 px-3">
                                            <option value="comment">💬 Comment</option>
                                            <option value="chat">💬 Chat</option>
                                            <option value="email">📧 Email</option>
                                            <option value="phone">☎️ Phone</option>
                                            <option value="meeting">🤝 Meeting</option>
                                            <option value="visit">🏢 Visit</option>
                                            <option value="whatsapp">📱 WhatsApp</option>
                                            <option value="sms">📱 SMS</option>
                                            <option value="reminder">⏰ Reminder</option>
                                            <option value="follow_up">🔄 Follow-up</option>
                                        </select>
                                    </div>

                                    <!-- Message Input Field -->
                                    <div class="flex-1 relative">
                                        <div class="border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 shadow-sm">
                                            <textarea id="message-input" name="message" rows="2"
                                                      class="w-full px-4 py-3 border-0 rounded-lg resize-none focus:ring-0 focus:outline-none text-sm"
                                                      placeholder="Type your message..."
                                                      style="min-height: 60px; max-height: 150px;"></textarea>

                                            <!-- Input Toolbar -->
                                            <div class="flex items-center justify-between px-3 py-2 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                                                <div class="flex items-center space-x-2">
                                                    <!-- File Upload -->
                                                    <input type="file" id="file-input" multiple class="hidden"
                                                           accept="image/*,application/pdf,.doc,.docx,.txt,.xlsx,.pptx">
                                                    <button type="button" onclick="document.getElementById('file-input').click()"
                                                            class="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors">
                                                        <i class="fas fa-paperclip"></i>
                                                    </button>

                                                    <!-- Emoji Picker -->
                                                    <button type="button" id="emoji-btn"
                                                            class="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors">
                                                        <i class="fas fa-smile"></i>
                                                    </button>

                                                    <!-- Formatting Options -->
                                                    <button type="button" id="format-bold"
                                                            class="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors">
                                                        <i class="fas fa-bold"></i>
                                                    </button>
                                                    <button type="button" id="format-italic"
                                                            class="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors">
                                                        <i class="fas fa-italic"></i>
                                                    </button>
                                                </div>

                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs text-gray-400" id="char-count">0/1000</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Send Button -->
                                    <div class="flex-shrink-0">
                                        <button type="submit" id="send-btn"
                                                class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm font-medium">
                                            <i class="fas fa-paper-plane mr-2"></i>
                                            <span class="hidden sm:inline">Send</span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Add Activity Modal -->
<div id="add-activity-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add Manual Activity</h3>
                <button type="button" onclick="closeAddActivityModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-activity-form">
                @csrf
                <div class="mb-4">
                    <label for="activity_type" class="block text-sm font-medium text-gray-700 mb-2">Activity Type</label>
                    <select id="activity_type" name="type" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <option value="manual_log">Manual Log</option>
                        <option value="comment">Comment</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="activity_title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" id="activity_title" name="title" required
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="Enter activity title">
                </div>
                <div class="mb-4">
                    <label for="activity_description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <div id="activity_description_editor" style="height: 120px;"></div>
                    <input type="hidden" id="activity_description" name="description">
                </div>
                <div class="mb-4">
                    <label for="activity_severity" class="block text-sm font-medium text-gray-700 mb-2">Severity</label>
                    <select id="activity_severity" name="severity" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="important">Important</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="activity_category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select id="activity_category" name="category" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <option value="user_action">User Action</option>
                        <option value="business_event">Business Event</option>
                        <option value="communication">Communication</option>
                        <option value="system">System</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeAddActivityModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Add Activity
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Notification Settings Modal -->
<div id="notification-settings-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Notification Preferences</h3>
                <button type="button" onclick="closeNotificationSettings()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="notification-preferences-form">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Notification Channels -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">Notification Channels</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" id="email_enabled" name="email_enabled" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">Email notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="sms_enabled" name="sms_enabled" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">SMS notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">WhatsApp notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="in_app_enabled" name="in_app_enabled" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">In-app notifications</span>
                            </label>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">Contact Information</h4>
                        <div class="space-y-3">
                            <div>
                                <label for="notification_email" class="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" id="notification_email" name="notification_email"
                                       class="mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </div>
                            <div>
                                <label for="notification_phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                <input type="tel" id="notification_phone" name="notification_phone"
                                       class="mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </div>
                            <div>
                                <label for="notification_whatsapp" class="block text-sm font-medium text-gray-700">WhatsApp</label>
                                <input type="tel" id="notification_whatsapp" name="notification_whatsapp"
                                       class="mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Frequency and Severity -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="frequency" class="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                        <select id="frequency" name="frequency" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="immediate">Immediate</option>
                            <option value="hourly">Hourly digest</option>
                            <option value="daily">Daily digest</option>
                            <option value="weekly">Weekly digest</option>
                            <option value="never">Never</option>
                        </select>
                    </div>
                    <div>
                        <label for="min_severity" class="block text-sm font-medium text-gray-700 mb-2">Minimum Severity</label>
                        <select id="min_severity" name="min_severity" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="info">All activities</option>
                            <option value="warning">Warning and above</option>
                            <option value="important">Important and above</option>
                            <option value="critical">Critical only</option>
                        </select>
                    </div>
                </div>

                <!-- Activity Types -->
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Activity Types to Notify</h4>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="comment" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Comments</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="document_upload" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Document Uploads</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="status_change" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Status Changes</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="tag_assignment" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Tag Changes</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="product_assignment" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Product Changes</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="activity_types[]" value="mention" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Mentions</span>
                        </label>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeNotificationSettings()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Save Preferences
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


            </div>
        </div>
    </div>
</div>



<script>
function switchTab(tabName) {
    // Hide all tab panes
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabPanes.forEach(pane => {
        pane.classList.add('hidden');
    });

    // Remove active state from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        button.setAttribute('aria-selected', 'false');
    });

    // Show selected tab pane
    const selectedPane = document.getElementById(tabName + '-content');
    if (selectedPane) {
        selectedPane.classList.remove('hidden');
    }

    // Add active state to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        selectedButton.classList.add('border-blue-500', 'text-blue-600');
        selectedButton.setAttribute('aria-selected', 'true');
    }

    // Update URL hash
    window.location.hash = tabName;

    // Handle specific tab initialization
    if (tabName === 'activity') {
        // Initialize chat manager if not already done
        if (window.chatManager) {
            window.chatManager.loadMessages();
        } else {
            // Will be initialized by the overridden switchTab function below
            console.log('ChatManager will be initialized');
        }
    }
}

// Initialize tabs on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL to determine which tab to show
    const hash = window.location.hash.substring(1);
    if (hash === 'contacts') {
        switchTab('contacts');
    } else if (hash === 'documents') {
        switchTab('documents');
    } else if (hash === 'activity') {
        switchTab('activity');
    } else {
        // Default to business-info tab
        switchTab('business-info');
    }

    // Add event listeners for responsive behavior and keyboard navigation
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.id.replace('-tab', '');
            // Update URL hash without scrolling
            history.replaceState(null, null, '#' + tabName);
        });

        // Add keyboard navigation
        button.addEventListener('keydown', function(e) {
            const tabName = this.id.replace('-tab', '');

            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                e.preventDefault();
                const currentIndex = Array.from(tabButtons).indexOf(this);
                let nextIndex;

                if (e.key === 'ArrowLeft') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : tabButtons.length - 1;
                } else {
                    nextIndex = currentIndex < tabButtons.length - 1 ? currentIndex + 1 : 0;
                }

                const nextButton = tabButtons[nextIndex];
                const nextTabName = nextButton.id.replace('-tab', '');

                switchTab(nextTabName);
                nextButton.focus();
                history.replaceState(null, null, '#' + nextTabName);
            }
        });
    });
});

// Handle browser back/forward navigation
window.addEventListener('hashchange', function() {
    const hash = window.location.hash.substring(1);
    if (hash === 'business-info' || hash === 'contacts' || hash === 'documents' || hash === 'activity') {
        switchTab(hash);
    }
});

// Activity Tab Functionality
let currentActivityPage = 1;
let isLoadingActivities = false;
let hasMoreActivities = true;
let autoRefreshInterval = null;
let lastActivityId = null;

// Load activities when activity tab is shown
function loadActivities(page = 1, append = false) {
    if (isLoadingActivities) return;

    isLoadingActivities = true;
    const feedContainer = document.getElementById('activity-feed');
    const loadMoreContainer = document.getElementById('load-more-container');

    if (!append) {
        feedContainer.innerHTML = '<div class="px-6 py-8 text-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-sm text-gray-500">Loading activities...</p></div>';
    }

    const typeFilter = document.getElementById('activity-type-filter').value;
    const severityFilter = document.getElementById('activity-severity-filter').value;
    const searchQuery = document.getElementById('activity-search').value.trim();

    const params = new URLSearchParams({
        page: page,
        per_page: 20
    });

    if (typeFilter) {
        params.append('type', typeFilter);
    }

    if (severityFilter) {
        params.append('severity', severityFilter);
    }

    if (searchQuery) {
        params.append('search', searchQuery);
    }

    fetch(`{{ route('business.activities.index', $business) }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (!append) {
                    feedContainer.innerHTML = '';
                }

                if (data.data.length === 0 && page === 1) {
                    feedContainer.innerHTML = `
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No activities yet.</p>
                            <p class="text-gray-400 text-sm">Activity will appear here as you interact with this business.</p>
                        </div>
                    `;
                } else {
                    data.data.forEach(activity => {
                        feedContainer.appendChild(createActivityElement(activity));
                    });

                    // Track the latest activity ID for real-time updates
                    if (data.data.length > 0 && page === 1) {
                        lastActivityId = data.data[0].id;
                    }
                }

                currentActivityPage = data.pagination.current_page;
                hasMoreActivities = data.pagination.has_more;

                if (hasMoreActivities) {
                    loadMoreContainer.classList.remove('hidden');
                } else {
                    loadMoreContainer.classList.add('hidden');
                }
            }
        })
        .catch(error => {
            console.error('Error loading activities:', error);
            feedContainer.innerHTML = '<div class="px-6 py-8 text-center text-red-600">Error loading activities. Please try again.</div>';
        })
        .finally(() => {
            isLoadingActivities = false;
        });
}

// Create activity element
function createActivityElement(activity) {
    // Get activity type info with fallbacks
    const typeInfo = getActivityTypeInfo(activity.type);
    const severityInfo = getActivitySeverityInfo(activity.severity);

    const div = document.createElement('div');
    div.className = 'px-6 py-4';
    div.innerHTML = `
        <div class="flex space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 rounded-full bg-${typeInfo.color}-100 flex items-center justify-center">
                    <i class="${typeInfo.icon} text-${typeInfo.color}-600 text-sm"></i>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900">${activity.title}</p>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${severityInfo.badge}">
                            ${severityInfo.label}
                        </span>
                        <span class="text-xs text-gray-500">${formatDate(activity.created_at)}</span>
                    </div>
                </div>
                ${activity.description ? `<p class="mt-1 text-sm text-gray-600">${activity.description}</p>` : ''}
                ${activity.user ? `<p class="mt-1 text-xs text-gray-500">by ${activity.user.name}</p>` : ''}

                <!-- Comments -->
                ${activity.comments && activity.comments.length > 0 ? `
                    <div class="mt-3 space-y-2">
                        ${activity.comments.map(comment => `
                            <div class="bg-gray-50 rounded-lg p-3">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-900">${comment.content_html || comment.content}</p>
                                        <p class="mt-1 text-xs text-gray-500">
                                            ${comment.user.name} • ${formatDate(comment.created_at)}
                                            ${comment.is_edited ? ' • edited' : ''}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                <!-- Attachments -->
                ${activity.attachments && activity.attachments.length > 0 ? `
                    <div class="mt-3">
                        <h5 class="text-xs font-medium text-gray-700 mb-2">Attachments (${activity.attachments.length})</h5>
                        <div class="space-y-2">
                            ${activity.attachments.map(attachment => `
                                <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="${getFileTypeIcon(attachment.original_filename)} text-blue-600 text-lg"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate">${attachment.original_filename}</p>
                                            <p class="text-xs text-gray-500">${formatFileSize(attachment.file_size)} • ${getFileTypeLabel(attachment.original_filename)}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        ${isImageFile(attachment.original_filename) ? `
                                            <button onclick="previewFile('/business/{{ $business->id }}/activities/attachments/${attachment.id}/download')"
                                                    class="text-blue-600 hover:text-blue-800 text-sm" title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        ` : ''}
                                        <a href="/business/{{ $business->id }}/activities/attachments/${attachment.id}/download"
                                           class="text-blue-600 hover:text-blue-800 text-sm" title="Download">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
    return div;
}

// Get activity type info
function getActivityTypeInfo(type) {
    const typeMap = {
        'comment': { icon: 'fas fa-comment', color: 'blue' },
        'document_upload': { icon: 'fas fa-upload', color: 'green' },
        'document_delete': { icon: 'fas fa-trash', color: 'red' },
        'status_change': { icon: 'fas fa-exchange-alt', color: 'purple' },
        'tag_assignment': { icon: 'fas fa-tag', color: 'indigo' },
        'tag_removal': { icon: 'fas fa-tag', color: 'gray' },
        'product_assignment': { icon: 'fas fa-box', color: 'yellow' },
        'product_removal': { icon: 'fas fa-box', color: 'gray' },
        'user_assignment': { icon: 'fas fa-user-plus', color: 'green' },
        'user_removal': { icon: 'fas fa-user-minus', color: 'red' },
        'business_update': { icon: 'fas fa-edit', color: 'blue' },
        'system_log': { icon: 'fas fa-cog', color: 'gray' },
        'manual_log': { icon: 'fas fa-pencil-alt', color: 'blue' },
        'file_share': { icon: 'fas fa-share', color: 'green' },
        'mention': { icon: 'fas fa-at', color: 'purple' }
    };

    return typeMap[type] || { icon: 'fas fa-circle', color: 'gray' };
}

// Get activity severity info
function getActivitySeverityInfo(severity) {
    const severityMap = {
        'info': { label: 'Info', badge: 'bg-blue-100 text-blue-800' },
        'warning': { label: 'Warning', badge: 'bg-yellow-100 text-yellow-800' },
        'important': { label: 'Important', badge: 'bg-orange-100 text-orange-800' },
        'critical': { label: 'Critical', badge: 'bg-red-100 text-red-800' }
    };

    return severityMap[severity] || { label: 'Unknown', badge: 'bg-gray-100 text-gray-800' };
}

// Get file type icon
function getFileTypeIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'pdf': 'fas fa-file-pdf',
        'doc': 'fas fa-file-word',
        'docx': 'fas fa-file-word',
        'xls': 'fas fa-file-excel',
        'xlsx': 'fas fa-file-excel',
        'ppt': 'fas fa-file-powerpoint',
        'pptx': 'fas fa-file-powerpoint',
        'jpg': 'fas fa-file-image',
        'jpeg': 'fas fa-file-image',
        'png': 'fas fa-file-image',
        'gif': 'fas fa-file-image',
        'txt': 'fas fa-file-alt',
        'zip': 'fas fa-file-archive',
        'rar': 'fas fa-file-archive'
    };

    return iconMap[extension] || 'fas fa-file';
}

// Get file type label
function getFileTypeLabel(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const labelMap = {
        'pdf': 'PDF Document',
        'doc': 'Word Document',
        'docx': 'Word Document',
        'xls': 'Excel Spreadsheet',
        'xlsx': 'Excel Spreadsheet',
        'ppt': 'PowerPoint Presentation',
        'pptx': 'PowerPoint Presentation',
        'jpg': 'JPEG Image',
        'jpeg': 'JPEG Image',
        'png': 'PNG Image',
        'gif': 'GIF Image',
        'txt': 'Text File',
        'zip': 'ZIP Archive',
        'rar': 'RAR Archive'
    };

    return labelMap[extension] || 'File';
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check if file is an image
function isImageFile(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension);
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Load more activities button
    document.getElementById('load-more-btn').addEventListener('click', function() {
        if (hasMoreActivities) {
            loadActivities(currentActivityPage + 1, true);
        }
    });

    // Activity filters
    document.getElementById('activity-type-filter').addEventListener('change', function() {
        currentActivityPage = 1;
        loadActivities(1, false);
    });

    const activitySeverityFilter = document.getElementById('activity-severity-filter');
    if (activitySeverityFilter) {
        activitySeverityFilter.addEventListener('change', function() {
            currentActivityPage = 1;
            loadActivities(1, false);
        });
    }

    // Activity search with debounce
    let searchTimeout;
    const activitySearch = document.getElementById('activity-search');
    if (activitySearch) {
        activitySearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentActivityPage = 1;
            loadActivities(1, false);
        }, 500); // 500ms debounce
        });
    }

    // Add comment form
    const addCommentForm = document.getElementById('add-comment-form');
    if (addCommentForm) {
        addCommentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addComment();
        });
    }

    // Attachment input change handler
    const commentAttachment = document.getElementById('comment-attachment');
    if (commentAttachment) {
        commentAttachment.addEventListener('change', function() {
            updateAttachmentDisplay();
        });
    }
});

// Override switchTab to load activities when activity tab is shown
const originalSwitchTab = switchTab;
switchTab = function(tabName) {
    originalSwitchTab(tabName);

    if (tabName === 'activity') {
        // Initialize chat manager when activity tab is first shown
        setTimeout(() => {
            if (window.chatManager) {
                window.chatManager.loadMessages();
            } else {
                // Initialize chat manager if not already done
                window.chatManager = new ChatManager();
            }
        }, 100);
    }
};

// Add comment function
function addComment() {
    const form = document.getElementById('add-comment-form');
    const contentTextarea = document.getElementById('comment-content');
    const content = contentTextarea.value.trim();
    const attachmentInput = document.getElementById('comment-attachment');
    const files = attachmentInput.files;

    if (!content && files.length === 0) {
        alert('Please enter a comment or attach a file');
        return;
    }

    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Adding...';
    submitBtn.disabled = true;

    // Determine activity type based on content
    const activityType = files.length > 0 ? 'file_share' : 'comment';
    const title = files.length > 0 ?
        `Shared ${files.length} file${files.length > 1 ? 's' : ''}` :
        'New comment added';

    // Create a new activity
    fetch(`{{ route('business.activities.store', $business) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            type: activityType,
            category: files.length > 0 ? 'document' : 'communication',
            severity: 'info',
            title: title,
            description: content || `Shared files: ${Array.from(files).map(f => f.name).join(', ')}`
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const activityId = data.activity.id;
            const promises = [];

            // Add comment if there's content
            if (content) {
                promises.push(
                    fetch(`/business/{{ $business->id }}/activities/${activityId}/comments`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            content: content
                        })
                    })
                );
            }

            // Upload files if any
            if (files.length > 0) {
                Array.from(files).forEach(file => {
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('is_public', 'false');

                    promises.push(
                        fetch(`/business/{{ $business->id }}/activities/${activityId}/attachments`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: formData
                        })
                    );
                });
            }

            return Promise.all(promises);
        } else {
            throw new Error(data.message || 'Failed to create activity');
        }
    })
    .then(responses => {
        // Check if all requests were successful
        const allSuccessful = responses.every(response => response.ok);
        if (allSuccessful) {
            contentTextarea.value = '';
            attachmentInput.value = '';
            updateAttachmentDisplay();
            // Reload activities to show the new comment/files
            loadActivities(1, false);
        } else {
            throw new Error('Some operations failed');
        }
    })
    .catch(error => {
        console.error('Error adding comment:', error);
        alert('Failed to add comment: ' + error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Update attachment display
function updateAttachmentDisplay() {
    const attachmentInput = document.getElementById('comment-attachment');
    const files = attachmentInput.files;

    let existingDisplay = document.getElementById('attachment-display');
    if (existingDisplay) {
        existingDisplay.remove();
    }

    if (files.length > 0) {
        const display = document.createElement('div');
        display.id = 'attachment-display';
        display.className = 'mt-2 flex flex-wrap gap-2';

        Array.from(files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
            fileItem.innerHTML = `
                <i class="fas fa-file mr-1"></i>
                ${file.name}
                <button type="button" onclick="removeAttachment(${index})" class="ml-2 text-blue-600 hover:text-blue-800">
                    <i class="fas fa-times"></i>
                </button>
            `;
            display.appendChild(fileItem);
        });

        attachmentInput.parentNode.insertBefore(display, attachmentInput.nextSibling);
    }
}

// Remove attachment
function removeAttachment(index) {
    const attachmentInput = document.getElementById('comment-attachment');
    const dt = new DataTransfer();

    Array.from(attachmentInput.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    attachmentInput.files = dt.files;
    updateAttachmentDisplay();
}

// File preview function
function previewFile(previewUrl) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="relative max-w-4xl max-h-full p-4">
            <button onclick="this.parentElement.parentElement.remove()"
                    class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
                <i class="fas fa-times text-2xl"></i>
            </button>
            <img src="${previewUrl}" class="max-w-full max-h-full object-contain" alt="File preview">
        </div>
    `;
    document.body.appendChild(modal);

    // Close on click outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            modal.remove();
        }
    }, { once: true });
}

// Auto-refresh functionality
function startAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    autoRefreshInterval = setInterval(() => {
        checkForNewActivities();
    }, 30000); // Check every 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// Check for new activities
function checkForNewActivities() {
    if (isLoadingActivities || !lastActivityId) return;

    const params = new URLSearchParams({
        page: 1,
        per_page: 5,
        since: lastActivityId
    });

    fetch(`{{ route('business.activities.index', $business) }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                showNewActivityNotification(data.data.length);
            }
        })
        .catch(error => {
            console.error('Error checking for new activities:', error);
        });
}

// Show notification for new activities
function showNewActivityNotification(count) {
    // Remove existing notification
    const existingNotification = document.getElementById('new-activity-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create new notification
    const notification = document.createElement('div');
    notification.id = 'new-activity-notification';
    notification.className = 'fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 cursor-pointer';
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i class="fas fa-bell"></i>
            <span>${count} new activit${count > 1 ? 'ies' : 'y'}</span>
            <i class="fas fa-times ml-2"></i>
        </div>
    `;

    // Add click handler to refresh and dismiss
    notification.addEventListener('click', function() {
        loadActivities(1, false);
        notification.remove();
    });

    document.body.appendChild(notification);

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 10000);
}

// Activity refresh button
function addRefreshButton() {
    const activityHeader = document.querySelector('#activity-content .flex.justify-between.items-center');
    if (activityHeader && !document.getElementById('refresh-activities-btn')) {
        const refreshBtn = document.createElement('button');
        refreshBtn.id = 'refresh-activities-btn';
        refreshBtn.className = 'inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-1"></i>Refresh';
        refreshBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            icon.classList.add('fa-spin');
            loadActivities(1, false);
            setTimeout(() => {
                icon.classList.remove('fa-spin');
            }, 1000);
        });

        activityHeader.querySelector('.flex.space-x-2').appendChild(refreshBtn);
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Only handle shortcuts when activity tab is active
    if (!document.getElementById('activity-tab').classList.contains('border-blue-500')) {
        return;
    }

    // Ctrl/Cmd + R: Refresh activities
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        loadActivities(1, false);
    }

    // Ctrl/Cmd + F: Focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        document.getElementById('activity-search').focus();
    }

    // Escape: Clear search
    if (e.key === 'Escape') {
        const searchInput = document.getElementById('activity-search');
        if (searchInput === document.activeElement) {
            searchInput.value = '';
            searchInput.blur();
            loadActivities(1, false);
        }
    }
});

// Page visibility API for pausing auto-refresh when tab is not visible
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        // Only restart if activity tab is active
        if (document.getElementById('activity-tab').classList.contains('border-blue-500')) {
            startAutoRefresh();
        }
    }
});

// Add Activity Modal functions
function openAddActivityModal() {
    document.getElementById('add-activity-modal').classList.remove('hidden');
}

function closeAddActivityModal() {
    document.getElementById('add-activity-modal').classList.add('hidden');
    document.getElementById('add-activity-form').reset();
}

// Notification Settings Modal functions
function openNotificationSettings() {
    document.getElementById('notification-settings-modal').classList.remove('hidden');
    loadNotificationPreferences();
}

function closeNotificationSettings() {
    document.getElementById('notification-settings-modal').classList.add('hidden');
}

// Load notification preferences
function loadNotificationPreferences() {
    fetch(`{{ route('business.notifications.preferences.show', $business) }}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateNotificationForm(data.preferences);
            }
        })
        .catch(error => {
            console.error('Error loading notification preferences:', error);
        });
}

// Populate notification form
function populateNotificationForm(preferences) {
    document.getElementById('email_enabled').checked = preferences.email_enabled;
    document.getElementById('sms_enabled').checked = preferences.sms_enabled;
    document.getElementById('whatsapp_enabled').checked = preferences.whatsapp_enabled;
    document.getElementById('in_app_enabled').checked = preferences.in_app_enabled;
    document.getElementById('frequency').value = preferences.frequency;
    document.getElementById('min_severity').value = preferences.min_severity;
    document.getElementById('notification_email').value = preferences.notification_email || '';
    document.getElementById('notification_phone').value = preferences.notification_phone || '';
    document.getElementById('notification_whatsapp').value = preferences.notification_whatsapp || '';

    // Handle activity types checkboxes
    const activityTypes = preferences.activity_types || [];
    document.querySelectorAll('input[name="activity_types[]"]').forEach(checkbox => {
        checkbox.checked = activityTypes.includes(checkbox.value);
    });
}

// Add activity form submission
document.addEventListener('DOMContentLoaded', function() {
    // Add activity form
    document.getElementById('add-activity-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Adding...';
        submitBtn.disabled = true;

        fetch(`{{ route('business.activities.store', $business) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeAddActivityModal();
                loadActivities(1, false);
            } else {
                throw new Error(data.message || 'Failed to add activity');
            }
        })
        .catch(error => {
            console.error('Error adding activity:', error);
            alert('Failed to add activity: ' + error.message);
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Notification preferences form
    document.getElementById('notification-preferences-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Handle checkboxes
        data.email_enabled = formData.has('email_enabled');
        data.sms_enabled = formData.has('sms_enabled');
        data.whatsapp_enabled = formData.has('whatsapp_enabled');
        data.in_app_enabled = formData.has('in_app_enabled');

        // Handle activity types array
        data.activity_types = formData.getAll('activity_types[]');

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Saving...';
        submitBtn.disabled = true;

        fetch(`{{ route('business.notifications.update-preferences', $business) }}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeNotificationSettings();
                alert('Notification preferences saved successfully!');
            } else {
                throw new Error(data.message || 'Failed to save preferences');
            }
        })
        .catch(error => {
            console.error('Error saving preferences:', error);
            alert('Failed to save preferences: ' + error.message);
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});

// Initialize Rich Text Editor
let activityDescriptionQuill = null;

function initializeRichTextEditor() {
    if (activityDescriptionQuill) {
        return; // Already initialized
    }

    activityDescriptionQuill = new Quill('#activity_description_editor', {
        theme: 'snow',
        placeholder: 'Enter activity description...',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                ['link'],
                [{ 'align': [] }],
                ['clean']
            ]
        }
    });

    // Update hidden input when content changes
    activityDescriptionQuill.on('text-change', function() {
        const html = activityDescriptionQuill.root.innerHTML;
        const text = activityDescriptionQuill.getText().trim();
        document.getElementById('activity_description').value = text ? html : '';
    });
}

// Override openAddActivityModal to initialize editor
const originalOpenAddActivityModal = openAddActivityModal;
openAddActivityModal = function() {
    originalOpenAddActivityModal();
    // Initialize editor after modal is shown
    setTimeout(() => {
        initializeRichTextEditor();
    }, 100);
};

// Override closeAddActivityModal to reset editor
const originalCloseAddActivityModal = closeAddActivityModal;
closeAddActivityModal = function() {
    if (activityDescriptionQuill) {
        activityDescriptionQuill.setContents([]);
    }
    originalCloseAddActivityModal();
};

// ===== UNIFIED BILLING MANAGEMENT =====

document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggle-billing-form');
    const billingForm = document.getElementById('billing-creation-form');
    const cancelBtn = document.getElementById('cancel-billing-form');
    const unifiedForm = document.getElementById('unified-billing-form');

    if (toggleBtn && billingForm && cancelBtn && unifiedForm) {
        // Toggle form visibility
        toggleBtn.addEventListener('click', function() {
            billingForm.classList.toggle('hidden');
            if (!billingForm.classList.contains('hidden')) {
                document.getElementById('document_type').focus();
            }
        });

        // Cancel form
        cancelBtn.addEventListener('click', function() {
            billingForm.classList.add('hidden');
            unifiedForm.reset();
        });

        // Handle form submission
        unifiedForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(unifiedForm);

            // Show loading state
            const submitBtn = unifiedForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Creating...';
            submitBtn.disabled = true;

            // Simulate API call (replace with actual endpoint)
            setTimeout(() => {
                // Hide form and reset
                billingForm.classList.add('hidden');
                unifiedForm.reset();

                // Show success message
                showNotification('Billing document created successfully!', 'success');

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 1000);
        });
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});

// ===== CHAT-STYLE ACTIVITY ENHANCEMENTS =====

document.addEventListener('DOMContentLoaded', function() {
    const chatEditor = document.getElementById('chat-editor');
    const sendBtn = document.getElementById('send-message-btn');
    const fileInput = document.getElementById('chat-file-input');
    const filePreviewArea = document.getElementById('file-preview-area');

    if (chatEditor && sendBtn) {
        // Enable/disable send button based on content
        function updateSendButton() {
            const hasContent = chatEditor.textContent.trim().length > 0 ||
                              (fileInput && fileInput.files.length > 0);
            sendBtn.disabled = !hasContent;
        }

        // Handle content changes
        chatEditor.addEventListener('input', updateSendButton);

        // Handle file selection
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                updateSendButton();
                showFilePreview();
            });
        }

        // Show file preview
        function showFilePreview() {
            if (!filePreviewArea || !fileInput) return;

            filePreviewArea.innerHTML = '';
            if (fileInput.files.length > 0) {
                filePreviewArea.classList.remove('hidden');
                Array.from(fileInput.files).forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'flex items-center justify-between bg-gray-100 p-2 rounded';
                    fileItem.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-file mr-2 text-gray-500"></i>
                            <span class="text-sm">${file.name}</span>
                            <span class="text-xs text-gray-500 ml-2">(${(file.size / 1024).toFixed(1)} KB)</span>
                        </div>
                        <button type="button" onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    filePreviewArea.appendChild(fileItem);
                });
            } else {
                filePreviewArea.classList.add('hidden');
            }
        }

        // Remove file from selection
        window.removeFile = function(index) {
            if (!fileInput) return;
            const dt = new DataTransfer();
            Array.from(fileInput.files).forEach((file, i) => {
                if (i !== index) dt.items.add(file);
            });
            fileInput.files = dt.files;
            showFilePreview();
            updateSendButton();
        };

        // Initial state
        updateSendButton();
    }
});

// Chat Message Rendering and Management
class ChatManager {
    constructor() {
        this.businessId = {{ $business->id ?? 'null' }};
        this.currentUser = {{ auth()->id() ?? 'null' }};
        this.messagesContainer = document.getElementById('messages-list');
        this.messagesLoading = document.getElementById('messages-loading');
        this.messagesEmpty = document.getElementById('messages-empty');
        this.loadMoreContainer = document.getElementById('load-more-container');
        this.messageForm = document.getElementById('message-form');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');

        this.messages = [];
        this.currentPage = 1;
        this.hasMoreMessages = true;
        this.isLoading = false;
        this.editingMessageId = null;
        this.replyToMessageId = null;

        // Only initialize if we have the required elements
        if (this.messagesContainer && this.messageForm && this.messageInput) {
            console.log('ChatManager: Initializing with all required elements');
            this.init();
        } else {
            console.warn('ChatManager: Required DOM elements not found', {
                messagesContainer: !!this.messagesContainer,
                messageForm: !!this.messageForm,
                messageInput: !!this.messageInput
            });
        }
    }

    init() {
        this.loadMessages();
        this.setupEventListeners();
        this.setupAutoResize();
        this.setupEmojiPicker();
    }

    setupEventListeners() {
        // Message form submission
        if (this.messageForm) {
            this.messageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendMessage();
            });
        }

        // Auto-resize textarea
        if (this.messageInput) {
            this.messageInput.addEventListener('input', () => {
                this.autoResizeTextarea();
                this.updateCharCount();
            });
        }

        // Load more messages
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreMessages();
            });
        }

        // Search functionality
        const messageSearch = document.getElementById('message-search');
        if (messageSearch) {
            messageSearch.addEventListener('input', (e) => {
                this.searchMessages(e.target.value);
            });
        }

        // Filter functionality
        const applyFilters = document.getElementById('apply-filters');
        if (applyFilters) {
            applyFilters.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        // Toggle search and filter panels
        const searchToggleBtn = document.getElementById('search-toggle-btn');
        if (searchToggleBtn) {
            searchToggleBtn.addEventListener('click', () => {
                this.toggleSearchBar();
            });
        }

        const filterToggleBtn = document.getElementById('filter-toggle-btn');
        if (filterToggleBtn) {
            filterToggleBtn.addEventListener('click', () => {
                this.toggleFilterPanel();
            });
        }
    }

    setupAutoResize() {
        // Auto-resize functionality is handled in the input event listener
        // This method exists to satisfy the init() call
        if (this.messageInput) {
            // Set initial height
            this.messageInput.style.height = 'auto';
            this.messageInput.style.minHeight = '40px';
            this.messageInput.style.maxHeight = '120px';
        }
    }

    autoResizeTextarea() {
        if (this.messageInput) {
            this.messageInput.style.height = 'auto';
            this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
        }
    }

    updateCharCount() {
        const charCount = document.getElementById('char-count');
        if (charCount && this.messageInput) {
            const length = this.messageInput.value.length;
            charCount.textContent = `${length}/1000`;
            charCount.className = length > 900 ? 'text-xs text-red-400' : 'text-xs text-gray-400';
        }
    }

    async loadMessages(page = 1) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        try {
            const response = await fetch(`/business/${this.businessId}/activities?page=${page}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) throw new Error('Failed to load messages');

            const data = await response.json();

            if (page === 1) {
                this.messages = data.data;
                this.renderMessages();
            } else {
                this.messages = [...data.data, ...this.messages];
                this.prependMessages(data.data);
            }

            this.hasMoreMessages = data.has_more_pages;
            this.currentPage = page;

            this.hideLoading();
            this.updateLoadMoreButton();

        } catch (error) {
            console.error('Error loading messages:', error);
            this.showError('Failed to load messages');
        } finally {
            this.isLoading = false;
        }
    }

    renderMessages() {
        if (this.messages.length === 0) {
            this.showEmpty();
            return;
        }

        this.messagesContainer.innerHTML = '';
        this.messages.forEach(message => {
            this.messagesContainer.appendChild(this.createMessageElement(message));
        });

        this.messagesContainer.classList.remove('hidden');
        this.messagesEmpty.classList.add('hidden');
        this.scrollToBottom();
    }

    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-wrapper';
        messageDiv.dataset.messageId = message.id;

        const isCurrentUser = message.user_id === this.currentUser;
        const isSystemMessage = message.is_system_generated;

        let bubbleClass = 'message-bubble ';
        if (isSystemMessage) {
            bubbleClass += 'system-message';
        } else if (isCurrentUser) {
            bubbleClass += 'user-message';
        } else {
            bubbleClass += 'other-message';
        }

        const avatar = this.createAvatar(message.user);
        const timestamp = this.formatTimestamp(message.created_at);
        const messageContent = this.formatMessageContent(message.description || message.title);

        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3 ${isCurrentUser ? 'flex-row-reverse space-x-reverse' : ''}">
                ${!isSystemMessage ? avatar : ''}
                <div class="${bubbleClass}">
                    ${message.reply_to ? this.createReplyPreview(message.reply_to) : ''}
                    <div class="message-content">
                        ${message.title && message.title !== message.description ? `<div class="font-medium mb-1">${message.title}</div>` : ''}
                        <div>${messageContent}</div>
                        ${message.attachments ? this.createAttachments(message.attachments) : ''}
                    </div>
                    <div class="message-timestamp">
                        ${timestamp}
                        ${message.edited_at ? '<span class="message-edited">(edited)</span>' : ''}
                    </div>
                    ${!isSystemMessage ? this.createMessageStatus(message) : ''}
                </div>
                ${!isSystemMessage ? this.createMessageActions(message, isCurrentUser) : ''}
            </div>
        `;

        return messageDiv;
    }

    createAvatar(user) {
        if (!user) return '';

        const initials = user.name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'];
        const colorClass = colors[user.id % colors.length];

        return `
            <div class="message-avatar ${colorClass}">
                ${initials}
            </div>
        `;
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 days
            return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
        }
    }

    formatMessageContent(content) {
        if (!content) return '';

        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-blue-600 hover:underline">$1</a>')
            .replace(/\n/g, '<br>');
    }

    createAttachments(attachments) {
        if (!attachments || attachments.length === 0) return '';

        return `
            <div class="message-attachments">
                ${attachments.map(attachment => `
                    <a href="/business/activities/attachment/${attachment.id}/download"
                       class="message-attachment"
                       target="_blank">
                        <i class="fas fa-${this.getFileIcon(attachment.mime_type)}"></i>
                        <span>${attachment.original_name}</span>
                        <small>(${this.formatFileSize(attachment.file_size)})</small>
                    </a>
                `).join('')}
            </div>
        `;
    }

    createReplyPreview(replyTo) {
        if (!replyTo) return '';

        return `
            <div class="message-reply-to">
                <div class="text-xs opacity-75">
                    <i class="fas fa-reply mr-1"></i>
                    ${replyTo.user?.name || 'Unknown'}
                </div>
                <div class="text-sm truncate">${replyTo.description || replyTo.title}</div>
            </div>
        `;
    }

    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType.includes('pdf')) return 'file-pdf';
        if (mimeType.includes('word')) return 'file-word';
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'file-excel';
        if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'file-powerpoint';
        return 'file';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    createMessageActions(message, isCurrentUser) {
        const canEdit = isCurrentUser || {{ auth()->user()->hasPermission('manage_businesses') ? 'true' : 'false' }};

        return `
            <div class="message-actions">
                <button type="button" onclick="chatManager.replyToMessage(${message.id})"
                        class="p-1 text-gray-500 hover:text-blue-600 rounded" title="Reply">
                    <i class="fas fa-reply text-xs"></i>
                </button>
                ${canEdit ? `
                    <button type="button" onclick="chatManager.editMessage(${message.id})"
                            class="p-1 text-gray-500 hover:text-yellow-600 rounded" title="Edit">
                        <i class="fas fa-edit text-xs"></i>
                    </button>
                    <button type="button" onclick="chatManager.deleteMessage(${message.id})"
                            class="p-1 text-gray-500 hover:text-red-600 rounded" title="Delete">
                        <i class="fas fa-trash text-xs"></i>
                    </button>
                ` : ''}
            </div>
        `;
    }

    showLoading() {
        if (this.messagesLoading) this.messagesLoading.classList.remove('hidden');
        if (this.messagesContainer) this.messagesContainer.classList.add('hidden');
        if (this.messagesEmpty) this.messagesEmpty.classList.add('hidden');
    }

    hideLoading() {
        if (this.messagesLoading) this.messagesLoading.classList.add('hidden');
    }

    showEmpty() {
        if (this.messagesEmpty) this.messagesEmpty.classList.remove('hidden');
        if (this.messagesContainer) this.messagesContainer.classList.add('hidden');
    }

    scrollToBottom() {
        const container = document.getElementById('messages-container');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    }

    async sendMessage() {
        if (!this.messageInput) return;

        const message = this.messageInput.value.trim();
        const messageTypeSelector = document.getElementById('message-type-selector');
        const messageType = messageTypeSelector ? messageTypeSelector.value : 'comment';
        const replyToIdInput = document.getElementById('reply-to-message-id');
        const replyToId = replyToIdInput ? replyToIdInput.value : '';
        const editingIdInput = document.getElementById('editing-message-id');
        const editingId = editingIdInput ? editingIdInput.value : '';

        if (!message) return;

        if (this.sendBtn) this.sendBtn.disabled = true;

        try {
            const formData = new FormData();
            formData.append('message', message);
            formData.append('message_type', messageType);
            if (replyToId) formData.append('reply_to_id', replyToId);

            // Add file attachments
            const fileInput = document.getElementById('file-input');
            if (fileInput.files.length > 0) {
                Array.from(fileInput.files).forEach(file => {
                    formData.append('attachments[]', file);
                });
            }

            const url = editingId
                ? `/business/${this.businessId}/activities/${editingId}`
                : `/business/${this.businessId}/activities`;

            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) throw new Error('Failed to send message');

            const data = await response.json();

            if (editingId) {
                this.updateMessageInList(data.activity);
                this.cancelEdit();
            } else {
                this.addMessageToList(data.activity);
                this.cancelReply();
            }

            this.clearForm();
            this.scrollToBottom();

        } catch (error) {
            console.error('Error sending message:', error);
            this.showError('Failed to send message');
        } finally {
            if (this.sendBtn) this.sendBtn.disabled = false;
        }
    }

    async editMessage(messageId) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (!messageElement) return;

        const message = this.messages.find(m => m.id === messageId);
        if (!message) return;

        document.getElementById('editing-message-id').value = messageId;
        this.messageInput.value = message.description;
        document.getElementById('message-type-selector').value = message.metadata?.communication_type || 'comment';

        document.getElementById('edit-preview').classList.remove('hidden');
        this.messageInput.focus();
        this.autoResizeTextarea();
    }

    async deleteMessage(messageId) {
        if (!confirm('Are you sure you want to delete this message?')) return;

        try {
            const response = await fetch(`/business/${this.businessId}/activities/${messageId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) throw new Error('Failed to delete message');

            this.removeMessageFromList(messageId);

        } catch (error) {
            console.error('Error deleting message:', error);
            this.showError('Failed to delete message');
        }
    }

    replyToMessage(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (!message) return;

        document.getElementById('reply-to-message-id').value = messageId;
        document.getElementById('reply-to-user').textContent = message.user?.name || 'Unknown';
        document.getElementById('reply-to-content').textContent = message.description;

        document.getElementById('reply-preview').classList.remove('hidden');
        this.messageInput.focus();
    }

    cancelReply() {
        document.getElementById('reply-to-message-id').value = '';
        document.getElementById('reply-preview').classList.add('hidden');
    }

    cancelEdit() {
        document.getElementById('editing-message-id').value = '';
        document.getElementById('edit-preview').classList.add('hidden');
    }

    clearForm() {
        this.messageInput.value = '';
        document.getElementById('file-input').value = '';
        document.getElementById('file-preview-area').classList.add('hidden');
        this.autoResizeTextarea();
        this.updateCharCount();
    }

    addMessageToList(message) {
        this.messages.push(message);
        this.messagesContainer.appendChild(this.createMessageElement(message));
    }

    updateMessageInList(message) {
        const index = this.messages.findIndex(m => m.id === message.id);
        if (index !== -1) {
            this.messages[index] = message;
            const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
            if (messageElement) {
                messageElement.replaceWith(this.createMessageElement(message));
            }
        }
    }

    removeMessageFromList(messageId) {
        this.messages = this.messages.filter(m => m.id !== messageId);
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.remove();
        }
    }

    setupEmojiPicker() {
        const emojiBtn = document.getElementById('emoji-btn');
        if (!emojiBtn) return;

        const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾'];

        emojiBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleEmojiPicker();
        });

        document.addEventListener('click', () => {
            this.hideEmojiPicker();
        });
    }

    toggleEmojiPicker() {
        let picker = document.getElementById('emoji-picker');
        if (!picker) {
            picker = this.createEmojiPicker();
            document.body.appendChild(picker);
        }
        picker.classList.toggle('hidden');
    }

    hideEmojiPicker() {
        const picker = document.getElementById('emoji-picker');
        if (picker) picker.classList.add('hidden');
    }

    createEmojiPicker() {
        const picker = document.createElement('div');
        picker.id = 'emoji-picker';
        picker.className = 'emoji-picker hidden';

        const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾'];

        const grid = document.createElement('div');
        grid.className = 'emoji-grid';

        emojis.forEach(emoji => {
            const item = document.createElement('div');
            item.className = 'emoji-item';
            item.textContent = emoji;
            item.addEventListener('click', () => {
                this.insertEmoji(emoji);
                this.hideEmojiPicker();
            });
            grid.appendChild(item);
        });

        picker.appendChild(grid);
        return picker;
    }

    insertEmoji(emoji) {
        const cursorPos = this.messageInput.selectionStart;
        const textBefore = this.messageInput.value.substring(0, cursorPos);
        const textAfter = this.messageInput.value.substring(cursorPos);

        this.messageInput.value = textBefore + emoji + textAfter;
        this.messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        this.messageInput.focus();
        this.autoResizeTextarea();
        this.updateCharCount();
    }

    toggleSearchBar() {
        document.getElementById('search-bar').classList.toggle('hidden');
        document.getElementById('filter-panel').classList.add('hidden');
    }

    toggleFilterPanel() {
        document.getElementById('filter-panel').classList.toggle('hidden');
        document.getElementById('search-bar').classList.add('hidden');
    }

    showError(message) {
        // Simple error display - you can enhance this
        alert(message);
    }

    // Missing methods that are called but not implemented
    loadMoreMessages() {
        if (this.hasMoreMessages && !this.isLoading) {
            this.loadMessages(this.currentPage + 1);
        }
    }

    searchMessages(query) {
        // Implement search functionality
        console.log('Searching for:', query);
    }

    applyFilters() {
        // Implement filter functionality
        const messageType = document.getElementById('message-type-filter')?.value;
        const dateRange = document.getElementById('date-range-filter')?.value;
        const userId = document.getElementById('user-filter')?.value;

        console.log('Applying filters:', { messageType, dateRange, userId });
        this.loadMessages(1);
    }

    updateLoadMoreButton() {
        if (this.loadMoreContainer) {
            if (this.hasMoreMessages) {
                this.loadMoreContainer.classList.remove('hidden');
            } else {
                this.loadMoreContainer.classList.add('hidden');
            }
        }
    }

    prependMessages(messages) {
        messages.reverse().forEach(message => {
            this.messagesContainer.insertBefore(
                this.createMessageElement(message),
                this.messagesContainer.firstChild
            );
        });
    }

    createMessageStatus(message) {
        return `
            <div class="message-status">
                <i class="fas fa-check text-xs"></i>
                <span class="text-xs">Sent</span>
            </div>
        `;
    }
}

// Initialize chat manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('activity-content')) {
        window.chatManager = new ChatManager();
    }
});
</script>

<!-- Quill.js Script -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

@endsection
