@extends('layouts.app')

@section('title', 'Product Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Management</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">Manage products and services for businesses</p>
            </div>
            <div class="flex space-x-3">
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('products.create') }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Product
                    </a>
                @endif
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 transition duration-150 ease-in-out">
            <form method="GET" action="{{ route('products.index') }}" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search products..." 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
                </div>

                <div>
                    <select name="status" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Search
                </button>
                @if(request()->hasAny(['search', 'category', 'status']))
                    <a href="{{ route('products.index') }}" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Clear
                    </a>
                @endif
            </form>
        </div>

        <!-- Products List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            @if($products->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                    @foreach($products as $product)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $product->name }}</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $product->category_label }}</p>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="{{ route('products.show', $product) }}" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(auth()->user()->hasPermission('manage_businesses'))
                                        <a href="{{ route('products.edit', $product) }}" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 text-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($product->businesses_count == 0)
                                            <form method="POST" action="{{ route('products.destroy', $product) }}" 
                                                  onsubmit="return confirm('Are you sure you want to delete this product?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 text-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    @endif
                                </div>
                            </div>
                            
                            @if($product->description)
                                <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 mb-4">{{ Str::limit($product->description, 100) }}</p>
                            @endif

                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Price:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $product->formatted_base_price }}</span>
                                </div>
                                @if($product->pricing_model)
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Model:</span>
                                        <span class="text-sm text-gray-900 dark:text-white">{{ $product->pricing_model_label }}</span>
                                    </div>
                                @endif
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Businesses:</span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $product->businesses_count ?? 0 }}</span>
                                </div>
                            </div>


                            
                            <div class="flex items-center justify-between">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                    {{ $product->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    Created {{ $product->created_at->diffForHumans() }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    {{ $products->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No products found.</div>
                </div>
            @endif
        </div>

        <!-- Product Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-box text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Products</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $products->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Active Products</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $products->where('is_active', true)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Assigned Products</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $products->where('businesses_count', '>', 0)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
