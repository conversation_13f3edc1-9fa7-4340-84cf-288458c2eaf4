@extends('layouts.app')

@section('title', 'Product Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center">
                <i class="{{ $product->icon }} text-3xl text-gray-600 dark:text-gray-400 dark:text-gray-500 mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $product->name }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">Product details and usage information</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Products
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('products.edit', $product) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Edit Product
                    </a>
                @endif
            </div>
        </div>

        <!-- Product Information -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Basic product details and configuration.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <dl>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Product name</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $product->name }}</dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Icon</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <div class="flex items-center">
                                <i class="{{ $product->icon }} text-lg text-gray-600 dark:text-gray-400 dark:text-gray-500 mr-2"></i>
                                <span>{{ $product->icon_label }}</span>
                            </div>
                        </dd>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Slug</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $product->slug }}</dd>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            {{ $product->created_at->format('M d, Y \a\t g:i A') }}
                            @if($product->creator)
                                by {{ $product->creator->name }}
                            @endif
                        </dd>
                    </div>
                    @if($product->updated_at != $product->created_at)
                        <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Last updated</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                {{ $product->updated_at->format('M d, Y \a\t g:i A') }}
                            </dd>
                        </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Usage Statistics</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">How this product is being used across businesses.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $stats['total_businesses'] }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Businesses</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $stats['active_assignments'] }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Active Assignments</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ $product->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Product Age</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assigned Businesses -->
        <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Assigned Businesses</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Businesses currently using this product.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                @if($product->businesses->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Business</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Assigned</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                                @foreach($product->businesses as $business)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img class="h-8 w-8 rounded-full mr-3" src="{{ $business->logo_url }}" alt="{{ $business->name }}">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $business->name }}</div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $business->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $business->status_color }}">
                                                {{ $business->status_label }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            {{ $business->pivot->created_at->format('M d, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('business.show', $business) }}" 
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">View Business</a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <i class="fas fa-box text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No businesses are using this product yet.</p>
                        <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">Assign this product to businesses to see them here.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Management Actions -->
        @if(auth()->user()->hasPermission('manage_businesses'))
            <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Management Actions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Administrative actions for this product.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex space-x-4">
                        @if($product->businesses()->count() == 0)
                            <form method="POST" action="{{ route('products.destroy', $product) }}" 
                                  onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white dark:bg-gray-800 hover:bg-red-50 transition duration-150 ease-in-out">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Product
                                </button>
                            </form>
                        @else
                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i>
                                Cannot delete product that is assigned to businesses.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
