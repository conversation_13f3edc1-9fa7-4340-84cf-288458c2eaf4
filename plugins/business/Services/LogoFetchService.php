<?php

namespace Plugins\Business\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LogoFetchService
{
    /**
     * Fetch logo URL for a business website
     */
    public static function fetchLogoFromWebsite(string $websiteUrl): ?string
    {
        try {
            // Normalize URL
            $url = self::normalizeUrl($websiteUrl);
            
            if (!$url) {
                return null;
            }

            // Try to fetch the webpage
            $response = Http::timeout(10)->get($url);
            
            if (!$response->successful()) {
                return null;
            }

            $html = $response->body();
            
            // Try different methods to find logo
            $logoUrl = self::extractLogoFromHtml($html, $url);
            
            return $logoUrl;
            
        } catch (\Exception $e) {
            Log::warning('Failed to fetch logo for website: ' . $websiteUrl, [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Normalize website URL
     */
    private static function normalizeUrl(string $url): ?string
    {
        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }

        // Validate URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return null;
        }

        return $url;
    }

    /**
     * Extract logo URL from HTML content
     */
    private static function extractLogoFromHtml(string $html, string $baseUrl): ?string
    {
        // Try Open Graph image first
        if (preg_match('/<meta[^>]+property=["\']og:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return self::resolveUrl($matches[1], $baseUrl);
        }

        // Try Twitter card image
        if (preg_match('/<meta[^>]+name=["\']twitter:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return self::resolveUrl($matches[1], $baseUrl);
        }

        // Try to find logo in common patterns
        $logoPatterns = [
            // Look for images with logo in src, alt, or class
            '/<img[^>]+(?:src=["\']([^"\']*logo[^"\']*)["\']|alt=["\'][^"\']*logo[^"\']*["\']|class=["\'][^"\']*logo[^"\']*["\'])[^>]*>/i',
            // Look for images in header with common logo names
            '/<header[^>]*>.*?<img[^>]+src=["\']([^"\']+)["\'][^>]*>.*?<\/header>/is',
            // Look for favicon
            '/<link[^>]+rel=["\'](?:icon|shortcut icon)["\'][^>]+href=["\']([^"\']+)["\'][^>]*>/i',
        ];

        foreach ($logoPatterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $logoUrl = self::resolveUrl($matches[1], $baseUrl);
                if ($logoUrl && self::isValidImageUrl($logoUrl)) {
                    return $logoUrl;
                }
            }
        }

        // Fallback to favicon
        $faviconUrl = self::getFaviconUrl($baseUrl);
        if ($faviconUrl && self::isValidImageUrl($faviconUrl)) {
            return $faviconUrl;
        }

        return null;
    }

    /**
     * Resolve relative URL to absolute URL
     */
    private static function resolveUrl(string $url, string $baseUrl): string
    {
        // Already absolute URL
        if (preg_match('/^https?:\/\//', $url)) {
            return $url;
        }

        $parsedBase = parse_url($baseUrl);
        $scheme = $parsedBase['scheme'] ?? 'https';
        $host = $parsedBase['host'] ?? '';

        // Protocol relative URL
        if (str_starts_with($url, '//')) {
            return $scheme . ':' . $url;
        }

        // Absolute path
        if (str_starts_with($url, '/')) {
            return $scheme . '://' . $host . $url;
        }

        // Relative path
        $path = $parsedBase['path'] ?? '/';
        $path = rtrim(dirname($path), '/');
        
        return $scheme . '://' . $host . $path . '/' . $url;
    }

    /**
     * Get favicon URL for a domain
     */
    private static function getFaviconUrl(string $baseUrl): string
    {
        $parsedUrl = parse_url($baseUrl);
        $scheme = $parsedUrl['scheme'] ?? 'https';
        $host = $parsedUrl['host'] ?? '';
        
        return $scheme . '://' . $host . '/favicon.ico';
    }

    /**
     * Check if URL points to a valid image
     */
    private static function isValidImageUrl(string $url): bool
    {
        try {
            $response = Http::timeout(5)->head($url);
            
            if (!$response->successful()) {
                return false;
            }

            $contentType = $response->header('Content-Type');
            
            return $contentType && str_starts_with($contentType, 'image/');
            
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate fallback logo URL based on business name
     */
    public static function generateFallbackLogo(string $businessName): string
    {
        // Use a service like UI Avatars to generate a logo
        $initials = self::getInitials($businessName);
        $backgroundColor = self::generateColorFromName($businessName);
        
        return "https://ui-avatars.com/api/?name=" . urlencode($initials) . 
               "&size=64&background=" . $backgroundColor . "&color=ffffff&bold=true";
    }

    /**
     * Get initials from business name
     */
    private static function getInitials(string $name): string
    {
        $words = explode(' ', trim($name));
        $initials = '';
        
        foreach (array_slice($words, 0, 2) as $word) {
            if (!empty($word)) {
                $initials .= strtoupper($word[0]);
            }
        }
        
        return $initials ?: 'B';
    }

    /**
     * Generate a consistent color from business name
     */
    private static function generateColorFromName(string $name): string
    {
        $colors = [
            '3b82f6', '10b981', 'f59e0b', 'ef4444', '8b5cf6',
            '06b6d4', 'f97316', '84cc16', 'ec4899', '6b7280'
        ];
        
        $hash = crc32($name);
        $index = abs($hash) % count($colors);
        
        return $colors[$index];
    }
}
