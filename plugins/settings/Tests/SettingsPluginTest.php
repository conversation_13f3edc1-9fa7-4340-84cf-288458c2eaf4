<?php

namespace Plugins\Settings\Tests;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class SettingsPluginTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create();
        $this->admin->permissions()->create(['name' => 'manage_settings']);

        $this->user = User::factory()->create();

        // Ensure backup directory exists
        if (!is_dir(storage_path('app/backups'))) {
            mkdir(storage_path('app/backups'), 0755, true);
        }
    }

    protected function tearDown(): void
    {
        // Clean up test backups
        $backupPath = storage_path('app/backups');
        if (is_dir($backupPath)) {
            $files = glob($backupPath . '/backup-*.zip');
            foreach ($files as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }
        }

        parent::tearDown();
    }

    /** @test */
    public function admin_can_access_settings_dashboard()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.index'));

        $response->assertStatus(200);
        $response->assertSee('Settings Dashboard');
        $response->assertSee('System Status');
    }

    /** @test */
    public function regular_user_cannot_access_settings()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('settings.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_view_system_information()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.system.info'));

        $response->assertStatus(200);
        $response->assertSee('System Information');
        $response->assertSee('Application Information');
        $response->assertSee('Server Information');
    }

    /** @test */
    public function admin_can_perform_health_check()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.system.health-check'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'overall_health',
            'checks' => [
                'database' => ['status', 'message'],
                'storage' => ['status', 'message'],
                'cache' => ['status', 'message'],
                'queue' => ['status', 'message'],
                'permissions' => ['status', 'message'],
            ],
            'timestamp'
        ]);
    }

    /** @test */
    public function admin_can_access_update_management()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.updates.index'));

        $response->assertStatus(200);
        $response->assertSee('Update Management');
        $response->assertSee('Current Version');
    }

    /** @test */
    public function admin_can_check_for_updates()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('settings.updates.check'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'current_version',
            'update_info',
            'has_updates'
        ]);
    }

    /** @test */
    public function admin_can_view_changelog()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.updates.changelog'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'changelog'
        ]);
    }

    /** @test */
    public function admin_can_access_backup_management()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.backups.index'));

        $response->assertStatus(200);
        $response->assertSee('Backup Management');
        $response->assertSee('Total Backups');
    }

    /** @test */
    public function admin_can_create_backup()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('settings.backups.create'), [
            'include_files' => false, // Database only for faster test
            'description' => 'Test backup'
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure([
            'success',
            'message',
            'backup' => [
                'filename',
                'size',
                'created_at',
                'includes_files',
                'description'
            ]
        ]);

        // Verify backup file was created
        $backupData = $response->json('backup');
        $backupFile = storage_path('app/backups/' . $backupData['filename']);
        $this->assertFileExists($backupFile);
    }

    /** @test */
    public function admin_can_get_backup_status()
    {
        $this->actingAs($this->admin);

        // First create a backup
        $createResponse = $this->post(route('settings.backups.create'), [
            'include_files' => false,
            'description' => 'Test backup for status check'
        ]);

        $createResponse->assertStatus(200);
        $backupFilename = $createResponse->json('backup.filename');

        // Then get its status
        $response = $this->get(route('settings.backups.status', $backupFilename));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'backup_info'
        ]);
    }

    /** @test */
    public function admin_can_delete_backup()
    {
        $this->actingAs($this->admin);

        // First create a backup
        $createResponse = $this->post(route('settings.backups.create'), [
            'include_files' => false,
            'description' => 'Test backup for deletion'
        ]);

        $createResponse->assertStatus(200);
        $backupFilename = $createResponse->json('backup.filename');
        $backupFile = storage_path('app/backups/' . $backupFilename);

        // Verify backup exists
        $this->assertFileExists($backupFile);

        // Delete the backup
        $response = $this->delete(route('settings.backups.destroy', $backupFilename));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify backup was deleted
        $this->assertFileDoesNotExist($backupFile);
    }

    /** @test */
    public function backup_download_requires_valid_filename()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.backups.download', 'invalid-backup.zip'));

        $response->assertStatus(404);
    }

    /** @test */
    public function backup_restore_requires_confirmation()
    {
        $this->actingAs($this->admin);

        // First create a backup
        $createResponse = $this->post(route('settings.backups.create'), [
            'include_files' => false,
            'description' => 'Test backup for restore'
        ]);

        $createResponse->assertStatus(200);
        $backupFilename = $createResponse->json('backup.filename');

        // Try to restore without confirmation
        $response = $this->post(route('settings.backups.restore', $backupFilename), [
            'confirm' => false
        ]);

        $response->assertStatus(422); // Validation error
    }

    /** @test */
    public function regular_user_cannot_access_backup_management()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('settings.backups.index'));
        $response->assertStatus(403);

        $response = $this->post(route('settings.backups.create'));
        $response->assertStatus(403);
    }

    /** @test */
    public function regular_user_cannot_access_update_management()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('settings.updates.index'));
        $response->assertStatus(403);

        $response = $this->post(route('settings.updates.check'));
        $response->assertStatus(403);
    }

    /** @test */
    public function regular_user_cannot_access_system_information()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('settings.system.info'));
        $response->assertStatus(403);

        $response = $this->get(route('settings.system.health-check'));
        $response->assertStatus(403);
    }

    /** @test */
    public function plugin_manager_shows_settings_data()
    {
        $this->actingAs($this->admin);

        // Create a test backup first
        $this->post(route('settings.backups.create'), [
            'include_files' => false,
            'description' => 'Test backup for plugin manager'
        ]);

        // Visit plugin management page for settings
        $response = $this->get(route('plugins.show', 'settings'));

        $response->assertStatus(200);
        $response->assertSee('backups'); // Should show backup statistics
        $response->assertSee('system_logs'); // Should show log statistics
    }

    /** @test */
    public function health_check_detects_database_connectivity()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.system.health-check'));

        $response->assertStatus(200);
        $healthData = $response->json();
        
        $this->assertTrue($healthData['success']);
        $this->assertArrayHasKey('database', $healthData['checks']);
        $this->assertEquals('healthy', $healthData['checks']['database']['status']);
    }

    /** @test */
    public function health_check_detects_storage_accessibility()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('settings.system.health-check'));

        $response->assertStatus(200);
        $healthData = $response->json();
        
        $this->assertTrue($healthData['success']);
        $this->assertArrayHasKey('storage', $healthData['checks']);
        $this->assertEquals('healthy', $healthData['checks']['storage']['status']);
    }

    /** @test */
    public function backup_includes_metadata()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('settings.backups.create'), [
            'include_files' => false,
            'description' => 'Test backup with metadata'
        ]);

        $response->assertStatus(200);
        $backupData = $response->json('backup');
        
        $this->assertArrayHasKey('filename', $backupData);
        $this->assertArrayHasKey('size', $backupData);
        $this->assertArrayHasKey('created_at', $backupData);
        $this->assertArrayHasKey('includes_files', $backupData);
        $this->assertArrayHasKey('description', $backupData);
        
        $this->assertEquals('Test backup with metadata', $backupData['description']);
        $this->assertFalse($backupData['includes_files']);
    }
}
