<?php

use Illuminate\Support\Facades\Route;
use Plugins\Settings\Controllers\SettingsController;
use Plugins\Settings\Controllers\SystemInfoController;
use Plugins\Settings\Controllers\UpdateController;
use Plugins\Settings\Controllers\BackupController;

// Settings Management Routes
Route::prefix('settings')->name('settings.')->group(function () {
    // Main settings dashboard
    Route::get('/', [SettingsController::class, 'index'])->name('index');
    
    // System Information Routes
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [SystemInfoController::class, 'index'])->name('info');
        Route::get('/health-check', [SystemInfoController::class, 'healthCheck'])->name('health-check');
        Route::get('/phpinfo', [SystemInfoController::class, 'phpInfo'])->name('phpinfo');
    });
    
    // Update Management Routes
    Route::prefix('updates')->name('updates.')->group(function () {
        Route::get('/', [UpdateController::class, 'index'])->name('index');
        Route::post('/check', [UpdateController::class, 'checkForUpdates'])->name('check');
        Route::get('/changelog', [UpdateController::class, 'changelog'])->name('changelog');
        Route::post('/install/{version}', [UpdateController::class, 'installUpdate'])->name('install');
    });
    
    // Backup Management Routes
    Route::prefix('backups')->name('backups.')->group(function () {
        Route::get('/', [BackupController::class, 'index'])->name('index');
        Route::post('/create', [BackupController::class, 'create'])->name('create');
        Route::get('/download/{backup}', [BackupController::class, 'download'])->name('download');
        Route::post('/restore/{backup}', [BackupController::class, 'restore'])->name('restore');
        Route::delete('/{backup}', [BackupController::class, 'destroy'])->name('destroy');
        Route::get('/status/{backup}', [BackupController::class, 'status'])->name('status');
    });
});
