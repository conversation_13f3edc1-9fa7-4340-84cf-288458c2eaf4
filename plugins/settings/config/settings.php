<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the backup functionality
    |
    */
    
    'backup_path' => storage_path('app/backups'),
    
    'max_backup_files' => env('SETTINGS_MAX_BACKUP_FILES', 10),
    
    'backup_retention_days' => env('SETTINGS_BACKUP_RETENTION_DAYS', 30),
    
    /*
    |--------------------------------------------------------------------------
    | Update Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the update functionality
    |
    */
    
    'update_server' => env('SETTINGS_UPDATE_SERVER', null),
    
    'update_channel' => env('SETTINGS_UPDATE_CHANNEL', 'stable'),
    
    'auto_backup_before_update' => env('SETTINGS_AUTO_BACKUP_BEFORE_UPDATE', true),
    
    /*
    |--------------------------------------------------------------------------
    | System Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration options for system monitoring
    |
    */
    
    'health_check_cache_duration' => env('SETTINGS_HEALTH_CHECK_CACHE', 300), // 5 minutes
    
    'system_info_cache_duration' => env('SETTINGS_SYSTEM_INFO_CACHE', 3600), // 1 hour
    
    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related configuration options
    |
    */
    
    'require_confirmation_for_destructive_actions' => true,
    
    'log_all_settings_changes' => true,
    
    'allowed_backup_extensions' => ['zip'],
    
    'max_backup_size' => env('SETTINGS_MAX_BACKUP_SIZE', 1024 * 1024 * 1024), // 1GB
];
