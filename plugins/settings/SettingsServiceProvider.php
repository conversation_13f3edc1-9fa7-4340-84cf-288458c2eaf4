<?php

namespace Plugins\Settings;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register plugin configuration
        $this->mergeConfigFrom(
            __DIR__ . '/config/settings.php', 'settings'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load plugin routes
        $this->loadRoutesFrom(__DIR__ . '/routes.php');
        
        // Load plugin views
        $this->loadViewsFrom(__DIR__ . '/Views', 'settings');
        
        // Publish plugin assets if needed
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/config/settings.php' => config_path('settings.php'),
            ], 'settings-config');
        }
    }
}
