<?php

namespace Plugins\Settings\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class SettingsPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create settings-related permissions
        $permissions = [
            [
                'name' => 'manage_settings',
                'display_name' => 'Manage Settings',
                'description' => 'Access to system settings, backups, and updates'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }

        // Assign settings permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $settingsPermissions = Permission::whereIn('name', [
                'manage_settings'
            ])->get();

            foreach ($settingsPermissions as $permission) {
                if (!$adminRole->permissions->contains($permission)) {
                    $adminRole->permissions()->attach($permission);
                }
            }
        }

        $this->command->info('Settings permissions created and assigned to admin role.');
    }
}
