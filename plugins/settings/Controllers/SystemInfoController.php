<?php

namespace Plugins\Settings\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class SystemInfoController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_settings')) {
                abort(403, 'Access denied. You do not have permission to manage settings.');
            }

            return $next($request);
        });
    }

    /**
     * Display system information
     */
    public function index(): View
    {
        $systemInfo = $this->getSystemInformation();
        
        return view('plugins.settings::system-info', compact('systemInfo'));
    }

    /**
     * Perform system health check
     */
    public function healthCheck(): JsonResponse
    {
        try {
            $checks = [
                'database' => $this->checkDatabase(),
                'storage' => $this->checkStorage(),
                'cache' => $this->checkCache(),
                'queue' => $this->checkQueue(),
                'permissions' => $this->checkPermissions(),
            ];

            $overallHealth = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

            return response()->json([
                'success' => true,
                'overall_health' => $overallHealth ? 'healthy' : 'issues_detected',
                'checks' => $checks,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            Log::error('Health check failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Health check failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display PHP information
     */
    public function phpInfo(): View
    {
        ob_start();
        phpinfo();
        $phpInfo = ob_get_clean();
        
        return view('plugins.settings::phpinfo', compact('phpInfo'));
    }

    /**
     * Get comprehensive system information
     */
    private function getSystemInformation(): array
    {
        return [
            'application' => $this->getApplicationInfo(),
            'server' => $this->getServerInfo(),
            'database' => $this->getDatabaseInfo(),
            'storage' => $this->getStorageInfo(),
            'performance' => $this->getPerformanceInfo(),
        ];
    }

    /**
     * Get application information
     */
    private function getApplicationInfo(): array
    {
        return [
            'name' => config('app.name'),
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'debug_mode' => config('app.debug') ? 'Enabled' : 'Disabled',
            'url' => config('app.url'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
            'laravel_version' => app()->version(),
        ];
    }

    /**
     * Get server information
     */
    private function getServerInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ];
    }

    /**
     * Get database information
     */
    private function getDatabaseInfo(): array
    {
        try {
            $connection = DB::connection();
            $driver = $connection->getDriverName();
            
            $info = [
                'driver' => $driver,
                'database' => $connection->getDatabaseName(),
                'host' => config("database.connections.{$driver}.host", 'N/A'),
                'port' => config("database.connections.{$driver}.port", 'N/A'),
                'version' => $this->getDatabaseVersion($driver),
            ];

            return $info;
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve database information: ' . $e->getMessage()];
        }
    }

    /**
     * Get storage information
     */
    private function getStorageInfo(): array
    {
        try {
            $storagePath = storage_path();
            $publicPath = public_path();
            
            return [
                'storage_path' => $storagePath,
                'storage_writable' => is_writable($storagePath),
                'public_path' => $publicPath,
                'public_writable' => is_writable($publicPath),
                'disk_free_space' => $this->formatBytes(disk_free_space($storagePath)),
                'disk_total_space' => $this->formatBytes(disk_total_space($storagePath)),
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve storage information: ' . $e->getMessage()];
        }
    }

    /**
     * Get performance information
     */
    private function getPerformanceInfo(): array
    {
        return [
            'memory_usage' => $this->formatBytes(memory_get_usage(true)),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
            'memory_limit' => ini_get('memory_limit'),
            'uptime' => $this->getSystemUptime(),
            'load_average' => $this->getLoadAverage(),
        ];
    }

    /**
     * Check database connectivity
     */
    private function checkDatabase(): array
    {
        try {
            DB::connection()->getPdo();
            return [
                'status' => 'healthy',
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage accessibility
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health-check-' . time() . '.txt';
            Storage::put($testFile, 'test');
            Storage::delete($testFile);
            
            return [
                'status' => 'healthy',
                'message' => 'Storage is writable',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Storage check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache functionality
     */
    private function checkCache(): array
    {
        try {
            $key = 'health-check-' . time();
            Cache::put($key, 'test', 60);
            $value = Cache::get($key);
            Cache::forget($key);
            
            if ($value === 'test') {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache is working',
                ];
            } else {
                return [
                    'status' => 'warning',
                    'message' => 'Cache test failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue functionality
     */
    private function checkQueue(): array
    {
        try {
            $driver = config('queue.default');
            return [
                'status' => 'healthy',
                'message' => "Queue driver: {$driver}",
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'warning',
                'message' => 'Queue check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check file permissions
     */
    private function checkPermissions(): array
    {
        $paths = [
            storage_path(),
            storage_path('logs'),
            storage_path('framework/cache'),
            storage_path('framework/sessions'),
            storage_path('framework/views'),
        ];

        $issues = [];
        foreach ($paths as $path) {
            if (!is_writable($path)) {
                $issues[] = $path;
            }
        }

        if (empty($issues)) {
            return [
                'status' => 'healthy',
                'message' => 'All required directories are writable',
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Non-writable directories: ' . implode(', ', $issues),
            ];
        }
    }

    /**
     * Get database version
     */
    private function getDatabaseVersion(string $driver): string
    {
        try {
            switch ($driver) {
                case 'mysql':
                    return DB::select('SELECT VERSION() as version')[0]->version;
                case 'sqlite':
                    return DB::select('SELECT sqlite_version() as version')[0]->version;
                case 'pgsql':
                    return DB::select('SELECT version() as version')[0]->version;
                default:
                    return 'Unknown';
            }
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get system uptime
     */
    private function getSystemUptime(): string
    {
        try {
            if (PHP_OS_FAMILY === 'Linux') {
                $uptime = file_get_contents('/proc/uptime');
                $seconds = (int) explode(' ', $uptime)[0];
                return $this->formatUptime($seconds);
            }
            return 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get load average
     */
    private function getLoadAverage(): string
    {
        try {
            if (function_exists('sys_getloadavg')) {
                $load = sys_getloadavg();
                return implode(', ', array_map(fn($l) => round($l, 2), $load));
            }
            return 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Format uptime seconds to human readable format
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        return "{$days} days, {$hours} hours, {$minutes} minutes";
    }
}
