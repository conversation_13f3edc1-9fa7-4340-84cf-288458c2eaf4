<?php

namespace Plugins\Settings\Controllers;

use App\Http\Controllers\Controller;
use App\Traits\DatabaseCompatibility;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use ZipArchive;

class BackupController extends Controller
{
    use DatabaseCompatibility;

    private string $backupPath;
    private int $maxBackups;
    private int $retentionDays;

    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_settings')) {
                abort(403, 'Access denied. You do not have permission to manage settings.');
            }

            return $next($request);
        });

        $this->backupPath = storage_path('app/backups');
        $this->maxBackups = config('settings.max_backup_files', 10);
        $this->retentionDays = config('settings.backup_retention_days', 30);

        // Ensure backup directory exists
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    /**
     * Display backup management interface
     */
    public function index(): View
    {
        $backups = $this->getBackupList();
        $stats = $this->getBackupStats();
        
        return view('plugins.settings::backups', compact('backups', 'stats'));
    }

    /**
     * Create a new backup
     */
    public function create(Request $request): JsonResponse
    {
        try {
            // Check if ZipArchive is available
            if (!class_exists('ZipArchive')) {
                return response()->json([
                    'success' => false,
                    'message' => 'ZipArchive extension is not available. Please install php-zip extension.',
                ], 500);
            }

            // Check if backup directory is writable
            if (!is_writable($this->backupPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup directory is not writable: ' . $this->backupPath,
                ], 500);
            }

            $request->validate([
                'include_files' => 'boolean',
                'description' => 'nullable|string|max:255',
            ]);

            $includeFiles = $request->boolean('include_files', true);
            $description = $request->input('description', '') ?? '';

            $backupResult = $this->createBackup($includeFiles, $description);

            Log::info('Backup created', [
                'backup_file' => $backupResult['filename'],
                'include_files' => $includeFiles,
                'user' => auth()->user()->email,
                'description' => $description,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup created successfully',
                'backup' => $backupResult,
            ]);
        } catch (\Exception $e) {
            Log::error('Backup creation failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user' => auth()->user()->email ?? 'unknown',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Backup creation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download a backup file
     */
    public function download(Request $request, string $backup): Response
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                abort(404, 'Backup file not found');
            }

            Log::info('Backup downloaded', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
            ]);

            return response()->download($backupFile);
        } catch (\Exception $e) {
            Log::error('Backup download failed: ' . $e->getMessage());
            abort(500, 'Failed to download backup');
        }
    }

    /**
     * Restore from backup
     */
    public function restore(Request $request, string $backup): JsonResponse
    {
        try {
            $request->validate([
                'confirm' => 'required|boolean|accepted',
            ]);

            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            $restoreResult = $this->restoreBackup($backupFile);
            
            Log::info('Backup restore attempted', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
                'success' => $restoreResult['success'],
            ]);

            return response()->json($restoreResult);
        } catch (\Exception $e) {
            Log::error('Backup restore failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Backup restore failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a backup file
     */
    public function destroy(Request $request, string $backup): JsonResponse
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            unlink($backupFile);
            
            Log::info('Backup deleted', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup deleted successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Backup deletion failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete backup: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get backup status
     */
    public function status(Request $request, string $backup): JsonResponse
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            $info = $this->getBackupInfo($backupFile);

            return response()->json([
                'success' => true,
                'backup_info' => $info,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get backup status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a backup
     */
    private function createBackup(bool $includeFiles = true, ?string $description = ''): array
    {
        // Ensure description is never null
        $description = $description ?? '';

        // Increase memory and time limits for backup operations
        ini_set('memory_limit', '1024M');
        set_time_limit(300); // 5 minutes

        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "backup-{$timestamp}.zip";
        $backupFile = $this->backupPath . '/' . $filename;

        // Ensure backup directory exists
        if (!is_dir($this->backupPath)) {
            if (!mkdir($this->backupPath, 0755, true)) {
                throw new \Exception("Cannot create backup directory: {$this->backupPath}");
            }
        }

        // Check if directory is writable
        if (!is_writable($this->backupPath)) {
            throw new \Exception("Backup directory is not writable: {$this->backupPath}");
        }

        $zip = new ZipArchive();
        $result = $zip->open($backupFile, ZipArchive::CREATE);
        if ($result !== TRUE) {
            $errorMessage = match($result) {
                ZipArchive::ER_OK => 'No error',
                ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
                ZipArchive::ER_RENAME => 'Renaming temporary file failed',
                ZipArchive::ER_CLOSE => 'Closing zip archive failed',
                ZipArchive::ER_SEEK => 'Seek error',
                ZipArchive::ER_READ => 'Read error',
                ZipArchive::ER_WRITE => 'Write error',
                ZipArchive::ER_CRC => 'CRC error',
                ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
                ZipArchive::ER_NOENT => 'No such file',
                ZipArchive::ER_EXISTS => 'File already exists',
                ZipArchive::ER_OPEN => 'Can\'t open file',
                ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
                ZipArchive::ER_ZLIB => 'Zlib error',
                ZipArchive::ER_MEMORY => 'Memory allocation failure',
                ZipArchive::ER_CHANGED => 'Entry has been changed',
                ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
                ZipArchive::ER_EOF => 'Premature EOF',
                ZipArchive::ER_INVAL => 'Invalid argument',
                ZipArchive::ER_NOZIP => 'Not a zip archive',
                ZipArchive::ER_INTERNAL => 'Internal error',
                ZipArchive::ER_INCONS => 'Zip archive inconsistent',
                ZipArchive::ER_REMOVE => 'Can\'t remove file',
                ZipArchive::ER_DELETED => 'Entry has been deleted',
                default => "Unknown error code: {$result}"
            };
            throw new \Exception("Cannot create backup file: {$backupFile}. Error: {$errorMessage}");
        }

        try {
            // Create database dump
            $databaseDump = $this->createDatabaseDump();
            $zip->addFromString('database.sql', $databaseDump);

            // Add metadata
            $metadata = [
                'created_at' => now()->toISOString(),
                'created_by' => auth()->user()->email,
                'app_version' => config('app.version', '1.0.0'),
                'description' => $description,
                'includes_files' => $includeFiles,
                'database_driver' => config('database.default'),
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'environment' => config('app.env'),
                'app_url' => config('app.url'),
                'timezone' => config('app.timezone'),
                'locale' => config('app.locale'),
                'includes_vendor' => $includeFiles && is_dir(base_path('vendor')),
                'includes_node_modules' => $includeFiles && is_dir(base_path('node_modules')),
                'composer_version' => $this->getComposerVersion(),
                'node_version' => $this->getNodeVersion(),
                'npm_version' => $this->getNpmVersion(),
            ];
            $zip->addFromString('metadata.json', json_encode($metadata, JSON_PRETTY_PRINT));

            // Add application files if requested
            if ($includeFiles) {
                $this->addFilesToBackup($zip);
            }

            $zip->close();
            Log::info('Backup ZIP file closed successfully');

            // Clean up old backups
            $this->cleanupOldBackups();
            Log::info('Old backups cleaned up');

            Log::info('Backup creation completed successfully', [
                'filename' => $filename,
                'size' => filesize($backupFile),
            ]);

            return [
                'filename' => $filename,
                'size' => filesize($backupFile),
                'created_at' => now()->toISOString(),
                'includes_files' => $includeFiles,
                'description' => $description,
            ];
        } catch (\Exception $e) {
            $zip->close();
            if (file_exists($backupFile)) {
                unlink($backupFile);
            }
            throw $e;
        }
    }

    /**
     * Create database dump
     */
    private function createDatabaseDump(): string
    {
        $driver = config('database.default');
        
        switch ($driver) {
            case 'mysql':
                return $this->createMySQLDump();
            case 'sqlite':
                return $this->createSQLiteDump();
            case 'pgsql':
                return $this->createPostgreSQLDump();
            default:
                throw new \Exception("Unsupported database driver: {$driver}");
        }
    }

    /**
     * Create MySQL dump
     */
    private function createMySQLDump(): string
    {
        $tables = DB::select('SHOW TABLES');
        $dump = "-- MySQL Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            
            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
            $dump .= "-- Table: {$tableName}\n";
            $dump .= $createTable->{'Create Table'} . ";\n\n";
            
            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Create SQLite dump
     */
    private function createSQLiteDump(): string
    {
        $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        $dump = "-- SQLite Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = $table->name;
            
            // Get table structure
            $createTable = DB::select("SELECT sql FROM sqlite_master WHERE type='table' AND name=?", [$tableName])[0];
            $dump .= "-- Table: {$tableName}\n";
            $dump .= $createTable->sql . ";\n\n";
            
            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO \"{$tableName}\" VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Create PostgreSQL dump
     */
    private function createPostgreSQLDump(): string
    {
        // This is a simplified version - in production, you might want to use pg_dump
        $tables = DB::select("SELECT tablename FROM pg_tables WHERE schemaname = 'public'");
        $dump = "-- PostgreSQL Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = $table->tablename;
            
            // Get table data (structure would require more complex queries)
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO \"{$tableName}\" VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Add files to backup
     */
    private function addFilesToBackup(ZipArchive $zip): void
    {
        $filesToBackup = [
            'app',
            'config',
            'database/migrations',
            'resources',
            'routes',
            'plugins',
            '.env',
            'composer.json',
            'composer.lock',
            'package.json',
            'package-lock.json',
            'vite.config.js',
            'tailwind.config.js',
        ];

        foreach ($filesToBackup as $path) {
            $fullPath = base_path($path);
            if (file_exists($fullPath)) {
                if (is_file($fullPath)) {
                    $zip->addFile($fullPath, $path);
                } else {
                    $this->addDirectoryToZip($zip, $fullPath, $path);
                }
            }
        }

        // Add dependency directories (with size limits to prevent timeouts)
        $dependencyDirectories = [
            'vendor' => base_path('vendor'),
            'node_modules' => base_path('node_modules'),
        ];

        foreach ($dependencyDirectories as $zipPath => $fullPath) {
            if (is_dir($fullPath)) {
                Log::info("Adding dependency directory to backup: {$zipPath}");

                // Check directory size before adding to prevent memory issues
                $dirSize = $this->getDirectorySize($fullPath);
                $maxSize = 500 * 1024 * 1024; // 500MB limit

                if ($dirSize > $maxSize) {
                    Log::info("Skipping large dependency directory: {$zipPath} (Size: " . $this->formatBytes($dirSize) . ")");
                    // Add a note file instead of the full directory
                    $zip->addFromString("{$zipPath}/README_EXCLUDED.txt",
                        "This directory was excluded from backup due to size ({$this->formatBytes($dirSize)}).\n" .
                        "To restore: run 'composer install' for vendor or 'npm install' for node_modules."
                    );
                } else {
                    $this->addDirectoryToZip($zip, $fullPath, $zipPath);
                }
            } else {
                Log::info("Dependency directory not found, skipping: {$zipPath}");
            }
        }

        // Add storage and public directories
        $storageDirectories = [
            'storage/app' => storage_path('app'),
            'storage/framework' => storage_path('framework'),
            'storage/logs' => storage_path('logs'),
            'public/uploads' => public_path('uploads'),
            'public/storage' => public_path('storage'),
            'public/build' => public_path('build'),
        ];

        foreach ($storageDirectories as $zipPath => $fullPath) {
            if (is_dir($fullPath)) {
                Log::info("Adding storage directory to backup: {$zipPath}");
                $this->addDirectoryToZip($zip, $fullPath, $zipPath);
            } else {
                Log::info("Directory not found, skipping: {$zipPath}");
            }
        }

        // Add database file if using SQLite
        $this->addDatabaseFileToBackup($zip);

        // Add environment-specific configurations
        $this->addEnvironmentConfigurations($zip);
    }

    /**
     * Get directory size in bytes
     */
    private function getDirectorySize(string $dir): int
    {
        $size = 0;
        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::SELF_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();

                    // Stop counting if size exceeds 1GB to prevent long calculations
                    if ($size > 1024 * 1024 * 1024) {
                        return $size;
                    }
                }
            }
        } catch (\Exception $e) {
            // If we can't calculate size, assume it's large
            return 1024 * 1024 * 1024; // 1GB
        }

        return $size;
    }

    /**
     * Add directory to zip recursively
     */
    private function addDirectoryToZip(ZipArchive $zip, string $dir, string $zipPath): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = $zipPath . '/' . $iterator->getSubPathName();
                $zip->addFile($file->getRealPath(), $relativePath);
            }
        }
    }

    /**
     * Add database file to backup (for SQLite)
     */
    private function addDatabaseFileToBackup(ZipArchive $zip): void
    {
        $driver = config('database.default');

        if ($driver === 'sqlite') {
            $databasePath = config('database.connections.sqlite.database');
            if (file_exists($databasePath)) {
                Log::info("Adding SQLite database file to backup");
                $zip->addFile($databasePath, 'database/' . basename($databasePath));
            } else {
                Log::warning("SQLite database file not found: {$databasePath}");
            }
        }
    }

    /**
     * Get list of backups
     */
    private function getBackupList(): array
    {
        $files = glob($this->backupPath . '/*.zip');
        $backups = [];

        foreach ($files as $file) {
            $filename = basename($file);
            $backups[] = [
                'filename' => $filename,
                'size' => filesize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                'info' => $this->getBackupInfo($file),
            ];
        }

        // Sort by creation date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $backups;
    }

    /**
     * Get backup statistics
     */
    private function getBackupStats(): array
    {
        $files = glob($this->backupPath . '/*.zip');
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }

        return [
            'total_backups' => count($files),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'oldest_backup' => count($files) > 0 ? date('Y-m-d H:i:s', min(array_map('filemtime', $files))) : null,
            'newest_backup' => count($files) > 0 ? date('Y-m-d H:i:s', max(array_map('filemtime', $files))) : null,
        ];
    }

    /**
     * Get backup information
     */
    private function getBackupInfo(string $backupFile): array
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($backupFile) === TRUE) {
                $metadata = $zip->getFromName('metadata.json');
                $zip->close();
                
                if ($metadata) {
                    return json_decode($metadata, true);
                }
            }
        } catch (\Exception $e) {
            // Ignore errors and return basic info
        }

        return [
            'created_at' => date('Y-m-d H:i:s', filemtime($backupFile)),
            'size' => filesize($backupFile),
        ];
    }

    /**
     * Restore from backup
     */
    private function restoreBackup(string $backupFile): array
    {
        try {
            // Increase memory and time limits for restore operations
            ini_set('memory_limit', '2048M');
            set_time_limit(600); // 10 minutes

            $zip = new ZipArchive();
            if ($zip->open($backupFile) !== TRUE) {
                throw new \Exception('Cannot open backup file');
            }

            $tempDir = storage_path('app/temp/restore-' . time());
            mkdir($tempDir, 0755, true);

            try {
                // Extract backup
                $zip->extractTo($tempDir);
                $zip->close();

                // Step 1: Restore database (complete replacement)
                $databaseFile = $tempDir . '/database.sql';
                if (file_exists($databaseFile)) {
                    $this->restoreDatabase($databaseFile);
                }

                // Step 2: Restore files (complete replacement)
                $this->restoreFiles($tempDir);

                // Step 3: Install dependencies
                $this->installDependencies();

                // Step 4: Build frontend assets
                $this->buildFrontendAssets();

                // Step 5: Ensure storage directories exist
                $this->ensureStorageDirectories();

                // Step 6: Clear application caches to ensure fresh state
                Artisan::call('cache:clear');
                Artisan::call('config:clear');
                Artisan::call('route:clear');
                Artisan::call('view:clear');

                // Step 7: Clear any session data to force re-authentication
                try {
                    Artisan::call('session:clear', ['--force' => true]);
                } catch (\Exception $e) {
                    // Session clear might not be available in all Laravel versions
                    Log::info('Session clear command not available, clearing manually');
                    $this->clearSessions();
                }

                // Step 8: Ensure roles and permissions are properly seeded
                $this->ensureRolesAndPermissions();

                return [
                    'success' => true,
                    'message' => 'Backup restored successfully. You will be redirected to the login page.',
                    'redirect' => url('/login')
                ];
            } finally {
                // Clean up temp directory
                $this->deleteDirectory($tempDir);
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Restore failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Restore database from SQL file
     */
    private function restoreDatabase(string $sqlFile): void
    {
        $sql = file_get_contents($sqlFile);

        // Split SQL into individual statements with better parsing
        $statements = $this->parseSqlStatements($sql);

        DB::transaction(function() use ($statements) {
            Log::info('Starting complete database restore', ['statement_count' => count($statements)]);

            // Step 1: Get list of all tables to drop
            $tables = $this->getAllTables();
            Log::info('Found tables to drop', ['tables' => $tables]);

            // Step 2: Drop ALL tables including migrations (complete replacement)
            $this->dropAllTables($tables);
            Log::info('Dropped all existing tables including migrations');

            // Step 3: Execute all statements from backup
            $successCount = 0;
            $errorCount = 0;
            $skipCount = 0;

            // Sort statements to handle dependencies properly
            $allStatements = $this->sortCreateTableStatements($statements);

            foreach ($allStatements as $statement) {
                if (!empty(trim($statement))) {
                    try {
                        // Validate statement before execution
                        if ($this->isValidSqlStatement($statement)) {
                            // Clean the statement to handle special characters
                            $cleanStatement = $this->cleanSqlStatement($statement);
                            DB::unprepared($cleanStatement);
                            $successCount++;
                        } else {
                            Log::warning('Skipping invalid SQL statement', [
                                'statement' => substr($statement, 0, 100) . '...'
                            ]);
                            $skipCount++;
                        }
                    } catch (\Exception $e) {
                        $errorCount++;

                        // Check if this is a non-critical error we can skip
                        $nonCriticalErrors = [
                            'already exists',
                            'UNIQUE constraint failed',
                            'syntax error',
                            'no such table',
                            'may not be dropped'
                        ];

                        $isCritical = true;
                        foreach ($nonCriticalErrors as $errorType) {
                            if (str_contains($e->getMessage(), $errorType)) {
                                $isCritical = false;
                                break;
                            }
                        }

                        if ($isCritical) {
                            Log::error('Critical database restore error', [
                                'statement' => substr($statement, 0, 200) . '...',
                                'error' => $e->getMessage()
                            ]);
                        } else {
                            Log::warning('Failed to execute SQL statement during restore', [
                                'statement' => substr($statement, 0, 100) . '...',
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
            }

            Log::info('Database restore completed', [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'skip_count' => $skipCount,
                'total_statements' => count($allStatements)
            ]);
        });
    }

    /**
     * Clean up old backups
     */
    private function cleanupOldBackups(): void
    {
        $files = glob($this->backupPath . '/*.zip');
        
        // Remove files older than retention period
        $cutoffTime = time() - ($this->retentionDays * 24 * 60 * 60);
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }

        // Remove excess files (keep only max number)
        $files = glob($this->backupPath . '/*.zip');
        if (count($files) > $this->maxBackups) {
            // Sort by modification time (oldest first)
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            $filesToDelete = array_slice($files, 0, count($files) - $this->maxBackups);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * Check if backup file is valid
     */
    private function isValidBackupFile(string $filename): bool
    {
        return preg_match('/^backup-\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/', $filename) ||
               preg_match('/^pre-update-backup-\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/', $filename);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    /**
     * Restore files from backup
     */
    private function restoreFiles(string $tempDir): void
    {
        Log::info('Starting file restoration');

        // Check if backup includes files
        $metadataFile = $tempDir . '/metadata.json';
        if (file_exists($metadataFile)) {
            $metadata = json_decode(file_get_contents($metadataFile), true);
            if (!($metadata['includes_files'] ?? false)) {
                Log::info('Backup does not include files, skipping file restoration');
                return;
            }
        }

        // Restore application files
        $filesToRestore = [
            'app' => base_path('app'),
            'config' => base_path('config'),
            'database/migrations' => base_path('database/migrations'),
            'resources' => base_path('resources'),
            'routes' => base_path('routes'),
            'plugins' => base_path('plugins'),
            '.env' => base_path('.env'),
            'composer.json' => base_path('composer.json'),
            'composer.lock' => base_path('composer.lock'),
            'package.json' => base_path('package.json'),
            'package-lock.json' => base_path('package-lock.json'),
            'vite.config.js' => base_path('vite.config.js'),
            'tailwind.config.js' => base_path('tailwind.config.js'),
        ];

        foreach ($filesToRestore as $backupPath => $targetPath) {
            $sourceFile = $tempDir . '/' . $backupPath;

            if (file_exists($sourceFile) && is_file($sourceFile)) {
                Log::info("Restoring file: {$backupPath}");

                // Create target directory if needed
                if (!is_dir(dirname($targetPath))) {
                    mkdir(dirname($targetPath), 0755, true);
                }

                // Copy file from backup
                if (copy($sourceFile, $targetPath)) {
                    Log::info("Successfully restored file: {$backupPath}");
                } else {
                    Log::error("Failed to copy file: {$backupPath}");
                    throw new \Exception("Failed to restore file: {$backupPath}");
                }
            } else if (file_exists($sourceFile) && is_dir($sourceFile)) {
                Log::info("Skipping directory in file restore section: {$backupPath} (will be handled in directory section)");
            } else {
                Log::info("File not found in backup: {$backupPath}");
            }
        }

        // Restore directories
        $directoriesToRestore = [
            'vendor' => base_path('vendor'),
            'node_modules' => base_path('node_modules'),
            'storage/app' => storage_path('app'),
            'storage/framework' => storage_path('framework'),
            'storage/logs' => storage_path('logs'),
            'public/uploads' => public_path('uploads'),
            'public/storage' => public_path('storage'),
            'public/build' => public_path('build'),
        ];

        foreach ($directoriesToRestore as $backupPath => $targetPath) {
            $sourceDir = $tempDir . '/' . $backupPath;

            Log::info("Processing directory: {$backupPath}");

            // Always remove existing directory completely (even if not in backup)
            if (is_dir($targetPath)) {
                Log::info("Removing existing directory: {$targetPath}");
                $this->deleteDirectory($targetPath);
                Log::info("Successfully removed existing directory: {$targetPath}");
            }

            // Only restore if directory exists in backup
            if (is_dir($sourceDir)) {
                Log::info("Restoring directory from backup: {$backupPath} to {$targetPath}");

                // Check directory size for large directories to prevent timeouts
                if (in_array($backupPath, ['vendor', 'node_modules'])) {
                    $dirSize = $this->getDirectorySize($sourceDir);
                    $maxSize = 1024 * 1024 * 1024; // 1GB limit for restore

                    if ($dirSize > $maxSize) {
                        Log::info("Skipping large directory restore: {$backupPath} (Size: " . $this->formatBytes($dirSize) . ")");
                        Log::info("Directory will be restored via dependency installation instead");
                        continue;
                    }
                }

                // Create target directory
                if (!is_dir(dirname($targetPath))) {
                    mkdir(dirname($targetPath), 0755, true);
                }

                // Copy files from backup with timeout protection
                try {
                    $this->copyDirectory($sourceDir, $targetPath);
                    Log::info("Successfully restored directory: {$backupPath}");
                } catch (\Exception $e) {
                    Log::error("Failed to restore directory: {$backupPath}", [
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other directories
                }
            } else {
                Log::info("Directory not found in backup (will remain deleted): {$backupPath}");
            }
        }

        // Restore database file if it exists in backup
        $this->restoreDatabaseFile($tempDir);

        // Recreate storage symlink
        $this->recreateStorageSymlink();

        Log::info('File restoration completed');
    }

    /**
     * Copy directory recursively
     */
    private function copyDirectory(string $source, string $destination): void
    {
        if (!is_dir($source)) {
            return;
        }

        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($source, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            $targetPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();

            if ($item->isDir()) {
                if (!is_dir($targetPath)) {
                    if (!mkdir($targetPath, 0755, true)) {
                        throw new \Exception("Failed to create directory: {$targetPath}");
                    }
                }
            } else {
                if (!copy($item->getRealPath(), $targetPath)) {
                    throw new \Exception("Failed to copy file: {$item->getRealPath()} to {$targetPath}");
                }
            }
        }
    }

    /**
     * Install dependencies
     */
    private function installDependencies(): void
    {
        Log::info('Starting dependency installation');

        // Install Composer dependencies
        if (file_exists(base_path('composer.json'))) {
            Log::info('Installing Composer dependencies');
            $composerOutput = shell_exec('cd ' . base_path() . ' && composer install --no-dev --optimize-autoloader 2>&1');
            Log::info('Composer install output', ['output' => $composerOutput]);
        }

        // Install NPM dependencies
        if (file_exists(base_path('package.json'))) {
            Log::info('Installing NPM dependencies');
            $npmOutput = shell_exec('cd ' . base_path() . ' && npm install 2>&1');
            Log::info('NPM install output', ['output' => $npmOutput]);
        }

        Log::info('Dependency installation completed');
    }

    /**
     * Build frontend assets
     */
    private function buildFrontendAssets(): void
    {
        Log::info('Starting frontend asset build');

        if (file_exists(base_path('package.json'))) {
            Log::info('Building frontend assets with npm run build');
            $buildOutput = shell_exec('cd ' . base_path() . ' && npm run build 2>&1');
            Log::info('NPM build output', ['output' => $buildOutput]);
        }

        Log::info('Frontend asset build completed');
    }

    /**
     * Clear sessions manually
     */
    private function clearSessions(): void
    {
        try {
            $sessionPath = storage_path('framework/sessions');
            if (is_dir($sessionPath)) {
                $files = glob($sessionPath . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                Log::info('Sessions cleared manually');
            }
        } catch (\Exception $e) {
            Log::warning('Failed to clear sessions manually', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Ensure storage directories exist
     */
    private function ensureStorageDirectories(): void
    {
        Log::info('Ensuring storage directories exist');

        $directories = [
            storage_path('framework'),
            storage_path('framework/cache'),
            storage_path('framework/cache/data'),
            storage_path('framework/sessions'),
            storage_path('framework/views'),
            storage_path('framework/testing'),
            storage_path('app'),
            storage_path('app/public'),
            storage_path('logs'),
        ];

        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                if (mkdir($directory, 0755, true)) {
                    Log::info("Created storage directory: {$directory}");
                } else {
                    Log::error("Failed to create storage directory: {$directory}");
                }
            }
        }

        // Set proper permissions
        chmod(storage_path('framework'), 0755);
        chmod(storage_path('framework/cache'), 0755);
        chmod(storage_path('framework/cache/data'), 0755);
        chmod(storage_path('framework/sessions'), 0755);
        chmod(storage_path('framework/views'), 0755);
        chmod(storage_path('logs'), 0755);

        Log::info('Storage directories verification completed');
    }

    /**
     * Ensure roles and permissions are properly set up after restore
     */
    private function ensureRolesAndPermissions(): void
    {
        try {
            Log::info('Ensuring roles and permissions are properly set up after restore');

            // Run the roles and permissions seeder
            Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\RolesAndPermissionsSeeder',
                '--force' => true
            ]);

            Log::info('Roles and permissions seeder completed');

            // Verify that users have roles assigned
            $this->verifyUserRoles();

        } catch (\Exception $e) {
            Log::error('Failed to ensure roles and permissions after restore', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Verify that users have roles assigned
     */
    private function verifyUserRoles(): void
    {
        try {
            $usersWithoutRoles = DB::table('users')
                ->leftJoin('model_has_roles', function($join) {
                    $join->on('users.id', '=', 'model_has_roles.model_id')
                         ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
                })
                ->whereNull('model_has_roles.role_id')
                ->count();

            if ($usersWithoutRoles > 0) {
                Log::warning("Found {$usersWithoutRoles} users without roles after restore");

                // Assign default role to users without roles
                $defaultRoleId = DB::table('roles')->where('name', 'user')->value('id');
                if ($defaultRoleId) {
                    $usersWithoutRolesIds = DB::table('users')
                        ->leftJoin('model_has_roles', function($join) {
                            $join->on('users.id', '=', 'model_has_roles.model_id')
                                 ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
                        })
                        ->whereNull('model_has_roles.role_id')
                        ->pluck('users.id');

                    foreach ($usersWithoutRolesIds as $userId) {
                        DB::table('model_has_roles')->insert([
                            'role_id' => $defaultRoleId,
                            'model_type' => 'App\\Models\\User',
                            'model_id' => $userId
                        ]);
                    }

                    Log::info("Assigned default role to {$usersWithoutRoles} users");
                }
            } else {
                Log::info('All users have roles assigned');
            }
        } catch (\Exception $e) {
            Log::error('Failed to verify user roles', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Restore database file (for SQLite)
     */
    private function restoreDatabaseFile(string $tempDir): void
    {
        $driver = config('database.default');

        if ($driver === 'sqlite') {
            $databasePath = config('database.connections.sqlite.database');
            $backupDatabaseFile = $tempDir . '/database/' . basename($databasePath);

            if (file_exists($backupDatabaseFile)) {
                Log::info("Restoring SQLite database file from backup");

                // Backup current database file
                if (file_exists($databasePath)) {
                    $backupPath = $databasePath . '.backup.' . time();
                    if (copy($databasePath, $backupPath)) {
                        Log::info("Backed up current database to: {$backupPath}");
                    } else {
                        Log::error("Failed to backup current database file");
                        throw new \Exception("Failed to backup current database file");
                    }
                }

                // Copy database file from backup
                if (copy($backupDatabaseFile, $databasePath)) {
                    Log::info("Successfully restored database file: {$databasePath}");
                } else {
                    Log::error("Failed to restore database file from backup");
                    throw new \Exception("Failed to restore database file from backup");
                }
            } else {
                Log::warning("Database file not found in backup: {$backupDatabaseFile}");
            }
        }
    }

    /**
     * Recreate storage symlink
     */
    private function recreateStorageSymlink(): void
    {
        try {
            $linkPath = public_path('storage');
            $targetPath = storage_path('app/public');

            // Remove existing symlink if it exists
            if (is_link($linkPath)) {
                unlink($linkPath);
                Log::info('Removed existing storage symlink');
            }

            // Create new symlink
            if (is_dir($targetPath)) {
                symlink($targetPath, $linkPath);
                Log::info('Recreated storage symlink');
            } else {
                // Create the target directory if it doesn't exist
                mkdir($targetPath, 0755, true);
                symlink($targetPath, $linkPath);
                Log::info('Created storage directory and symlink');
            }
        } catch (\Exception $e) {
            Log::error('Failed to recreate storage symlink', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Parse SQL statements with better handling
     */
    private function parseSqlStatements(string $sql): array
    {
        $statements = [];
        $currentStatement = '';
        $inString = false;
        $stringChar = '';
        $escaped = false;

        $lines = explode("\n", $sql);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip comments and empty lines
            if (empty($line) || str_starts_with($line, '--') || str_starts_with($line, '#')) {
                continue;
            }

            // Process character by character to handle strings properly
            for ($i = 0; $i < strlen($line); $i++) {
                $char = $line[$i];

                if ($escaped) {
                    $escaped = false;
                    $currentStatement .= $char;
                    continue;
                }

                if ($char === '\\') {
                    $escaped = true;
                    $currentStatement .= $char;
                    continue;
                }

                if (!$inString && ($char === '"' || $char === "'")) {
                    $inString = true;
                    $stringChar = $char;
                } elseif ($inString && $char === $stringChar) {
                    $inString = false;
                    $stringChar = '';
                } elseif (!$inString && $char === ';') {
                    // End of statement
                    $statement = trim($currentStatement);
                    if (!empty($statement)) {
                        $statements[] = $statement;
                    }
                    $currentStatement = '';
                    continue;
                }

                $currentStatement .= $char;
            }

            // Add space between lines if we're building a multi-line statement
            if (!empty($currentStatement)) {
                $currentStatement .= ' ';
            }
        }

        // Add final statement if exists
        $statement = trim($currentStatement);
        if (!empty($statement)) {
            $statements[] = $statement;
        }

        return $statements;
    }

    /**
     * Get all tables in the database
     */
    private function getAllTables(): array
    {
        $driver = config('database.default');

        switch ($driver) {
            case 'mysql':
                return DB::select('SHOW TABLES');
            case 'sqlite':
                return DB::select("SELECT name FROM sqlite_master WHERE type='table'");
            case 'pgsql':
                return DB::select("SELECT tablename FROM pg_tables WHERE schemaname = 'public'");
            default:
                return [];
        }
    }

    /**
     * Drop all tables
     */
    private function dropAllTables(array $tables): void
    {
        $driver = config('database.default');

        // Disable foreign key checks
        switch ($driver) {
            case 'mysql':
                DB::statement('SET FOREIGN_KEY_CHECKS=0');
                break;
            case 'sqlite':
                DB::statement('PRAGMA foreign_keys = OFF');
                break;
            case 'pgsql':
                // PostgreSQL doesn't have a global foreign key disable
                break;
        }

        foreach ($tables as $table) {
            try {
                $tableName = match ($driver) {
                    'mysql' => array_values((array) $table)[0],
                    'sqlite' => $table->name,
                    'pgsql' => $table->tablename,
                    default => null
                };

                if ($tableName) {
                    // Skip SQLite system tables that cannot be dropped
                    if ($driver === 'sqlite' && in_array($tableName, ['sqlite_sequence', 'sqlite_master', 'sqlite_temp_master'])) {
                        Log::info("Skipping SQLite system table: {$tableName}");
                        continue;
                    }

                    switch ($driver) {
                        case 'mysql':
                            DB::statement("DROP TABLE IF EXISTS `{$tableName}`");
                            break;
                        case 'sqlite':
                            DB::statement("DROP TABLE IF EXISTS \"{$tableName}\"");
                            break;
                        case 'pgsql':
                            DB::statement("DROP TABLE IF EXISTS \"{$tableName}\" CASCADE");
                            break;
                    }
                    Log::info("Dropped table: {$tableName}");
                }
            } catch (\Exception $e) {
                Log::warning("Failed to drop table", [
                    'table' => $tableName ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Re-enable foreign key checks
        switch ($driver) {
            case 'mysql':
                DB::statement('SET FOREIGN_KEY_CHECKS=1');
                break;
            case 'sqlite':
                DB::statement('PRAGMA foreign_keys = ON');
                break;
        }
    }

    /**
     * Sort CREATE TABLE statements to handle dependencies
     */
    private function sortCreateTableStatements(array $statements): array
    {
        $createStatements = [];
        $otherStatements = [];

        // Separate CREATE TABLE statements from others
        foreach ($statements as $statement) {
            if (preg_match('/^\s*CREATE\s+TABLE/i', $statement)) {
                $createStatements[] = $statement;
            } else {
                $otherStatements[] = $statement;
            }
        }

        // Sort CREATE TABLE statements by dependencies
        $sorted = [];
        $remaining = $createStatements;
        $maxIterations = count($createStatements) * 2; // Prevent infinite loops
        $iteration = 0;

        while (!empty($remaining) && $iteration < $maxIterations) {
            $iteration++;
            $progressMade = false;

            foreach ($remaining as $index => $statement) {
                $tableName = $this->extractTableName($statement);

                // Check if this table has dependencies that aren't created yet
                $dependencies = $this->extractForeignKeyDependencies($statement);
                $canCreate = true;

                foreach ($dependencies as $dependency) {
                    if (!$this->isTableInStatements($dependency, $sorted)) {
                        $canCreate = false;
                        break;
                    }
                }

                if ($canCreate) {
                    $sorted[] = $statement;
                    unset($remaining[$index]);
                    $remaining = array_values($remaining); // Re-index array
                    $progressMade = true;
                    break;
                }
            }

            // If no progress was made, add remaining statements anyway to avoid infinite loop
            if (!$progressMade) {
                $sorted = array_merge($sorted, $remaining);
                break;
            }
        }

        // Combine sorted CREATE TABLE statements with other statements
        return array_merge($sorted, $otherStatements);
    }

    /**
     * Extract table name from CREATE TABLE statement
     */
    private function extractTableName(string $statement): string
    {
        if (preg_match('/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?[`"]?(\w+)[`"]?/i', $statement, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * Extract foreign key dependencies from CREATE TABLE statement
     */
    private function extractForeignKeyDependencies(string $statement): array
    {
        $dependencies = [];

        // Match FOREIGN KEY ... REFERENCES table_name patterns
        if (preg_match_all('/FOREIGN\s+KEY.*?REFERENCES\s+[`"]?(\w+)[`"]?/i', $statement, $matches)) {
            $dependencies = array_merge($dependencies, $matches[1]);
        }

        return array_unique($dependencies);
    }

    /**
     * Check if table is already in statements list
     */
    private function isTableInStatements(string $tableName, array $statements): bool
    {
        foreach ($statements as $statement) {
            $statementTableName = $this->extractTableName($statement);
            if (strcasecmp($statementTableName, $tableName) === 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * Clean SQL statement to handle special characters
     */
    private function cleanSqlStatement(string $statement): string
    {
        // Handle problematic characters that cause SQL syntax errors
        $statement = trim($statement);

        // Fix common issues with quotes and escaping
        $statement = str_replace(["\r\n", "\r"], "\n", $statement);

        // Handle escaped quotes in JSON data
        $statement = preg_replace('/\\\\(["\'])/', '$1$1', $statement);

        return $statement;
    }

    /**
     * Validate SQL statement
     */
    private function isValidSqlStatement(string $statement): bool
    {
        $statement = trim($statement);

        // Skip empty statements
        if (empty($statement)) {
            return false;
        }

        // Skip comments
        if (str_starts_with($statement, '--') || str_starts_with($statement, '#')) {
            return false;
        }

        // Basic validation for common SQL statements
        $validPatterns = [
            '/^\s*CREATE\s+/i',
            '/^\s*INSERT\s+/i',
            '/^\s*UPDATE\s+/i',
            '/^\s*DELETE\s+/i',
            '/^\s*ALTER\s+/i',
            '/^\s*DROP\s+/i',
            '/^\s*SET\s+/i',
            '/^\s*PRAGMA\s+/i',
        ];

        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $statement)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get Composer version
     */
    private function getComposerVersion(): ?string
    {
        try {
            $output = shell_exec('composer --version 2>/dev/null');
            if ($output && preg_match('/Composer version ([^\s]+)/', $output, $matches)) {
                return $matches[1];
            }
        } catch (\Exception $e) {
            // Ignore errors
        }
        return null;
    }

    /**
     * Get Node.js version
     */
    private function getNodeVersion(): ?string
    {
        try {
            $output = shell_exec('node --version 2>/dev/null');
            return $output ? trim($output) : null;
        } catch (\Exception $e) {
            // Ignore errors
        }
        return null;
    }

    /**
     * Get NPM version
     */
    private function getNpmVersion(): ?string
    {
        try {
            $output = shell_exec('npm --version 2>/dev/null');
            return $output ? trim($output) : null;
        } catch (\Exception $e) {
            // Ignore errors
        }
        return null;
    }

    /**
     * Add environment-specific configurations to backup
     */
    private function addEnvironmentConfigurations(ZipArchive $zip): void
    {
        Log::info('Adding environment-specific configurations to backup');

        // Add .env file with sensitive data masked for security
        $envFile = base_path('.env');
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);

            // Create a sanitized version for backup (mask sensitive values)
            $sanitizedEnv = $this->sanitizeEnvContent($envContent);
            $zip->addFromString('environment/.env.backup', $sanitizedEnv);

            // Also add the original .env file (this will be restored as-is)
            $zip->addFile($envFile, '.env');

            Log::info('Added .env file to backup');
        }

        // Add other environment files if they exist
        $envFiles = [
            '.env.example',
            '.env.local',
            '.env.production',
            '.env.staging',
            '.env.testing',
        ];

        foreach ($envFiles as $envFileName) {
            $envFilePath = base_path($envFileName);
            if (file_exists($envFilePath)) {
                $zip->addFile($envFilePath, "environment/{$envFileName}");
                Log::info("Added environment file to backup: {$envFileName}");
            }
        }

        // Add configuration cache if it exists
        $configCache = base_path('bootstrap/cache/config.php');
        if (file_exists($configCache)) {
            $zip->addFile($configCache, 'cache/config.php');
            Log::info('Added configuration cache to backup');
        }

        // Add route cache if it exists
        $routeCache = base_path('bootstrap/cache/routes-v7.php');
        if (file_exists($routeCache)) {
            $zip->addFile($routeCache, 'cache/routes-v7.php');
            Log::info('Added route cache to backup');
        }

        Log::info('Environment-specific configurations added to backup');
    }

    /**
     * Sanitize .env content for backup (mask sensitive values)
     */
    private function sanitizeEnvContent(string $content): string
    {
        $lines = explode("\n", $content);
        $sanitizedLines = [];

        $sensitiveKeys = [
            'DB_PASSWORD',
            'MAIL_PASSWORD',
            'AWS_SECRET_ACCESS_KEY',
            'STRIPE_SECRET',
            'PAYPAL_SECRET',
            'JWT_SECRET',
            'APP_KEY',
            'ENCRYPTION_KEY',
            'API_KEY',
            'SECRET_KEY',
            'PRIVATE_KEY',
            'PASSWORD',
            'TOKEN',
            'SECRET',
        ];

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines and comments
            if (empty($line) || str_starts_with($line, '#')) {
                $sanitizedLines[] = $line;
                continue;
            }

            // Check if this line contains a sensitive key
            $isSensitive = false;
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (str_starts_with($line, $sensitiveKey . '=')) {
                    $sanitizedLines[] = $sensitiveKey . '=***MASKED***';
                    $isSensitive = true;
                    break;
                }
            }

            if (!$isSensitive) {
                $sanitizedLines[] = $line;
            }
        }

        return implode("\n", $sanitizedLines);
    }
}
