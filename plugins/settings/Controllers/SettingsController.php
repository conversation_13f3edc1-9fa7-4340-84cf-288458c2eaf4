<?php

namespace Plugins\Settings\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_settings')) {
                abort(403, 'Access denied. You do not have permission to manage settings.');
            }

            return $next($request);
        });
    }

    /**
     * Display the settings dashboard
     */
    public function index(): View
    {
        // Get quick stats for dashboard
        $stats = $this->getDashboardStats();
        
        return view('plugins.settings::index', compact('stats'));
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        try {
            // System stats
            $systemStats = [
                'app_version' => config('app.version', '1.0.0'),
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_driver' => config('database.default'),
                'environment' => config('app.env'),
                'debug_mode' => config('app.debug'),
            ];

            // Database stats
            $databaseStats = [
                'total_tables' => $this->getTotalTables(),
                'database_size' => $this->getDatabaseSize(),
                'last_backup' => $this->getLastBackupDate(),
            ];

            // Backup stats
            $backupStats = [
                'total_backups' => $this->getTotalBackups(),
                'backup_storage_used' => $this->getBackupStorageUsed(),
                'oldest_backup' => $this->getOldestBackupDate(),
            ];

            return [
                'system' => $systemStats,
                'database' => $databaseStats,
                'backups' => $backupStats,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get dashboard stats: ' . $e->getMessage());
            
            return [
                'system' => ['error' => 'Unable to load system stats'],
                'database' => ['error' => 'Unable to load database stats'],
                'backups' => ['error' => 'Unable to load backup stats'],
            ];
        }
    }

    /**
     * Get total number of database tables
     */
    private function getTotalTables(): int
    {
        try {
            $driver = config('database.default');
            $database = config("database.connections.{$driver}.database");
            
            switch ($driver) {
                case 'mysql':
                    return DB::select("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?", [$database])[0]->count;
                case 'sqlite':
                    return DB::select("SELECT COUNT(*) as count FROM sqlite_master WHERE type = 'table'")[0]->count;
                case 'pgsql':
                    return DB::select("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_catalog = ?", [$database])[0]->count;
                default:
                    return 0;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get table count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get database size
     */
    private function getDatabaseSize(): string
    {
        try {
            $driver = config('database.default');
            $database = config("database.connections.{$driver}.database");
            
            switch ($driver) {
                case 'mysql':
                    $result = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = ?", [$database]);
                    return ($result[0]->size_mb ?? 0) . ' MB';
                case 'sqlite':
                    $path = database_path('database.sqlite');
                    if (file_exists($path)) {
                        return round(filesize($path) / 1024 / 1024, 2) . ' MB';
                    }
                    return '0 MB';
                case 'pgsql':
                    $result = DB::select("SELECT pg_size_pretty(pg_database_size(?)) as size", [$database]);
                    return $result[0]->size ?? '0 MB';
                default:
                    return 'Unknown';
            }
        } catch (\Exception $e) {
            Log::error('Failed to get database size: ' . $e->getMessage());
            return 'Unknown';
        }
    }

    /**
     * Get last backup date
     */
    private function getLastBackupDate(): ?string
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return null;
            }

            $files = glob($backupPath . '/*.zip');
            if (empty($files)) {
                return null;
            }

            $latestFile = max(array_map('filemtime', $files));
            return date('Y-m-d H:i:s', $latestFile);
        } catch (\Exception $e) {
            Log::error('Failed to get last backup date: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get total number of backups
     */
    private function getTotalBackups(): int
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return 0;
            }

            return count(glob($backupPath . '/*.zip'));
        } catch (\Exception $e) {
            Log::error('Failed to get backup count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get backup storage used
     */
    private function getBackupStorageUsed(): string
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return '0 MB';
            }

            $files = glob($backupPath . '/*.zip');
            $totalSize = 0;
            
            foreach ($files as $file) {
                $totalSize += filesize($file);
            }

            return round($totalSize / 1024 / 1024, 2) . ' MB';
        } catch (\Exception $e) {
            Log::error('Failed to get backup storage size: ' . $e->getMessage());
            return 'Unknown';
        }
    }

    /**
     * Get oldest backup date
     */
    private function getOldestBackupDate(): ?string
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return null;
            }

            $files = glob($backupPath . '/*.zip');
            if (empty($files)) {
                return null;
            }

            $oldestFile = min(array_map('filemtime', $files));
            return date('Y-m-d H:i:s', $oldestFile);
        } catch (\Exception $e) {
            Log::error('Failed to get oldest backup date: ' . $e->getMessage());
            return null;
        }
    }
}
