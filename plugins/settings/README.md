# Settings Plugin

A comprehensive system management plugin for Laravel applications that provides system information monitoring, update management, and backup functionality.

## Features

### 🖥️ System Information
- **Application Details**: Version, environment, debug status
- **Server Information**: PHP version, server software, memory limits
- **Database Information**: Driver, version, connection status
- **Storage Information**: Disk space, permissions, writability checks
- **Performance Metrics**: Memory usage, load average, uptime

### 🔄 Update Management
- **Update Checking**: Automatic and manual update detection
- **Version Comparison**: Current vs. latest version display
- **Release Notes**: Detailed changelog and update information
- **Update Installation**: Automated update process with backup creation
- **Update Channels**: Support for stable, beta, and alpha releases

### 💾 Backup Management
- **Full Backups**: Complete application and database backups
- **Database-Only Backups**: Quick database-only backup option
- **Backup Listing**: View all available backups with details
- **Backup Restoration**: Restore from any backup file
- **Backup Download**: Download backup files for external storage
- **Automatic Cleanup**: Configurable retention policies

### 🔍 Health Monitoring
- **System Health Checks**: Database, storage, cache, queue monitoring
- **Real-time Status**: Live system status indicators
- **Performance Monitoring**: Memory usage and system load tracking
- **Error Detection**: Automatic issue identification and reporting

## Installation

The Settings plugin follows the standard plugin architecture:

1. **Plugin Structure**: Located in `plugins/settings/`
2. **Auto-Discovery**: Automatically detected by the plugin manager
3. **Permission Integration**: Uses `manage_settings` permission
4. **Navigation Integration**: Adds settings menu to sidebar

## Configuration

### Environment Variables

```env
# Backup Configuration
SETTINGS_MAX_BACKUP_FILES=10
SETTINGS_BACKUP_RETENTION_DAYS=30
SETTINGS_MAX_BACKUP_SIZE=**********

# Update Configuration
SETTINGS_UPDATE_SERVER=https://your-update-server.com
SETTINGS_UPDATE_CHANNEL=stable
SETTINGS_AUTO_BACKUP_BEFORE_UPDATE=true

# Monitoring Configuration
SETTINGS_HEALTH_CHECK_CACHE=300
SETTINGS_SYSTEM_INFO_CACHE=3600
```

### Configuration File

The plugin includes a comprehensive configuration file at `config/settings.php`:

```php
return [
    'backup_path' => storage_path('app/backups'),
    'max_backup_files' => env('SETTINGS_MAX_BACKUP_FILES', 10),
    'backup_retention_days' => env('SETTINGS_BACKUP_RETENTION_DAYS', 30),
    'update_server' => env('SETTINGS_UPDATE_SERVER', null),
    'update_channel' => env('SETTINGS_UPDATE_CHANNEL', 'stable'),
    // ... more configuration options
];
```

## Usage

### Accessing Settings

1. **Navigation**: Use the Settings menu in the sidebar
2. **Permissions**: Requires `manage_settings` permission
3. **Dashboard**: Overview of system status and quick actions

### System Information

- **View Details**: Complete system configuration and status
- **Health Check**: Real-time system health monitoring
- **PHP Info**: Detailed PHP configuration (admin only)

### Backup Management

```php
// Create a backup programmatically
$backupController = new BackupController();
$result = $backupController->create(new Request([
    'include_files' => true,
    'description' => 'Scheduled backup'
]));
```

### Update Management

- **Check Updates**: Manual or automatic update checking
- **Install Updates**: Automated installation with pre-update backup
- **View Changelog**: Detailed release notes and changes

## Security Features

### Access Control
- **Permission-Based**: All features require `manage_settings` permission
- **Admin-Only**: Sensitive operations restricted to administrators
- **Audit Logging**: All actions logged for security tracking

### Backup Security
- **Secure Storage**: Backups stored in protected directory
- **File Validation**: Backup file integrity checking
- **Access Restrictions**: Download protection and validation

### Update Security
- **Version Validation**: Prevents downgrade attacks
- **Backup Creation**: Automatic backup before updates
- **Rollback Support**: Ability to restore from pre-update backup

## Database Compatibility

The Settings plugin supports all major database drivers:

- **MySQL**: Full backup and restore support
- **PostgreSQL**: Complete database dump functionality
- **SQLite**: File-based backup and restore
- **SQL Server**: Enterprise database support

## File Structure

```
plugins/settings/
├── Controllers/
│   ├── SettingsController.php      # Main dashboard controller
│   ├── SystemInfoController.php    # System information and health
│   ├── UpdateController.php        # Update management
│   └── BackupController.php        # Backup operations
├── Views/
│   ├── index.blade.php             # Settings dashboard
│   ├── system-info.blade.php       # System information page
│   ├── phpinfo.blade.php           # PHP information page
│   ├── updates.blade.php           # Update management page
│   └── backups.blade.php           # Backup management page
├── config/
│   └── settings.php                # Plugin configuration
├── plugin.json                     # Plugin metadata
├── routes.php                      # Plugin routes
├── SettingsServiceProvider.php     # Service provider
└── README.md                       # This file
```

## API Endpoints

### System Information
- `GET /settings/system/` - System information page
- `GET /settings/system/health-check` - Health check API
- `GET /settings/system/phpinfo` - PHP information page

### Update Management
- `GET /settings/updates/` - Update management page
- `POST /settings/updates/check` - Check for updates
- `GET /settings/updates/changelog` - Get changelog
- `POST /settings/updates/install/{version}` - Install update

### Backup Management
- `GET /settings/backups/` - Backup management page
- `POST /settings/backups/create` - Create new backup
- `GET /settings/backups/download/{backup}` - Download backup
- `POST /settings/backups/restore/{backup}` - Restore backup
- `DELETE /settings/backups/{backup}` - Delete backup
- `GET /settings/backups/status/{backup}` - Get backup info

## Permissions

The plugin uses a single permission for all functionality:

- **`manage_settings`**: Access to all settings features
  - System information viewing
  - Health check execution
  - Backup creation and management
  - Update checking and installation
  - Configuration management

## Logging

All significant actions are logged for audit purposes:

```php
Log::info('Backup created', [
    'backup_file' => $filename,
    'user' => auth()->user()->email,
    'include_files' => $includeFiles
]);
```

## Error Handling

The plugin includes comprehensive error handling:

- **Graceful Degradation**: Continues operation when non-critical features fail
- **User Feedback**: Clear error messages and status indicators
- **Logging**: All errors logged for debugging
- **Recovery**: Automatic recovery mechanisms where possible

## Performance Considerations

- **Caching**: System information cached to reduce overhead
- **Background Processing**: Long-running operations handled asynchronously
- **Resource Management**: Memory and disk space monitoring
- **Optimization**: Efficient database queries and file operations

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure user has `manage_settings` permission
2. **Backup Failures**: Check storage permissions and disk space
3. **Update Failures**: Verify network connectivity and server configuration
4. **Health Check Errors**: Review system logs for specific issues

### Debug Mode

Enable debug mode for detailed error information:

```env
APP_DEBUG=true
LOG_LEVEL=debug
```

## Contributing

When contributing to the Settings plugin:

1. **Follow Standards**: Use existing code style and patterns
2. **Test Thoroughly**: Test all database drivers and configurations
3. **Document Changes**: Update README and inline documentation
4. **Security Review**: Ensure all changes maintain security standards

## License

This plugin is part of the main application and follows the same licensing terms.
