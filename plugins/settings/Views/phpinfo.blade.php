<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Information</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            color: #333;
        }
        .header p {
            margin: 5px 0 0 0;
            color: #666;
        }
        .phpinfo-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .phpinfo-container table {
            width: 100%;
            border-collapse: collapse;
        }
        .phpinfo-container th,
        .phpinfo-container td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .phpinfo-container th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .phpinfo-container tr:hover {
            background-color: #f8f9fa;
        }
        .back-button {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 20px;
            transition: background-color 0.2s;
        }
        .back-button:hover {
            background: #2563eb;
        }
        .warning {
            background: #fef3cd;
            border: 1px solid #fecaca;
            color: #92400e;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .warning strong {
            display: block;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <a href="{{ route('settings.system.info') }}" class="back-button">
        ← Back to System Information
    </a>

    <div class="header">
        <h1>PHP Information</h1>
        <p>Complete PHP configuration and environment details</p>
    </div>

    <div class="warning">
        <strong>Security Notice:</strong>
        This page contains sensitive system information. Ensure this is only accessible to authorized administrators.
    </div>

    <div class="phpinfo-container">
        {!! $phpInfo !!}
    </div>

    <script>
        // Clean up the phpinfo output for better presentation
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the default phpinfo styling
            const style = document.querySelector('style');
            if (style && style.textContent.includes('phpinfo')) {
                style.remove();
            }

            // Remove any inline styles from phpinfo
            const elements = document.querySelectorAll('[style]');
            elements.forEach(el => {
                if (el.closest('.phpinfo-container')) {
                    el.removeAttribute('style');
                }
            });

            // Add responsive behavior for tables
            const tables = document.querySelectorAll('.phpinfo-container table');
            tables.forEach(table => {
                table.style.fontSize = '14px';
                table.style.lineHeight = '1.5';
            });
        });
    </script>
</body>
</html>
