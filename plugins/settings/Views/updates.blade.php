@extends('layouts.app')

@section('title', 'Update Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Demo Notice -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Demo Update System</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>This is a demonstration of the update system. In a real application, updates would be fetched from a secure server and properly validated before installation.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Update Management</h1>
                <p class="text-gray-600 mt-2">Check for and install application updates</p>
            </div>
            <button onclick="checkForUpdates()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-sync-alt mr-2"></i>Check for Updates
            </button>
        </div>
    </div>

    <!-- Current Version Info -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Current Version</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">{{ $updateInfo['current_version'] }}</div>
                    <div class="text-sm text-gray-500 mt-1">Installed Version</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold {{ $updateInfo['has_updates'] ? 'text-orange-600' : 'text-green-600' }}">
                        {{ $updateInfo['latest_version'] }}
                    </div>
                    <div class="text-sm text-gray-500 mt-1">Latest Version</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-medium">
                        @if($updateInfo['has_updates'])
                            <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full">Update Available</span>
                        @else
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full">Up to Date</span>
                        @endif
                    </div>
                    <div class="text-sm text-gray-500 mt-1">Status</div>
                </div>
            </div>

            <!-- Update Source Information -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Update Source</h4>
                        <p class="text-sm text-gray-500">Demo System (Simulated)</p>
                    </div>
                    <div class="text-right">
                        <h4 class="text-sm font-medium text-gray-900">Update Channel</h4>
                        <p class="text-sm text-gray-500">{{ $updateInfo['update_channel'] ?? 'stable' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status -->
    <div id="updateStatus" class="hidden mb-6"></div>

    <!-- Available Updates -->
    @if($updateInfo['has_updates'])
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Available Updates</h3>
        </div>
        <div class="p-6">
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <div class="flex">
                    <i class="fas fa-info-circle text-blue-400 mr-3 mt-1"></i>
                    <div>
                        <h4 class="text-blue-800 font-medium">Update Available: {{ $updateInfo['latest_version'] }}</h4>
                        <p class="text-blue-700 mt-1">A new version is available for installation.</p>
                    </div>
                </div>
            </div>

            @if(isset($updateInfo['release_notes']) && !empty($updateInfo['release_notes']))
                @foreach($updateInfo['release_notes'] as $version => $notes)
                <div class="border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Version {{ $version }}</h4>
                        <span class="text-sm text-gray-500">{{ $notes['date'] ?? 'Unknown date' }}</span>
                    </div>
                    
                    @if(isset($notes['security_fixes']) && $notes['security_fixes'])
                        <div class="mb-3">
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Security Fixes</span>
                        </div>
                    @endif
                    
                    @if(isset($notes['breaking_changes']) && $notes['breaking_changes'])
                        <div class="mb-3">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Breaking Changes</span>
                        </div>
                    @endif

                    @if(isset($notes['changes']) && is_array($notes['changes']))
                        <ul class="list-disc list-inside space-y-1 text-gray-700">
                            @foreach($notes['changes'] as $change)
                                <li>{{ $change }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
                @endforeach
            @endif

            <div class="mt-6 flex space-x-3">
                <button onclick="installUpdate('{{ $updateInfo['latest_version'] }}')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-download mr-2"></i>Install Update
                </button>
                <button onclick="viewChangelog()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-list mr-2"></i>View Full Changelog
                </button>
            </div>
        </div>
    </div>
    @endif

    <!-- Update Settings -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Update Settings</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Update Channel</label>
                    <select class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="stable" {{ ($updateInfo['update_channel'] ?? 'stable') === 'stable' ? 'selected' : '' }}>Stable</option>
                        <option value="beta" {{ ($updateInfo['update_channel'] ?? 'stable') === 'beta' ? 'selected' : '' }}>Beta</option>
                        <option value="alpha" {{ ($updateInfo['update_channel'] ?? 'stable') === 'alpha' ? 'selected' : '' }}>Alpha</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Choose which type of updates to receive</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Check</label>
                    <div class="text-gray-900">
                        {{ $updateInfo['last_check'] ? \Carbon\Carbon::parse($updateInfo['last_check'])->diffForHumans() : 'Never' }}
                    </div>
                    <p class="text-xs text-gray-500 mt-1">When updates were last checked</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Update History -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Update History</h3>
        </div>
        <div class="p-6">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-history text-4xl mb-4"></i>
                <p>Update history will appear here</p>
                <p class="text-sm">Previous installations and their details will be tracked</p>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span id="loadingText">Processing...</span>
        </div>
    </div>
</div>

<!-- Changelog Modal -->
<div id="changelogModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-4xl mx-auto max-h-96 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Changelog</h3>
            <button onclick="closeChangelog()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="changelogContent" class="p-6 overflow-y-auto max-h-80">
            <!-- Changelog content will be loaded here -->
        </div>
    </div>
</div>

<script>
function checkForUpdates() {
    showLoading('Checking for updates...');
    
    fetch('{{ route('settings.updates.check') }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showUpdateStatus(data);
        
        if (data.success && data.has_updates) {
            setTimeout(() => location.reload(), 2000);
        }
    })
    .catch(error => {
        hideLoading();
        showUpdateStatus({
            success: false,
            message: 'Update check failed: ' + error.message
        });
    });
}

function installUpdate(version) {
    if (confirm(`Install update to version ${version}? This will create a backup first and may take several minutes.`)) {
        showLoading('Installing update...');
        
        fetch(`{{ url('settings/updates/install') }}/${version}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                confirm: true
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            showUpdateStatus(data);
            
            if (data.success) {
                if (data.restart_required) {
                    setTimeout(() => {
                        alert('Update installed successfully. The application will restart.');
                        location.reload();
                    }, 2000);
                } else {
                    setTimeout(() => location.reload(), 2000);
                }
            }
        })
        .catch(error => {
            hideLoading();
            showUpdateStatus({
                success: false,
                message: 'Update installation failed: ' + error.message
            });
        });
    }
}

function viewChangelog() {
    showLoading('Loading changelog...');
    
    fetch('{{ route('settings.updates.changelog') }}')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showChangelog(data.changelog);
            } else {
                alert('Failed to load changelog: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading();
            alert('Failed to load changelog: ' + error.message);
        });
}

function showChangelog(changelog) {
    let html = '';
    
    Object.entries(changelog).forEach(([version, notes]) => {
        html += `
            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-lg font-medium text-gray-900">Version ${version}</h4>
                    <span class="text-sm text-gray-500">${notes.date || 'Unknown date'}</span>
                </div>
        `;
        
        if (notes.security_fixes) {
            html += '<div class="mb-3"><span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Security Fixes</span></div>';
        }
        
        if (notes.breaking_changes) {
            html += '<div class="mb-3"><span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Breaking Changes</span></div>';
        }
        
        if (notes.changes && Array.isArray(notes.changes)) {
            html += '<ul class="list-disc list-inside space-y-1 text-gray-700">';
            notes.changes.forEach(change => {
                html += `<li>${change}</li>`;
            });
            html += '</ul>';
        }
        
        html += '</div>';
    });
    
    document.getElementById('changelogContent').innerHTML = html;
    document.getElementById('changelogModal').classList.remove('hidden');
    document.getElementById('changelogModal').classList.add('flex');
}

function closeChangelog() {
    document.getElementById('changelogModal').classList.add('hidden');
    document.getElementById('changelogModal').classList.remove('flex');
}

function showUpdateStatus(data) {
    const statusDiv = document.getElementById('updateStatus');
    statusDiv.classList.remove('hidden');
    
    if (data.success) {
        statusDiv.className = 'mb-6 p-4 rounded-md border bg-green-50 border-green-200';
        statusDiv.innerHTML = `
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="text-green-800 font-medium">Success</h4>
                    <p class="text-green-700 mt-1">${data.message || 'Operation completed successfully'}</p>
                </div>
            </div>
        `;
    } else {
        statusDiv.className = 'mb-6 p-4 rounded-md border bg-red-50 border-red-200';
        statusDiv.innerHTML = `
            <div class="flex">
                <i class="fas fa-times-circle text-red-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="text-red-800 font-medium">Error</h4>
                    <p class="text-red-700 mt-1">${data.message || 'Operation failed'}</p>
                </div>
            </div>
        `;
    }
}

function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}
</script>
@endsection
