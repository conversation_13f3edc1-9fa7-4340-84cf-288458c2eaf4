@extends('layouts.app')

@section('title', 'Settings Dashboard')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Settings Dashboard</h1>
        <p class="text-gray-600 mt-2">System management and configuration</p>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- System Info Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-server text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">System Status</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @if(isset($stats['system']['error']))
                                    <span class="text-red-600">Error</span>
                                @else
                                    <span class="text-green-600">Healthy</span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-500">
                        <p>App Version: {{ $stats['system']['app_version'] ?? 'Unknown' }}</p>
                        <p>PHP Version: {{ $stats['system']['php_version'] ?? 'Unknown' }}</p>
                        <p>Environment: {{ $stats['system']['environment'] ?? 'Unknown' }}</p>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('settings.system.info') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View Details →
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Info Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-database text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Database</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @if(isset($stats['database']['error']))
                                    <span class="text-red-600">Error</span>
                                @else
                                    <span class="text-green-600">Connected</span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-500">
                        <p>Tables: {{ $stats['database']['total_tables'] ?? 'Unknown' }}</p>
                        <p>Size: {{ $stats['database']['database_size'] ?? 'Unknown' }}</p>
                        <p>Last Backup: {{ $stats['database']['last_backup'] ? \Carbon\Carbon::parse($stats['database']['last_backup'])->diffForHumans() : 'Never' }}</p>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('settings.backups.index') }}" class="text-green-600 hover:text-green-800 text-sm font-medium">
                            Manage Backups →
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Info Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-archive text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Backups</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ $stats['backups']['total_backups'] ?? 0 }} Files
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-500">
                        <p>Storage Used: {{ $stats['backups']['backup_storage_used'] ?? '0 MB' }}</p>
                        <p>Oldest: {{ $stats['backups']['oldest_backup'] ? \Carbon\Carbon::parse($stats['backups']['oldest_backup'])->diffForHumans() : 'None' }}</p>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('settings.backups.index') }}" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                            View Backups →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- System Information -->
                <a href="{{ route('settings.system.info') }}" class="group block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-medium text-gray-900 group-hover:text-blue-600">System Info</h4>
                            <p class="text-sm text-gray-500">View system details</p>
                        </div>
                    </div>
                </a>

                <!-- Health Check -->
                <button onclick="performHealthCheck()" class="group block w-full p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:shadow-md transition-all duration-200 text-left">
                    <div class="flex items-center">
                        <i class="fas fa-heartbeat text-green-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-medium text-gray-900 group-hover:text-green-600">Health Check</h4>
                            <p class="text-sm text-gray-500">Check system health</p>
                        </div>
                    </div>
                </button>

                <!-- Create Backup -->
                <button onclick="createBackup()" class="group block w-full p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all duration-200 text-left">
                    <div class="flex items-center">
                        <i class="fas fa-download text-purple-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-medium text-gray-900 group-hover:text-purple-600">Create Backup</h4>
                            <p class="text-sm text-gray-500">Backup system data</p>
                        </div>
                    </div>
                </button>

                <!-- Check Updates -->
                <button onclick="checkForUpdates()" class="group block w-full p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:shadow-md transition-all duration-200 text-left">
                    <div class="flex items-center">
                        <i class="fas fa-sync-alt text-orange-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-medium text-gray-900 group-hover:text-orange-600">Check Updates</h4>
                            <p class="text-sm text-gray-500">Look for updates</p>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-clock text-4xl mb-4"></i>
                <p>Activity logging will appear here</p>
                <p class="text-sm">System events, backups, and updates will be tracked</p>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span id="loadingText">Processing...</span>
        </div>
    </div>
</div>

<script>
function performHealthCheck() {
    showLoading('Performing health check...');
    
    fetch('{{ route('settings.system.health-check') }}')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                const status = data.overall_health === 'healthy' ? 'success' : 'warning';
                showToast(`System health: ${data.overall_health}`, status);
            } else {
                showToast('Health check failed: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('Health check failed: ' + error.message, 'error');
        });
}

function createBackup() {
    if (confirm('Create a new backup? This may take several minutes.')) {
        showLoading('Creating backup...');
        
        fetch('{{ route('settings.backups.create') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                include_files: true,
                description: 'Quick backup from dashboard'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showToast('Backup created successfully', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('Backup failed: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('Backup failed: ' + error.message, 'error');
        });
    }
}

function checkForUpdates() {
    showLoading('Checking for updates...');
    
    fetch('{{ route('settings.updates.check') }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            if (data.has_updates) {
                showToast(`Update available: ${data.update_info.latest_version}`, 'info');
                setTimeout(() => window.location.href = '{{ route('settings.updates.index') }}', 1500);
            } else {
                showToast('System is up to date', 'success');
            }
        } else {
            showToast('Update check failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('Update check failed: ' + error.message, 'error');
    });
}

function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}

function showToast(message, type) {
    // This would integrate with your existing toast notification system
    alert(message); // Fallback for demo
}
</script>
@endsection
