@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create New Permission</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500">Add a new permission to the system</p>
        </div>
        <a href="{{ route('permissions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Permissions
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:p-6">
            <form method="POST" action="{{ route('permissions.store') }}" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500">Provide basic details about the permission.</p>
                    </div>

                    <!-- Display Name -->
                    <div>
                        <label for="display_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Display Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="display_name" id="display_name" value="{{ old('display_name') }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md @error('display_name') border-red-300 @enderror"
                                   placeholder="e.g., Manage Blog Posts">
                        </div>
                        @error('display_name')
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- System Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            System Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md @error('name') border-red-300 @enderror"
                                   placeholder="e.g., manage_blog_posts">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Lowercase letters, numbers, and underscores only. Used internally by the system.</p>
                        @error('name')
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Plugin -->
                    <div>
                        <label for="plugin" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Plugin
                        </label>
                        <div class="mt-1">
                            <select name="plugin" id="plugin"
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md @error('plugin') border-red-300 @enderror">
                                <option value="">System Permission</option>
                                @php
                                    $pluginManager = app(\App\Services\PluginManager::class);
                                    $enabledPlugins = $pluginManager->getEnabledPlugins();
                                @endphp
                                @foreach($enabledPlugins as $pluginName => $plugin)
                                    <option value="{{ $pluginName }}" {{ old('plugin') == $pluginName ? 'selected' : '' }}>
                                        {{ $plugin->config['display_name'] ?? ucfirst($pluginName) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Select the plugin this permission belongs to, or leave empty for system permissions.</p>
                        @error('plugin')
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Description
                        </label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3"
                                      class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md @error('description') border-red-300 @enderror"
                                      placeholder="Describe what this permission allows users to do...">{{ old('description') }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('permissions.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Create Permission
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Permission Examples -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Permission Examples</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Common permission patterns and naming conventions.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Content Management</h4>
                        <div class="space-y-2">
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Create Blog Posts</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">create_blog_posts</p>
                            </div>
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Edit Blog Posts</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">edit_blog_posts</p>
                            </div>
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Delete Blog Posts</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">delete_blog_posts</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">System Administration</h4>
                        <div class="space-y-2">
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">View System Logs</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">view_system_logs</p>
                            </div>
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Manage Settings</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">manage_settings</p>
                            </div>
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Export Data</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">export_data</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Text -->
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 transition duration-150 ease-in-out">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Permission Creation Guidelines
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc pl-5 space-y-1">
                        <li>System names should be unique and use lowercase letters, numbers, and underscores only</li>
                        <li>Display names are shown to users and can contain spaces and special characters</li>
                        <li>Use descriptive names that clearly indicate what the permission allows</li>
                        <li>Follow naming conventions: action_resource (e.g., create_posts, manage_users)</li>
                        <li>Permissions can be assigned to roles after creation</li>
                        <li>Consider grouping related permissions logically</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate system name from display name
    const displayNameInput = document.getElementById('display_name');
    const nameInput = document.getElementById('name');
    
    if (displayNameInput && nameInput) {
        displayNameInput.addEventListener('input', function() {
            if (!nameInput.value || nameInput.dataset.autoGenerated !== 'false') {
                const systemName = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .replace(/_{2,}/g, '_')
                    .replace(/^_|_$/g, '');
                nameInput.value = systemName;
                nameInput.dataset.autoGenerated = 'true';
            }
        });
        
        nameInput.addEventListener('input', function() {
            this.dataset.autoGenerated = 'false';
        });
    }
    
    // Click to copy examples
    const examples = document.querySelectorAll('.bg-gray-50 dark:bg-gray-700.rounded-lg');
    examples.forEach(example => {
        example.addEventListener('click', function() {
            const systemName = this.querySelector('.text-xs.text-gray-600 dark:text-gray-400 dark:text-gray-500').textContent;
            const displayName = this.querySelector('.text-sm.font-medium.text-gray-900 dark:text-white').textContent;
            
            if (displayNameInput && nameInput) {
                displayNameInput.value = displayName;
                nameInput.value = systemName;
                nameInput.dataset.autoGenerated = 'false';
                
                // Add visual feedback
                this.classList.add('bg-primary-100', 'border-primary-300');
                setTimeout(() => {
                    this.classList.remove('bg-primary-100', 'border-primary-300');
                }, 1000);
            }
        });
        
        // Add cursor pointer to indicate clickability
        example.style.cursor = 'pointer';
        example.title = 'Click to use this example';
    });
});
</script>
@endsection
