@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $permission->display_name }}</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500">Permission details and assigned roles</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('permissions.edit', $permission) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <i class="fas fa-edit mr-2"></i>
                Edit Permission
            </a>
            <a href="{{ route('permissions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Permissions
            </a>
        </div>
    </div>

    <!-- Permission Information -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Permission Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Basic details about this permission.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <dl>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Display Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $permission->display_name }}</dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">System Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $permission->name }}</dd>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $permission->description ?: 'No description provided' }}</dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Plugin</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $permission->plugin ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' }} transition duration-150 ease-in-out">
                            <i class="fas {{ $permission->plugin ? 'fa-puzzle-piece' : 'fa-cog' }} mr-1"></i>
                            {{ $permission->plugin_display_name }}
                        </span>
                    </dd>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Type</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                        @php
                            $systemPermissions = ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard', 'manage_navigation', 'view_navigation'];
                        @endphp
                        @if(in_array($permission->name, $systemPermissions))
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                <i class="fas fa-cog mr-1"></i>
                                System Permission
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                <i class="fas fa-user mr-1"></i>
                                Custom Permission
                            </span>
                        @endif
                    </dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Users</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                            <i class="fas fa-users mr-1"></i>
                            {{ $permission->total_users_count }} {{ Str::plural('user', $permission->total_users_count) }}
                        </span>
                    </dd>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Created</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $permission->created_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Updated</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $permission->updated_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Assigned Roles -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Roles with this Permission</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Roles that have been granted this permission.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            @if($permission->roles->count() > 0)
                <div class="px-4 py-5 sm:px-6">
                    <div class="space-y-3">
                        @foreach($permission->roles as $role)
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200 transition duration-150 ease-in-out">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-shield text-2xl text-blue-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-blue-900">{{ $role->display_name }}</p>
                                        <p class="text-xs text-blue-700">{{ $role->name }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @php
                                        $totalUsers = ($role->users_count ?? 0) + ($role->users_many_count ?? 0);
                                    @endphp
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                        {{ $totalUsers }} {{ Str::plural('user', $totalUsers) }}
                                    </span>
                                    <a href="{{ route('roles.show', $role) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Role">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="px-4 py-5 sm:px-6">
                    <div class="text-center py-8">
                        <i class="fas fa-user-shield text-4xl text-gray-300 mb-4"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">No roles have been assigned this permission.</p>
                        <a href="{{ route('roles.create') }}" class="mt-2 inline-flex items-center text-sm text-primary-600 hover:text-primary-500">
                            <i class="fas fa-plus mr-1"></i>
                            Create Role
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Users with this Permission -->
    @if($permission->roles->count() > 0)
        @php
            $usersWithPermission = collect();
            foreach($permission->roles as $role) {
                $usersWithPermission = $usersWithPermission->merge($role->users);
            }
            $usersWithPermission = $usersWithPermission->unique('id');
        @endphp
        
        @if($usersWithPermission->count() > 0)
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Users with this Permission</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Users who have this permission through their assigned roles.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($usersWithPermission as $user)
                                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200 transition duration-150 ease-in-out">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-user-circle text-2xl text-green-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-green-900">{{ $user->name }}</p>
                                            <p class="text-xs text-green-700">{{ $user->email }}</p>
                                            <p class="text-xs text-green-600 dark:text-green-400">via {{ $user->role->display_name }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        @if($user->is_active)
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                                Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 transition duration-150 ease-in-out">
                                                Inactive
                                            </span>
                                        @endif
                                        <a href="{{ route('users.show', $user) }}" class="text-green-600 dark:text-green-400 hover:text-green-900" title="View User">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endif

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Assigned Roles</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $permission->roles->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="{{ route('roles.index') }}" class="font-medium text-primary-700 hover:text-primary-900">
                        Manage Roles <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Users</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $usersWithPermission->count() ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="{{ route('users.index') }}" class="font-medium text-primary-700 hover:text-primary-900">
                        Manage Users <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-edit text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Permission Type</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                @if(in_array($permission->name, ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard']))
                                    System
                                @else
                                    Custom
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="{{ route('permissions.edit', $permission) }}" class="font-medium text-primary-700 hover:text-primary-900">
                        Edit Permission <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    @php
        $systemPermissions = ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard', 'manage_navigation', 'view_navigation'];
    @endphp
    @if(!in_array($permission->name, $systemPermissions) && $permission->roles->count() == 0)
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border-l-4 border-red-400 transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                <p class="mt-1 max-w-2xl text-sm text-red-600 dark:text-red-400">Irreversible and destructive actions.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-red-900">Delete this permission</h4>
                        <p class="text-sm text-red-600 dark:text-red-400">Once you delete a permission, there is no going back. Please be certain.</p>
                    </div>
                    <form method="POST" action="{{ route('permissions.destroy', $permission) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this permission? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Permission
                        </button>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
