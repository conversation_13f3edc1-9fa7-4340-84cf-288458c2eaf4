@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $user->name }}</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500">User profile and permissions</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('users.edit', $user) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <i class="fas fa-edit mr-2"></i>
                Edit User
            </a>
            <a href="{{ route('users.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <!-- User Information -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">User Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Personal details and account information.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <dl>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Full Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $user->name }}</dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Email Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $user->email }}</dd>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                        @if($user->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                <i class="fas fa-check-circle mr-1"></i>
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 transition duration-150 ease-in-out">
                                <i class="fas fa-times-circle mr-1"></i>
                                Inactive
                            </span>
                        @endif
                    </dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Role</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                        @if($user->role)
                            <div class="flex items-center">
                                <i class="fas fa-user-shield text-blue-500 mr-2"></i>
                                <span class="font-medium">{{ $user->role->display_name }}</span>
                                <span class="ml-2 text-gray-500 dark:text-gray-400 dark:text-gray-500">({{ $user->role->name }})</span>
                            </div>
                        @else
                            <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500">No role assigned</span>
                        @endif
                    </dd>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Member Since</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $user->created_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Updated</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $user->updated_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- User Permissions -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Permissions</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">Permissions granted through the user's role.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            @if($user->role && $user->role->permissions->count() > 0)
                <div class="px-4 py-5 sm:px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($user->role->permissions as $permission)
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200 transition duration-150 ease-in-out">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-key text-purple-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-purple-900">{{ $permission->display_name }}</p>
                                    <p class="text-xs text-purple-700">{{ $permission->name }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="px-4 py-5 sm:px-6">
                    <div class="text-center py-8">
                        <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">No permissions assigned to this user.</p>
                        @if(!$user->role)
                            <a href="{{ route('users.edit', $user) }}" class="mt-2 inline-flex items-center text-sm text-primary-600 hover:text-primary-500">
                                <i class="fas fa-user-shield mr-1"></i>
                                Assign Role
                            </a>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-edit text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Account Status</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->is_active ? 'Active' : 'Inactive' }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <form method="POST" action="{{ route('users.toggle-status', $user) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="font-medium text-primary-700 hover:text-primary-900">
                            {{ $user->is_active ? 'Deactivate' : 'Activate' }} User <span aria-hidden="true">&rarr;</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Role</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->role ? $user->role->display_name : 'None' }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    @if($user->role)
                        <a href="{{ route('roles.show', $user->role) }}" class="font-medium text-primary-700 hover:text-primary-900">
                            View Role Details <span aria-hidden="true">&rarr;</span>
                        </a>
                    @else
                        <a href="{{ route('users.edit', $user) }}" class="font-medium text-primary-700 hover:text-primary-900">
                            Assign Role <span aria-hidden="true">&rarr;</span>
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-key text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Permissions</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->role ? $user->role->permissions->count() : 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="{{ route('users.edit', $user) }}" class="font-medium text-primary-700 hover:text-primary-900">
                        Edit User <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    @if(auth()->id() !== $user->id)
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border-l-4 border-red-400 transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                <p class="mt-1 max-w-2xl text-sm text-red-600 dark:text-red-400">Irreversible and destructive actions.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-red-900">Delete this user</h4>
                        <p class="text-sm text-red-600 dark:text-red-400">Once you delete a user, there is no going back. Please be certain.</p>
                    </div>
                    <form method="POST" action="{{ route('users.destroy', $user) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                            <i class="fas fa-trash mr-2"></i>
                            Delete User
                        </button>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
