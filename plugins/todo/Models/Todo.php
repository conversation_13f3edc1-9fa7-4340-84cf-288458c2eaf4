<?php

namespace Plugins\Todo\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class Todo extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'tags',
        'is_completed',
        'priority',
        'due_date',
        'sort_order',
        'user_id',
        'completed_at'
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'sort_order' => 'integer',
        'tags' => 'array'
    ];

    /**
     * Get the user that owns the todo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only incomplete todos
     */
    public function scopeIncomplete($query)
    {
        return $query->where('is_completed', false);
    }

    /**
     * Scope to get only completed todos
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Scope to filter by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get the next sort order for a user
     */
    public static function getNextSortOrder($userId): int
    {
        return self::where('user_id', $userId)->max('sort_order') + 1;
    }

    /**
     * Mark todo as completed
     */
    public function markCompleted(): void
    {
        $this->update([
            'is_completed' => true,
            'completed_at' => now()
        ]);
    }

    /**
     * Mark todo as incomplete
     */
    public function markIncomplete(): void
    {
        $this->update([
            'is_completed' => false,
            'completed_at' => null
        ]);
    }

    /**
     * Get priority color class
     */
    public function getPriorityColorClass(): string
    {
        return match($this->priority) {
            'high' => 'text-red-600 dark:text-red-400',
            'medium' => 'text-yellow-600 dark:text-yellow-400',
            'low' => 'text-green-600 dark:text-green-400',
            default => 'text-gray-600 dark:text-gray-400'
        };
    }

    /**
     * Get priority badge class
     */
    public function getPriorityBadgeClass(): string
    {
        return match($this->priority) {
            'high' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'medium' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
            'low' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            default => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
        };
    }

    /**
     * Check if todo is overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !$this->is_completed;
    }

    /**
     * Get tags as array
     */
    public function getTagsArray(): array
    {
        return $this->tags ?? [];
    }

    /**
     * Get tags as comma-separated string
     */
    public function getTagsString(): string
    {
        return implode(', ', $this->getTagsArray());
    }

    /**
     * Add a tag
     */
    public function addTag(string $tag): void
    {
        $tags = $this->getTagsArray();
        $tag = trim($tag);

        if (!empty($tag) && !in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
        }
    }

    /**
     * Remove a tag
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->getTagsArray();
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        $this->tags = array_values($tags);
    }

    /**
     * Set tags from array or comma-separated string
     */
    public function setTags($tags): void
    {
        if (is_string($tags)) {
            $tags = array_map('trim', explode(',', $tags));
        }

        $this->tags = array_filter($tags ?? [], fn($tag) => !empty(trim($tag)));
    }

    /**
     * Check if todo has a specific tag
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->getTagsArray());
    }

    /**
     * Scope to search todos by title, description, or tags
     */
    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('title', 'like', "%{$searchTerm}%")
              ->orWhere('description', 'like', "%{$searchTerm}%")
              ->orWhereJsonContains('tags', $searchTerm);
        });
    }

    /**
     * Scope to filter by tag
     */
    public function scopeWithTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * Scope to filter overdue todos
     */
    public function scopeOverdue($query)
    {
        return $query->where('is_completed', false)
                     ->where('due_date', '<', now());
    }

    /**
     * Get all unique tags for a user
     */
    public static function getAllTagsForUser($userId): array
    {
        return self::where('user_id', $userId)
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values()
            ->toArray();
    }
}
