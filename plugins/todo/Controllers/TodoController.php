<?php

namespace Plugins\Todo\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Todo\Models\Todo;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TodoController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);

            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for each action
     */
    protected function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_todos',
            'show' => 'view_todos',
            'store' => 'manage_todos',
            'update' => 'manage_todos',
            'destroy' => 'manage_todos',
            'toggle' => 'manage_todos',
            'reorder' => 'manage_todos',
            'apiList' => 'view_todos',
            'getTags' => 'view_todos',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display the todo list
     */
    public function index(Request $request): View
    {
        $user = auth()->user();

        $query = Todo::where('user_id', $user->id);

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereJsonContains('tags', $searchTerm);
            });
        }

        // Apply tag filter
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // Apply priority filter
        if ($request->filled('priority') && $request->priority !== 'all') {
            $query->where('priority', $request->priority);
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'completed') {
                $query->where('is_completed', true);
            } elseif ($request->status === 'pending') {
                $query->where('is_completed', false);
            } elseif ($request->status === 'overdue') {
                $query->where('is_completed', false)
                      ->where('due_date', '<', now());
            }
        }

        $todos = $query->ordered()->get();

        // Get all unique tags for filter dropdown
        $allTags = Todo::where('user_id', $user->id)
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        $stats = [
            'total' => $todos->count(),
            'completed' => $todos->where('is_completed', true)->count(),
            'pending' => $todos->where('is_completed', false)->count(),
            'overdue' => $todos->filter(fn($todo) => $todo->isOverdue())->count(),
            'high_priority' => $todos->where('priority', 'high')->count(),
        ];

        return view('plugins.todo::index', compact('todos', 'stats', 'allTags'));
    }

    /**
     * Store a new todo
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:10000', // Increased limit for rich text content
            'priority' => 'required|in:low,medium,high',
            'due_date' => 'nullable|date|after:today',
            'tags' => 'nullable|string|max:1000',
        ]);

        try {
            $user = auth()->user();
            
            $todo = Todo::create([
                'title' => $request->title,
                'description' => $request->description,
                'priority' => $request->priority,
                'due_date' => $request->due_date,
                'user_id' => $user->id,
                'sort_order' => Todo::getNextSortOrder($user->id),
                'is_completed' => false,
            ]);

            // Handle tags
            if ($request->has('tags')) {
                $todo->setTags($request->tags);
                $todo->save();
            }

            Log::info("Todo '{$todo->title}' created", [
                'user' => $user->email,
                'todo_id' => $todo->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Todo created successfully!',
                'todo' => $todo->load('user')
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create todo: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create todo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific todo
     */
    public function show(Todo $todo): JsonResponse
    {
        // Ensure user can only see their own todos
        if ($todo->user_id !== auth()->id()) {
            abort(403, 'Access denied.');
        }

        return response()->json([
            'success' => true,
            'todo' => $todo->load('user')
        ]);
    }

    /**
     * Update a todo
     */
    public function update(Request $request, Todo $todo): JsonResponse
    {
        // Ensure user can only update their own todos
        if ($todo->user_id !== auth()->id()) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:10000', // Increased limit for rich text content
            'priority' => 'required|in:low,medium,high',
            'due_date' => 'nullable|date',
            'tags' => 'nullable|string|max:1000',
        ]);

        try {
            $todo->update([
                'title' => $request->title,
                'description' => $request->description,
                'priority' => $request->priority,
                'due_date' => $request->due_date,
            ]);

            // Handle tags
            if ($request->has('tags')) {
                $todo->setTags($request->tags);
                $todo->save();
            }

            Log::info("Todo '{$todo->title}' updated", [
                'user' => auth()->user()->email,
                'todo_id' => $todo->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Todo updated successfully!',
                'todo' => $todo->fresh()->load('user')
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update todo: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update todo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a todo
     */
    public function destroy(Todo $todo): JsonResponse
    {
        // Ensure user can only delete their own todos
        if ($todo->user_id !== auth()->id()) {
            abort(403, 'Access denied.');
        }

        try {
            $title = $todo->title;
            $todo->delete();

            Log::info("Todo '{$title}' deleted", [
                'user' => auth()->user()->email,
                'todo_id' => $todo->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Todo deleted successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to delete todo: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete todo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle todo completion status
     */
    public function toggle(Todo $todo): JsonResponse
    {
        // Ensure user can only toggle their own todos
        if ($todo->user_id !== auth()->id()) {
            abort(403, 'Access denied.');
        }

        try {
            if ($todo->is_completed) {
                $todo->markIncomplete();
                $message = 'Todo marked as incomplete';
            } else {
                $todo->markCompleted();
                $message = 'Todo marked as completed';
            }

            Log::info("Todo '{$todo->title}' toggled", [
                'user' => auth()->user()->email,
                'todo_id' => $todo->id,
                'completed' => $todo->is_completed,
            ]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'todo' => $todo->fresh()->load('user')
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to toggle todo: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle todo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder todos
     */
    public function reorder(Request $request): JsonResponse
    {
        $request->validate([
            'todo_ids' => 'required|array',
            'todo_ids.*' => 'exists:todos,id',
        ]);

        try {
            $user = auth()->user();
            
            DB::transaction(function () use ($request, $user) {
                foreach ($request->todo_ids as $index => $todoId) {
                    Todo::where('id', $todoId)
                        ->where('user_id', $user->id) // Ensure user can only reorder their own todos
                        ->update(['sort_order' => $index + 1]);
                }
            });

            Log::info("Todos reordered", [
                'user' => $user->email,
                'todo_count' => count($request->todo_ids),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Todos reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to reorder todos: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder todos: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get todos as JSON for AJAX requests
     */
    public function apiList(Request $request): JsonResponse
    {
        $user = auth()->user();

        $query = Todo::where('user_id', $user->id);

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereJsonContains('tags', $searchTerm);
            });
        }

        // Apply tag filter
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // Filter by completion status if requested
        if ($request->has('show_completed')) {
            if (!$request->boolean('show_completed')) {
                $query->incomplete();
            }
        }

        // Filter by priority if requested
        if ($request->has('priority') && $request->priority !== 'all') {
            $query->byPriority($request->priority);
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'completed') {
                $query->where('is_completed', true);
            } elseif ($request->status === 'pending') {
                $query->where('is_completed', false);
            } elseif ($request->status === 'overdue') {
                $query->where('is_completed', false)
                      ->where('due_date', '<', now());
            }
        }

        $todos = $query->ordered()->get();

        return response()->json([
            'success' => true,
            'todos' => $todos->load('user')
        ]);
    }

    /**
     * Get all available tags for the current user
     */
    public function getTags(): JsonResponse
    {
        $user = auth()->user();

        $tags = Todo::where('user_id', $user->id)
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        return response()->json([
            'success' => true,
            'tags' => $tags
        ]);
    }
}
