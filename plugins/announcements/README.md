# Announcements Plugin

A comprehensive announcement management system for the Laravel application that provides user acknowledgment tracking, notification features, and role-based targeting.

## Features

- **Announcement Management**: Create, read, update, and delete announcements with rich metadata
- **User Acknowledgment System**: Require users to explicitly acknowledge reading announcements
- **Read Status Tracking**: Track which users have read which announcements with timestamps
- **Mandatory Reading**: Prevent users from dismissing announcements until acknowledged
- **Streamlined Workflow**: Acknowledging an announcement automatically marks it as read (single action)
- **Priority System**: Set announcement priorities (low, normal, high, urgent)
- **Type Classification**: Categorize announcements (info, warning, success, error)
- **Role-Based Targeting**: Target specific user roles or all users
- **Publishing Controls**: Schedule announcements with publish and expiry dates
- **Real-time Notifications**: Modal popups for unread announcements
- **Navigation Integration**: Unread count badges in navigation
- **Permission-Based Access**: Granular permissions for different announcement operations
- **Privacy Controls**: Read statistics only visible to announcement creators
- **Administrative Filters**: Filter announcements by acknowledgment status (admin only)
- **Pending Acknowledgment Tracking**: Track and display users who still need to acknowledge

## Dependencies

- **users** plugin (required) - Provides authentication and role management

## Permissions

The plugin defines two specific permissions:

1. **manage_announcements** - Create, edit, delete, and manage all announcements
2. **view_announcements** - View and read announcements, mark as read/acknowledged

## Installation

1. The plugin is installed in the `plugins/announcements` directory
2. Enable the plugin through the Plugin Manager interface
3. Run migrations to create the required database tables
4. Run the permission seeder to add announcement permissions to the database

### Manual Installation Steps

```bash
# Run migrations
php artisan migrate --path=plugins/announcements/Migrations

# Run seeders
php artisan db:seed --class=Plugins\\Announcements\\Seeds\\AnnouncementPermissionSeeder
php artisan db:seed --class=Plugins\\Announcements\\Seeds\\AnnouncementsSampleDataSeeder
```

## Database Structure

### Announcements Table
- `id` - Primary key
- `title` - Announcement title
- `content` - Announcement content (supports HTML)
- `priority` - Priority level (low, normal, high, urgent)
- `type` - Type classification (info, warning, success, error)
- `is_active` - Whether announcement is active
- `requires_acknowledgment` - Whether users must acknowledge
- `published_at` - When announcement becomes visible
- `expires_at` - When announcement expires (optional)
- `target_roles` - JSON array of target role names (optional)
- `metadata` - Additional JSON metadata
- `created_by` - User who created the announcement
- `created_at`, `updated_at`, `deleted_at` - Standard timestamps

### Announcement Reads Table
- `id` - Primary key
- `announcement_id` - Foreign key to announcements
- `user_id` - Foreign key to users
- `read_at` - When user read the announcement
- `acknowledged_at` - When user acknowledged the announcement
- `ip_address` - IP address for audit trail
- `user_agent` - User agent for audit trail
- `metadata` - Additional JSON metadata
- `created_at`, `updated_at` - Standard timestamps

## Usage

### Admin Interface

Administrators with `manage_announcements` permission can:

- **Create Announcements**: Set title, content, priority, type, and targeting
- **Edit Announcements**: Modify existing announcements
- **View Statistics**: See read and acknowledgment rates
- **Manage Publishing**: Control when announcements are visible
- **Target Roles**: Specify which user roles should see announcements

### User Interface

Users with `view_announcements` permission can:

- **View Announcements**: See all announcements targeted to their role
- **Mark as Read**: Mark announcements as read (for announcements not requiring acknowledgment)
- **Acknowledge**: Confirm understanding of important announcements (automatically marks as read)
- **Modal Notifications**: Receive popup notifications for unread announcements

### Streamlined User Experience

The plugin provides an improved user experience for announcement acknowledgments:

- **For announcements requiring acknowledgment**: Users see only one button "I Have Read and Understood" which performs both actions (mark as read + acknowledge)
- **For announcements not requiring acknowledgment**: Users see a simple "Mark as Read" button
- **Clear messaging**: Users are informed that acknowledging will mark the announcement as both read and acknowledged
- **Single action workflow**: Eliminates the need for users to perform two separate actions

### Privacy and Security Features

The plugin includes enhanced privacy and security controls:

- **Read Statistics Privacy**: Read counts, acknowledgment statistics, and user activity are only visible to the announcement creator
- **Administrative Access Control**: Data management features (seed/clear data) are restricted to users with `manage_announcements` permission
- **Permission-Based Filtering**: Advanced filters like acknowledgment status are only available to administrators

### Administrative Features

Administrators have access to powerful management tools:

- **Acknowledgment Status Filter**: Filter announcements by acknowledgment status (pending acknowledgments, fully acknowledged)
- **Pending Acknowledgment Tracking**: See how many users still need to acknowledge each announcement
- **Creator-Only Statistics**: Read and acknowledgment statistics are private to announcement creators
- **Data Management Controls**: Secure access to sample data seeding and data clearing operations

### API Endpoints

- `GET /announcements/api/unread-count` - Get unread announcement count
- `GET /announcements/api/unread` - Get unread announcements
- `POST /announcements/{id}/mark-read` - Mark announcement as read
- `POST /announcements/{id}/acknowledge` - Acknowledge announcement

## Integration

### Navigation Integration

The plugin automatically integrates with the main application sidebar navigation:

- **Main Menu Item**: "Announcements" appears in the sidebar with other plugin menu items
- **Sub-menu Items**:
  - "All Announcements" (for viewing/managing announcements)
  - "Create Announcement" (for admins with manage permissions)
- **Unread Count Badge**: Shows unread announcement count next to the menu item
- **Active State Highlighting**: Proper highlighting when on announcement-related pages
- **Permission-Based Display**: Menu items only appear for users with appropriate permissions

The navigation is automatically included when the plugin is enabled and follows the existing navigation patterns.

### Modal System

Unread announcements requiring acknowledgment are automatically displayed in a modal when users log in.

### Data Management

The plugin includes data management features accessible through the Plugin Manager interface at `/plugins/announcements`:

- **Clear All Data**: Remove all announcement data (database-agnostic)
- **Database Compatibility**: Works with SQLite, MySQL, PostgreSQL, and SQL Server
- **Safe Deletion**: Proper foreign key constraint handling during data clearing
- **Data Statistics**: View announcement and read statistics
- **Centralized Management**: All administrative functions located in Plugin Manager

## Controller Methods

### AnnouncementController

- `index()` - List all announcements with filtering
- `create()` - Show announcement creation form
- `store()` - Create a new announcement
- `show()` - Display announcement details and statistics
- `edit()` - Show announcement edit form
- `update()` - Update announcement information
- `destroy()` - Delete an announcement
- `markAsRead()` - Mark announcement as read by current user
- `acknowledge()` - Acknowledge announcement by current user
- `getUnreadCount()` - Get unread count for current user
- `getUnreadAnnouncements()` - Get unread announcements for current user

## Model Relationships

### Announcement Model

- `creator()` - Belongs to User (who created the announcement)
- `reads()` - Has many AnnouncementRead records
- `readByUsers()` - Many-to-many relationship with users who read
- `acknowledgedByUsers()` - Many-to-many relationship with users who acknowledged

### AnnouncementRead Model

- `announcement()` - Belongs to Announcement
- `user()` - Belongs to User

## Views

- `index.blade.php` - Announcement listing with search and filters
- `create.blade.php` - Announcement creation form
- `edit.blade.php` - Announcement editing form
- `show.blade.php` - Announcement details with statistics
- `user-announcements.blade.php` - User-facing announcement view
- `components/announcement-modal.blade.php` - Modal for unread announcements

## Navigation Integration

The plugin integrates directly with the main application layout (`resources/views/layouts/app.blade.php`) and includes:

- Sidebar navigation menu with sub-items
- Unread count badges in navigation
- JavaScript functions for menu toggling
- Modal integration for unread announcements
- Real-time count updates

## Security

- All routes are protected by authentication middleware
- Permission checks are enforced at the controller level
- Role-based targeting ensures users only see relevant announcements
- Audit trail tracking with IP addresses and user agents
- Soft deletes are used for announcements to maintain data integrity

## Future Enhancements

- Email notifications for urgent announcements
- Rich text editor for announcement content
- File attachments for announcements
- Announcement templates
- Bulk operations for managing multiple announcements
- Advanced analytics and reporting
- Integration with external notification services
