<?php

namespace Plugins\Announcements\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class AnnouncementRead extends Model
{
    use HasFactory;

    protected $fillable = [
        'announcement_id',
        'user_id',
        'read_at',
        'acknowledged_at',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    protected $casts = [
        'read_at' => 'datetime',
        'acknowledged_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the announcement this read belongs to
     */
    public function announcement()
    {
        return $this->belongsTo(Announcement::class);
    }

    /**
     * Get the user who read the announcement
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only read records
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope to get only acknowledged records
     */
    public function scopeAcknowledged($query)
    {
        return $query->whereNotNull('acknowledged_at');
    }

    /**
     * Scope to get unread records
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope to get unacknowledged records
     */
    public function scopeUnacknowledged($query)
    {
        return $query->whereNull('acknowledged_at');
    }

    /**
     * Check if this record is read
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if this record is acknowledged
     */
    public function isAcknowledged(): bool
    {
        return !is_null($this->acknowledged_at);
    }

    /**
     * Mark as read
     */
    public function markAsRead(): self
    {
        if (!$this->read_at) {
            $this->update(['read_at' => now()]);
        }
        
        return $this;
    }

    /**
     * Mark as acknowledged
     */
    public function markAsAcknowledged(): self
    {
        $this->update([
            'read_at' => $this->read_at ?? now(),
            'acknowledged_at' => now(),
        ]);
        
        return $this;
    }

    /**
     * Get time since read
     */
    public function getTimeSinceRead(): ?string
    {
        if (!$this->read_at) {
            return null;
        }

        return $this->read_at->diffForHumans();
    }

    /**
     * Get time since acknowledged
     */
    public function getTimeSinceAcknowledged(): ?string
    {
        if (!$this->acknowledged_at) {
            return null;
        }

        return $this->acknowledged_at->diffForHumans();
    }
}
