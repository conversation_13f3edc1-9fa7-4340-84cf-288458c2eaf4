<?php

namespace Plugins\Announcements\Seeds;

use Illuminate\Database\Seeder;

class AnnouncementsPluginSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Announcements Plugin...');

        // Run permission seeder first
        $this->call(AnnouncementPermissionSeeder::class);

        // Then run sample data seeder
        $this->call(AnnouncementsSampleDataSeeder::class);

        $this->command->info('Announcements Plugin seeded successfully!');
    }
}
