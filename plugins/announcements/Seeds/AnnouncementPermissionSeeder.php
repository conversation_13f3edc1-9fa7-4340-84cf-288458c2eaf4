<?php

namespace Plugins\Announcements\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class AnnouncementPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Announcement Permissions...');

        // Define announcement permissions
        $permissions = [
            [
                'name' => 'manage_announcements',
                'display_name' => 'Manage Announcements',
                'description' => 'Create, edit, delete, and manage all announcements',
            ],
            [
                'name' => 'view_announcements',
                'display_name' => 'View Announcements',
                'description' => 'View and read announcements',
            ],
        ];

        // Create permissions
        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'display_name' => $permissionData['display_name'],
                    'description' => $permissionData['description'],
                ]
            );

            $this->command->info("Created/Updated permission: {$permission->name}");
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        $this->command->info('Announcement permissions seeded successfully!');
    }

    /**
     * Assign permissions to default roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Get permissions
        $manageAnnouncementsPermission = Permission::where('name', 'manage_announcements')->first();
        $viewAnnouncementsPermission = Permission::where('name', 'view_announcements')->first();

        if (!$manageAnnouncementsPermission || !$viewAnnouncementsPermission) {
            $this->command->warn('Announcement permissions not found. Skipping role assignment.');
            return;
        }

        // Assign to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->permissions()->syncWithoutDetaching([
                $manageAnnouncementsPermission->id,
                $viewAnnouncementsPermission->id,
            ]);
            $this->command->info("Assigned announcement permissions to admin role");
        }

        // Assign view permission to user role
        $userRole = Role::where('name', 'user')->first();
        if ($userRole) {
            $userRole->permissions()->syncWithoutDetaching([
                $viewAnnouncementsPermission->id,
            ]);
            $this->command->info("Assigned view announcement permission to user role");
        }

        // Assign to manager role if it exists
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerRole->permissions()->syncWithoutDetaching([
                $manageAnnouncementsPermission->id,
                $viewAnnouncementsPermission->id,
            ]);
            $this->command->info("Assigned announcement permissions to manager role");
        }

        // Assign to moderator role if it exists
        $moderatorRole = Role::where('name', 'moderator')->first();
        if ($moderatorRole) {
            $moderatorRole->permissions()->syncWithoutDetaching([
                $manageAnnouncementsPermission->id,
                $viewAnnouncementsPermission->id,
            ]);
            $this->command->info("Assigned announcement permissions to moderator role");
        }
    }
}
