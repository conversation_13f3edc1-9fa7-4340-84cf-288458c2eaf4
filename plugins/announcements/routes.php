<?php

use Illuminate\Support\Facades\Route;
use Plugins\Announcements\Controllers\AnnouncementController;

// Admin routes for managing announcements
Route::prefix('announcements')->name('announcements.')->group(function () {
    Route::get('/', [AnnouncementController::class, 'index'])->name('index');
    Route::get('/create', [AnnouncementController::class, 'create'])->name('create');
    Route::post('/', [AnnouncementController::class, 'store'])->name('store');
    Route::get('/{announcement}', [AnnouncementController::class, 'show'])->name('show');
    Route::get('/{announcement}/edit', [AnnouncementController::class, 'edit'])->name('edit');
    Route::put('/{announcement}', [AnnouncementController::class, 'update'])->name('update');
    Route::delete('/{announcement}', [AnnouncementController::class, 'destroy'])->name('destroy');

    // User interaction routes
    Route::post('/{announcement}/acknowledge', [AnnouncementController::class, 'acknowledge'])->name('acknowledge');
    Route::post('/{announcement}/mark-read', [AnnouncementController::class, 'markAsRead'])->name('mark-read');

    // API routes for notifications
    Route::get('/api/unread-count', [AnnouncementController::class, 'getUnreadCount'])->name('api.unread-count');
    Route::get('/api/unread', [AnnouncementController::class, 'getUnreadAnnouncements'])->name('api.unread');


});
