<?php

namespace Plugins\Announcements\Tests;

use Tests\TestCase;
use Plugins\Announcements\Models\Announcement;
use Plugins\Announcements\Models\AnnouncementRead;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AnnouncementPluginTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles and permissions
        $adminRole = Role::create(['name' => 'admin', 'display_name' => 'Administrator']);
        $userRole = Role::create(['name' => 'user', 'display_name' => 'User']);
        
        $managePermission = Permission::create([
            'name' => 'manage_announcements',
            'display_name' => 'Manage Announcements'
        ]);
        
        $viewPermission = Permission::create([
            'name' => 'view_announcements',
            'display_name' => 'View Announcements'
        ]);
        
        $adminRole->permissions()->attach([$managePermission->id, $viewPermission->id]);
        $userRole->permissions()->attach([$viewPermission->id]);
        
        // Create users
        $this->admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
        ]);
        
        $this->user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $userRole->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function admin_can_create_announcement()
    {
        $this->actingAs($this->admin);
        
        $announcementData = [
            'title' => 'Test Announcement',
            'content' => 'This is a test announcement content.',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now()->format('Y-m-d\TH:i'),
        ];
        
        $response = $this->post(route('announcements.store'), $announcementData);
        
        $response->assertRedirect(route('announcements.index'));
        $this->assertDatabaseHas('announcements', [
            'title' => 'Test Announcement',
            'content' => 'This is a test announcement content.',
            'created_by' => $this->admin->id,
        ]);
    }

    /** @test */
    public function user_cannot_create_announcement()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('announcements.create'));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function user_can_view_announcements()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('announcements.index'));
        
        $response->assertStatus(200);
    }

    /** @test */
    public function announcement_can_be_marked_as_read()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => false,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);
        
        $this->actingAs($this->user);
        
        $response = $this->post(route('announcements.mark-read', $announcement));
        
        $response->assertJson(['success' => true]);
        $this->assertDatabaseHas('announcement_reads', [
            'announcement_id' => $announcement->id,
            'user_id' => $this->user->id,
        ]);
    }

    /** @test */
    public function announcement_can_be_acknowledged()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        $this->actingAs($this->user);

        $response = $this->post(route('announcements.acknowledge', $announcement));

        $response->assertJson(['success' => true]);
        $this->assertDatabaseHas('announcement_reads', [
            'announcement_id' => $announcement->id,
            'user_id' => $this->user->id,
        ]);

        $read = AnnouncementRead::where('announcement_id', $announcement->id)
                               ->where('user_id', $this->user->id)
                               ->first();

        // Verify both read_at and acknowledged_at are set when acknowledging
        $this->assertNotNull($read->read_at, 'Announcement should be marked as read when acknowledged');
        $this->assertNotNull($read->acknowledged_at, 'Announcement should have acknowledgment timestamp');
        $this->assertTrue($read->read_at <= $read->acknowledged_at, 'Read timestamp should be before or equal to acknowledgment timestamp');
    }

    /** @test */
    public function acknowledging_announcement_automatically_marks_as_read()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        $this->actingAs($this->user);

        // Acknowledge the announcement (should automatically mark as read)
        $response = $this->post(route('announcements.acknowledge', $announcement));

        $response->assertJson(['success' => true]);

        $read = AnnouncementRead::where('announcement_id', $announcement->id)
                               ->where('user_id', $this->user->id)
                               ->first();

        // Verify the announcement is both read and acknowledged
        $this->assertTrue($announcement->isReadByUser($this->user), 'Announcement should be marked as read');
        $this->assertTrue($announcement->isAcknowledgedByUser($this->user), 'Announcement should be acknowledged');
        $this->assertNotNull($read->read_at, 'Read timestamp should be set');
        $this->assertNotNull($read->acknowledged_at, 'Acknowledgment timestamp should be set');
    }

    /** @test */
    public function unread_count_api_works()
    {
        // Create an announcement
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => false,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);
        
        $this->actingAs($this->user);
        
        $response = $this->get(route('announcements.api.unread-count'));
        
        $response->assertJson(['unread_count' => 1]);
    }

    /** @test */
    public function role_targeting_works()
    {
        // Create announcement targeting only admins
        $announcement = Announcement::create([
            'title' => 'Admin Only Announcement',
            'content' => 'This is for admins only',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => false,
            'published_at' => now(),
            'target_roles' => ['admin'],
            'created_by' => $this->admin->id,
        ]);
        
        // Admin should see it
        $this->assertTrue($announcement->isVisibleToUser($this->admin));
        
        // Regular user should not see it
        $this->assertFalse($announcement->isVisibleToUser($this->user));
    }

    /** @test */
    public function announcement_statistics_work()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);
        
        // Mark as read by user
        $announcement->markAsReadByUser($this->user);
        
        $stats = $announcement->getReadStats();
        
        $this->assertEquals(2, $stats['total_users']); // admin + user
        $this->assertEquals(1, $stats['read_count']);
        $this->assertEquals(0, $stats['acknowledged_count']);
        $this->assertEquals(50, $stats['read_percentage']);
    }

    /** @test */
    public function read_statistics_include_pending_acknowledgments()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        // Mark as read by user but not acknowledged
        $announcement->markAsReadByUser($this->user);

        $stats = $announcement->getReadStats();

        $this->assertEquals(2, $stats['total_users']); // admin + user
        $this->assertEquals(1, $stats['read_count']);
        $this->assertEquals(0, $stats['acknowledged_count']);
        $this->assertEquals(2, $stats['pending_acknowledgments']); // Both users need to acknowledge
    }

    /** @test */
    public function pending_acknowledgment_count_method_works()
    {
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        // Initially, all users need to acknowledge
        $this->assertEquals(2, $announcement->getPendingAcknowledgmentCount());

        // After one user acknowledges
        $announcement->acknowledgeByUser($this->user);
        $this->assertEquals(1, $announcement->getPendingAcknowledgmentCount());

        // After all users acknowledge
        $announcement->acknowledgeByUser($this->admin);
        $this->assertEquals(0, $announcement->getPendingAcknowledgmentCount());
    }

    /** @test */
    public function acknowledgment_filter_only_accessible_to_managers()
    {
        $this->actingAs($this->user);

        // Regular user should not see acknowledgment filter results
        $response = $this->get(route('announcements.index', ['acknowledgment' => 'unacknowledged']));

        $response->assertStatus(200);
        // The filter should be ignored for non-managers
    }

    /** @test */
    public function admin_can_filter_unacknowledged_announcements()
    {
        // Create announcement requiring acknowledgment
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('announcements.index', ['acknowledgment' => 'unacknowledged']));

        $response->assertStatus(200);
        $response->assertSee($announcement->title);
    }

    /** @test */
    public function admin_can_clear_all_announcement_data_via_plugin_manager()
    {
        // Create some test data
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        $announcement->acknowledgeByUser($this->user);

        // Verify data exists
        $this->assertDatabaseHas('announcements', ['id' => $announcement->id]);
        $this->assertDatabaseHas('announcement_reads', ['announcement_id' => $announcement->id]);

        $this->actingAs($this->admin);

        // Clear all data via plugin manager
        $response = $this->post(route('plugins.clear-all-data', 'announcements'));

        $response->assertJson(['success' => true]);

        // Verify data is cleared
        $this->assertDatabaseMissing('announcements', ['id' => $announcement->id]);
        $this->assertDatabaseMissing('announcement_reads', ['announcement_id' => $announcement->id]);
    }

    /** @test */
    public function regular_user_cannot_clear_announcement_data_via_plugin_manager()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('plugins.clear-all-data', 'announcements'));

        $response->assertStatus(403);
    }

    /** @test */
    public function plugin_management_interface_shows_announcements_data()
    {
        // Create some test data
        $announcement = Announcement::create([
            'title' => 'Test Announcement',
            'content' => 'Test content',
            'priority' => 'normal',
            'type' => 'info',
            'is_active' => true,
            'requires_acknowledgment' => true,
            'published_at' => now(),
            'created_by' => $this->admin->id,
        ]);

        $announcement->acknowledgeByUser($this->user);

        $this->actingAs($this->admin);

        // Visit plugin management page for announcements
        $response = $this->get(route('plugins.show', 'announcements'));

        $response->assertStatus(200);
        $response->assertSee('Clear All Data'); // Should show clear button when data exists
        $response->assertSee('announcements'); // Should show data statistics
        $response->assertSee('announcement_reads');
    }

}
