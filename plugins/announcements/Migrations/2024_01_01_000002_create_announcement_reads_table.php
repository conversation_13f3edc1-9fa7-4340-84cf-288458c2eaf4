<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcement_reads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('announcement_id')->constrained('announcements')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->timestamp('read_at')->nullable();
            $table->timestamp('acknowledged_at')->nullable();
            $table->string('ip_address')->nullable(); // Track IP for audit purposes
            $table->string('user_agent')->nullable(); // Track user agent for audit purposes
            $table->json('metadata')->nullable(); // Additional tracking data
            $table->timestamps();

            // Unique constraint to prevent duplicate reads
            $table->unique(['announcement_id', 'user_id']);
            
            // Indexes for performance
            $table->index(['announcement_id', 'read_at']);
            $table->index(['user_id', 'read_at']);
            $table->index(['announcement_id', 'acknowledged_at']);
            $table->index(['user_id', 'acknowledged_at']);
            $table->index(['read_at']);
            $table->index(['acknowledged_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcement_reads');
    }
};
