<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->enum('type', ['info', 'warning', 'success', 'error'])->default('info');
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_acknowledgment')->default(true);
            $table->datetime('published_at')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->json('target_roles')->nullable(); // Array of role names that should see this announcement
            $table->json('metadata')->nullable(); // Additional data like styling, icons, etc.
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['is_active', 'published_at']);
            $table->index(['priority', 'created_at']);
            $table->index(['type', 'is_active']);
            $table->index(['created_by']);
            $table->index(['published_at', 'expires_at']);
            $table->index(['requires_acknowledgment']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcements');
    }
};
