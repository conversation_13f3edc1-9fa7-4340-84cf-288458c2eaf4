<!-- Announcement Modal -->
<div id="announcement-modal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50 hidden transition duration-150 ease-in-out">
    <div class="relative top-20 mx-auto p-5 border border-gray-200 dark:border-gray-700 w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800 transition duration-150 ease-in-out">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-bullhorn text-blue-500 dark:text-blue-400 mr-2"></i>
                    Important Announcements
                </h3>
                <button onclick="closeAnnouncementModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 dark:text-gray-400 dark:hover:text-gray-300 transition duration-150 ease-in-out">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-4 max-h-96 overflow-y-auto">
                <div id="announcement-list">
                    <!-- Announcements will be loaded here -->
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700 mt-4">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <span id="announcement-count">0</span> unread announcement(s)
                </div>
                <div class="flex space-x-3">
                    <button onclick="markAllAsRead()"
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                        <i class="fas fa-eye mr-2"></i>Mark All as Read
                    </button>
                    <button onclick="closeAnnouncementModal()"
                            class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:hover:bg-gray-7000 dark:bg-gray-600 dark:hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Modal-specific functions that integrate with the main layout script

function showAnnouncementModal() {
    const modal = document.getElementById('announcement-modal');
    const announcementList = document.getElementById('announcement-list');
    
    if (!modal || !announcementList) return;
    
    // Clear existing content
    announcementList.innerHTML = '';
    
    // Add announcements to modal
    unreadAnnouncements.forEach(announcement => {
        const announcementElement = createAnnouncementElement(announcement);
        announcementList.appendChild(announcementElement);
    });
    
    // Show modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function createAnnouncementElement(announcement) {
    const div = document.createElement('div');
    div.className = `mb-4 p-4 border-l-4 border-${getTypeColor(announcement.type)}-500 bg-${getTypeColor(announcement.type)}-50 dark:bg-${getTypeColor(announcement.type)}-900/20 rounded-r-lg`;
    div.innerHTML = `
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-2">
                    <h4 class="text-md font-semibold text-gray-900 dark:text-white">${escapeHtml(announcement.title)}</h4>
                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeClass(announcement.priority)}">
                        ${announcement.priority.charAt(0).toUpperCase() + announcement.priority.slice(1)}
                    </span>
                </div>
                <div class="text-sm text-gray-700 dark:text-gray-300 mb-3">
                    ${escapeHtml(announcement.content).replace(/\n/g, '<br>')}
                </div>
                <div class="text-xs text-gray-500">
                    Published ${formatDate(announcement.created_at)} by ${escapeHtml(announcement.creator)}
                </div>
            </div>
        </div>
        <div class="mt-3 flex flex-col space-y-2">
            ${announcement.requires_acknowledgment ? `
                <button onclick="acknowledgeAnnouncementInModal(${announcement.id})"
                        class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out">
                    <i class="fas fa-check mr-1"></i>I Have Read and Understood
                </button>
                <p class="text-xs text-gray-500">This will mark the announcement as read and acknowledged</p>
            ` : `
                <button onclick="markAnnouncementAsRead(${announcement.id})"
                        class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out">
                    <i class="fas fa-eye mr-1"></i>Mark as Read
                </button>
            `}
        </div>
    `;
    return div;
}

function closeAnnouncementModal() {
    const modal = document.getElementById('announcement-modal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

function markAnnouncementAsRead(announcementId) {
    fetch(`/announcements/${announcementId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove from unread list
            if (typeof unreadAnnouncements !== 'undefined') {
                unreadAnnouncements = unreadAnnouncements.filter(a => a.id !== announcementId);
            }

            // Update counts using global function if available
            if (typeof updateAllUnreadCounts === 'function') {
                updateAllUnreadCounts();
            }

            // Refresh modal content
            if (typeof unreadAnnouncements !== 'undefined' && unreadAnnouncements.length === 0) {
                closeAnnouncementModal();
            } else {
                showAnnouncementModal();
            }

            showToast('Announcement marked as read', 'success');
        } else {
            showToast('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Error: ' + error.message, 'error');
    });
}

function acknowledgeAnnouncementInModal(announcementId) {
    if (confirm('By clicking "OK", you confirm that you have read and understood this announcement. This will mark it as both read and acknowledged.')) {
        fetch(`/announcements/${announcementId}/acknowledge`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from unread list
                if (typeof unreadAnnouncements !== 'undefined') {
                    unreadAnnouncements = unreadAnnouncements.filter(a => a.id !== announcementId);
                }

                // Update counts using global function if available
                if (typeof updateAllUnreadCounts === 'function') {
                    updateAllUnreadCounts();
                }

                // Refresh modal content
                if (typeof unreadAnnouncements !== 'undefined' && unreadAnnouncements.length === 0) {
                    closeAnnouncementModal();
                } else {
                    showAnnouncementModal();
                }

                showToast('Announcement read and acknowledged successfully', 'success');
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('Error: ' + error.message, 'error');
        });
    }
}

function markAllAsRead() {
    if (confirm('Mark all announcements as read?')) {
        const announcementsToMark = typeof unreadAnnouncements !== 'undefined' ? unreadAnnouncements : [];
        const promises = announcementsToMark.map(announcement =>
            fetch(`/announcements/${announcement.id}/mark-read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
        );

        Promise.all(promises)
            .then(() => {
                if (typeof unreadAnnouncements !== 'undefined') {
                    unreadAnnouncements = [];
                }

                // Update counts using global function if available
                if (typeof updateAllUnreadCounts === 'function') {
                    updateAllUnreadCounts();
                }

                closeAnnouncementModal();
                showToast('All announcements marked as read', 'success');
            })
            .catch(error => {
                showToast('Error marking announcements as read', 'error');
            });
    }
}

// Utility functions
function getTypeColor(type) {
    switch(type) {
        case 'error': return 'red';
        case 'warning': return 'yellow';
        case 'success': return 'green';
        case 'info':
        default: return 'blue';
    }
}

function getPriorityBadgeClass(priority) {
    switch(priority) {
        case 'urgent': return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
        case 'high': return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
        case 'normal': return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        case 'low': return 'bg-gray-100 dark:bg-gray-700 text-gray-800';
        default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800';
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-500 dark:bg-green-600' : 'bg-red-500 dark:bg-red-600'}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('announcement-modal');
    if (event.target === modal) {
        closeAnnouncementModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeAnnouncementModal();
    }
});
</script>
