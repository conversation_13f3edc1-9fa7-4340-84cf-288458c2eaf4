@extends('layouts.app')

@section('title', $announcement->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $announcement->title }}</h1>
                <p class="text-gray-600 mt-1">Announcement Details</p>
            </div>
            <div class="flex space-x-3">
                @if(auth()->user()->hasPermission('manage_announcements'))
                    <a href="{{ route('announcements.edit', $announcement) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endif
                <a href="{{ route('announcements.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Announcements
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Announcement Content -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $announcement->getPriorityBadgeClass() }}">
                                    {{ ucfirst($announcement->priority) }}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $announcement->getTypeBadgeClass() }}">
                                    {{ ucfirst($announcement->type) }}
                                </span>
                                @if($announcement->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif
                                @if($announcement->requires_acknowledgment)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Requires Acknowledgment
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <div class="prose max-w-none">
                            {!! nl2br(e($announcement->content)) !!}
                        </div>
                    </div>
                </div>

                <!-- Read Activity -->
                @if($recentReads->count() > 0)
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Recent Read Activity</h3>
                        </div>
                        <div class="px-6 py-4">
                            <div class="space-y-3">
                                @foreach($recentReads as $read)
                                    <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <span class="text-sm font-medium text-gray-700">
                                                        {{ substr($read->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">{{ $read->user->name }}</p>
                                                <p class="text-xs text-gray-500">{{ $read->user->email }}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm text-gray-900">
                                                Read {{ $read->read_at->diffForHumans() }}
                                            </p>
                                            @if($read->acknowledged_at)
                                                <p class="text-xs text-green-600">
                                                    Acknowledged {{ $read->acknowledged_at->diffForHumans() }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Announcement Info -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Announcement Info</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created by</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $announcement->creator->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created at</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $announcement->created_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        @if($announcement->published_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Published at</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $announcement->published_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        @endif
                        @if($announcement->expires_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Expires at</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $announcement->expires_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        @endif
                        @if($announcement->target_roles && count($announcement->target_roles) > 0)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Target roles</dt>
                                <dd class="mt-1">
                                    @foreach($announcement->target_roles as $role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-1 mb-1">
                                            {{ ucfirst($role) }}
                                        </span>
                                    @endforeach
                                </dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Read Statistics (Only visible to announcement creator) -->
                @if(auth()->id() === $announcement->created_by)
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Read Statistics</h3>
                            <p class="text-sm text-gray-500 mt-1">Only visible to the announcement creator</p>
                        </div>
                        <div class="px-6 py-4 space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total users</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900">{{ $readStats['total_users'] }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Users who read</dt>
                                <dd class="mt-1 text-2xl font-semibold text-blue-600">
                                    {{ $readStats['read_count'] }}
                                    <span class="text-sm font-normal text-gray-500">({{ $readStats['read_percentage'] }}%)</span>
                                </dd>
                            </div>
                            @if($announcement->requires_acknowledgment)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Users who acknowledged</dt>
                                    <dd class="mt-1 text-2xl font-semibold text-green-600">
                                        {{ $readStats['acknowledged_count'] }}
                                        <span class="text-sm font-normal text-gray-500">({{ $readStats['acknowledged_percentage'] }}%)</span>
                                    </dd>
                                </div>
                                @if($readStats['pending_acknowledgments'] > 0)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Pending acknowledgments</dt>
                                        <dd class="mt-1 text-2xl font-semibold text-orange-600">
                                            {{ $readStats['pending_acknowledgments'] }}
                                            <span class="text-sm font-normal text-gray-500">users still need to acknowledge</span>
                                        </dd>
                                    </div>
                                @endif
                            @endif

                            <!-- Progress Bars -->
                            <div class="space-y-3">
                                <div>
                                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                                        <span>Read Progress</span>
                                        <span>{{ $readStats['read_percentage'] }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $readStats['read_percentage'] }}%"></div>
                                    </div>
                                </div>
                                @if($announcement->requires_acknowledgment)
                                    <div>
                                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                                            <span>Acknowledgment Progress</span>
                                            <span>{{ $readStats['acknowledged_percentage'] }}%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: {{ $readStats['acknowledged_percentage'] }}%"></div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
