@extends('layouts.app')

@section('title', 'Edit Announcement')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Edit Announcement: {{ $announcement->title }}</h1>
            <div class="flex space-x-3">
                <a href="{{ route('announcements.show', $announcement) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Announcement
                </a>
                <a href="{{ route('announcements.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Announcements
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('announcements.update', $announcement) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Title -->
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="{{ old('title', $announcement->title) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('title') border-red-300 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">A clear and concise title for the announcement</p>
                    </div>

                    <!-- Content -->
                    <div class="md:col-span-2">
                        <label for="content" class="block text-sm font-medium text-gray-700">
                            Content <span class="text-red-500">*</span>
                        </label>
                        <textarea name="content" id="content" rows="6" required
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('content') border-red-300 @enderror">{{ old('content', $announcement->content) }}</textarea>
                        @error('content')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">The main content of the announcement. You can use HTML formatting.</p>
                    </div>

                    <!-- Priority -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700">
                            Priority <span class="text-red-500">*</span>
                        </label>
                        <select name="priority" id="priority" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('priority') border-red-300 @enderror">
                            <option value="">Select Priority</option>
                            <option value="low" {{ old('priority', $announcement->priority) === 'low' ? 'selected' : '' }}>Low</option>
                            <option value="normal" {{ old('priority', $announcement->priority) === 'normal' ? 'selected' : '' }}>Normal</option>
                            <option value="high" {{ old('priority', $announcement->priority) === 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ old('priority', $announcement->priority) === 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                        @error('priority')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Higher priority announcements are displayed more prominently</p>
                    </div>

                    <!-- Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700">
                            Type <span class="text-red-500">*</span>
                        </label>
                        <select name="type" id="type" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-300 @enderror">
                            <option value="">Select Type</option>
                            <option value="info" {{ old('type', $announcement->type) === 'info' ? 'selected' : '' }}>Info</option>
                            <option value="success" {{ old('type', $announcement->type) === 'success' ? 'selected' : '' }}>Success</option>
                            <option value="warning" {{ old('type', $announcement->type) === 'warning' ? 'selected' : '' }}>Warning</option>
                            <option value="error" {{ old('type', $announcement->type) === 'error' ? 'selected' : '' }}>Error</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">The type affects the visual styling of the announcement</p>
                    </div>

                    <!-- Publishing Settings -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Publishing Settings</h3>
                    </div>

                    <!-- Published At -->
                    <div>
                        <label for="published_at" class="block text-sm font-medium text-gray-700">
                            Publish Date & Time
                        </label>
                        <input type="datetime-local" name="published_at" id="published_at" 
                               value="{{ old('published_at', $announcement->published_at?->format('Y-m-d\TH:i')) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('published_at') border-red-300 @enderror">
                        @error('published_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">When the announcement should become visible to users</p>
                    </div>

                    <!-- Expires At -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700">
                            Expiry Date & Time
                        </label>
                        <input type="datetime-local" name="expires_at" id="expires_at" 
                               value="{{ old('expires_at', $announcement->expires_at?->format('Y-m-d\TH:i')) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('expires_at') border-red-300 @enderror">
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Optional: When the announcement should stop being visible (leave empty for no expiry)</p>
                    </div>

                    <!-- Target Roles -->
                    <div class="md:col-span-2">
                        <label for="target_roles" class="block text-sm font-medium text-gray-700">
                            Target Roles
                        </label>
                        <div class="mt-2 space-y-2">
                            @php
                                $roles = \App\Models\Role::all();
                                $selectedRoles = old('target_roles', $announcement->target_roles ?? []);
                            @endphp
                            @foreach($roles as $role)
                                <label class="inline-flex items-center mr-6">
                                    <input type="checkbox" name="target_roles[]" value="{{ $role->name }}" 
                                           {{ in_array($role->name, $selectedRoles) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">{{ $role->display_name ?? ucfirst($role->name) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('target_roles')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Leave empty to show to all users, or select specific roles</p>
                    </div>

                    <!-- Options -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Options</h3>
                    </div>

                    <!-- Is Active -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" 
                                   {{ old('is_active', $announcement->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_active" class="ml-2 block text-sm text-gray-700">
                                Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Whether the announcement is active and can be displayed</p>
                    </div>

                    <!-- Requires Acknowledgment -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="requires_acknowledgment" id="requires_acknowledgment" value="1" 
                                   {{ old('requires_acknowledgment', $announcement->requires_acknowledgment) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="requires_acknowledgment" class="ml-2 block text-sm text-gray-700">
                                Requires Acknowledgment
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Whether users must explicitly acknowledge reading this announcement</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('announcements.show', $announcement) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Announcement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-adjust textarea height
    const contentTextarea = document.getElementById('content');
    contentTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Validate expiry date is after publish date
    const publishedAt = document.getElementById('published_at');
    const expiresAt = document.getElementById('expires_at');
    
    function validateDates() {
        if (publishedAt.value && expiresAt.value) {
            const publishDate = new Date(publishedAt.value);
            const expiryDate = new Date(expiresAt.value);
            
            if (expiryDate <= publishDate) {
                expiresAt.setCustomValidity('Expiry date must be after publish date');
            } else {
                expiresAt.setCustomValidity('');
            }
        } else {
            expiresAt.setCustomValidity('');
        }
    }
    
    publishedAt.addEventListener('change', validateDates);
    expiresAt.addEventListener('change', validateDates);
});
</script>
@endsection
