<?php

namespace Plugins\Announcements\Controllers;

use App\Http\Controllers\Controller;
use App\Traits\PluginDataManagement;
use Plugins\Announcements\Models\Announcement;
use Plugins\Announcements\Models\AnnouncementRead;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class AnnouncementController extends Controller
{
    use PluginDataManagement;

    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        return match($action) {
            'index', 'show', 'getUnreadCount', 'getUnreadAnnouncements' => 'view_announcements',
            'create', 'store', 'edit', 'update', 'destroy' => 'manage_announcements',
            'acknowledge', 'markAsRead' => 'view_announcements',
            default => null,
        };
    }

    /**
     * Display a listing of announcements
     */
    public function index(Request $request): View
    {
        $query = Announcement::with(['creator', 'reads'])
                             ->orderBy('priority', 'desc')
                             ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Acknowledgment filter (only for users with manage_announcements permission)
        if ($request->filled('acknowledgment') && Auth::user()->hasPermission('manage_announcements')) {
            $acknowledgment = $request->get('acknowledgment');

            if ($acknowledgment === 'unacknowledged') {
                // Show announcements requiring acknowledgment that have users who haven't acknowledged
                $query->where('requires_acknowledgment', true)
                      ->whereHas('reads', function ($q) {
                          $q->whereNull('acknowledged_at');
                      })
                      ->orWhere(function ($q) {
                          $q->where('requires_acknowledgment', true)
                            ->whereDoesntHave('reads', function ($subQ) {
                                $subQ->whereNotNull('acknowledged_at');
                            });
                      });
            } elseif ($acknowledgment === 'fully_acknowledged') {
                // Show announcements that are fully acknowledged by all users
                $query->where('requires_acknowledgment', true)
                      ->whereDoesntHave('reads', function ($q) {
                          $q->whereNull('acknowledged_at');
                      })
                      ->whereHas('reads', function ($q) {
                          $q->whereNotNull('acknowledged_at');
                      });
            }
        }

        $announcements = $query->paginate(15);

        // Add read statistics to each announcement
        $announcements->getCollection()->transform(function ($announcement) {
            $announcement->read_stats = $announcement->getReadStats();
            return $announcement;
        });

        return view('plugins.announcements::index', compact('announcements'));
    }

    /**
     * Show the form for creating a new announcement
     */
    public function create(): View
    {
        return view('plugins.announcements::create');
    }

    /**
     * Store a newly created announcement
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'priority' => 'required|in:low,normal,high,urgent',
            'type' => 'required|in:info,warning,success,error',
            'is_active' => 'boolean',
            'requires_acknowledgment' => 'boolean',
            'published_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:published_at',
            'target_roles' => 'nullable|array',
            'target_roles.*' => 'string',
        ]);

        $validated['created_by'] = Auth::id();
        $validated['is_active'] = $request->boolean('is_active', true);
        $validated['requires_acknowledgment'] = $request->boolean('requires_acknowledgment', true);

        // Set published_at to now if not provided and is_active is true
        if ($validated['is_active'] && !$validated['published_at']) {
            $validated['published_at'] = now();
        }

        $announcement = Announcement::create($validated);

        return redirect()->route('announcements.index')
                        ->with('success', 'Announcement created successfully.');
    }

    /**
     * Display the specified announcement
     */
    public function show(Announcement $announcement): View
    {
        $announcement->load(['creator', 'reads.user']);
        $readStats = $announcement->getReadStats();
        
        // Get recent read activity
        $recentReads = $announcement->reads()
                                  ->with('user')
                                  ->whereNotNull('read_at')
                                  ->orderBy('read_at', 'desc')
                                  ->limit(10)
                                  ->get();

        return view('plugins.announcements::show', compact('announcement', 'readStats', 'recentReads'));
    }

    /**
     * Show the form for editing the specified announcement
     */
    public function edit(Announcement $announcement): View
    {
        return view('plugins.announcements::edit', compact('announcement'));
    }

    /**
     * Update the specified announcement
     */
    public function update(Request $request, Announcement $announcement): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'priority' => 'required|in:low,normal,high,urgent',
            'type' => 'required|in:info,warning,success,error',
            'is_active' => 'boolean',
            'requires_acknowledgment' => 'boolean',
            'published_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:published_at',
            'target_roles' => 'nullable|array',
            'target_roles.*' => 'string',
        ]);

        $validated['is_active'] = $request->boolean('is_active', true);
        $validated['requires_acknowledgment'] = $request->boolean('requires_acknowledgment', true);

        $announcement->update($validated);

        return redirect()->route('announcements.index')
                        ->with('success', 'Announcement updated successfully.');
    }

    /**
     * Remove the specified announcement
     */
    public function destroy(Announcement $announcement): RedirectResponse
    {
        $announcement->delete();

        return redirect()->route('announcements.index')
                        ->with('success', 'Announcement deleted successfully.');
    }

    /**
     * Mark announcement as read by current user
     */
    public function markAsRead(Request $request, Announcement $announcement): JsonResponse
    {
        $user = Auth::user();

        if (!$announcement->isVisibleToUser($user)) {
            return response()->json(['success' => false, 'message' => 'Announcement not accessible.'], 403);
        }

        $read = $announcement->markAsReadByUser($user);

        return response()->json([
            'success' => true,
            'message' => 'Announcement marked as read.',
            'read_at' => $read->read_at->toISOString(),
        ]);
    }

    /**
     * Acknowledge announcement by current user
     */
    public function acknowledge(Request $request, Announcement $announcement): JsonResponse
    {
        $user = Auth::user();

        if (!$announcement->isVisibleToUser($user)) {
            return response()->json(['success' => false, 'message' => 'Announcement not accessible.'], 403);
        }

        $read = $announcement->acknowledgeByUser($user);

        return response()->json([
            'success' => true,
            'message' => 'Announcement acknowledged successfully.',
            'acknowledged_at' => $read->acknowledged_at->toISOString(),
        ]);
    }

    /**
     * Get unread announcement count for current user
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        $user = Auth::user();

        $unreadCount = Announcement::active()
                                  ->published()
                                  ->whereDoesntHave('reads', function ($query) use ($user) {
                                      $query->where('user_id', $user->id)
                                            ->whereNotNull('read_at');
                                  })
                                  ->get()
                                  ->filter(function ($announcement) use ($user) {
                                      return $announcement->isVisibleToUser($user);
                                  })
                                  ->count();

        return response()->json(['unread_count' => $unreadCount]);
    }

    /**
     * Get unread announcements for current user
     */
    public function getUnreadAnnouncements(Request $request): JsonResponse
    {
        $user = Auth::user();

        $announcements = Announcement::active()
                                    ->published()
                                    ->with('creator')
                                    ->whereDoesntHave('reads', function ($query) use ($user) {
                                        $query->where('user_id', $user->id)
                                              ->whereNotNull('read_at');
                                    })
                                    ->orderBy('priority', 'desc')
                                    ->orderBy('created_at', 'desc')
                                    ->get()
                                    ->filter(function ($announcement) use ($user) {
                                        return $announcement->isVisibleToUser($user);
                                    })
                                    ->values();

        return response()->json([
            'announcements' => $announcements->map(function ($announcement) {
                return [
                    'id' => $announcement->id,
                    'title' => $announcement->title,
                    'content' => $announcement->content,
                    'priority' => $announcement->priority,
                    'type' => $announcement->type,
                    'requires_acknowledgment' => $announcement->requires_acknowledgment,
                    'created_at' => $announcement->created_at->toISOString(),
                    'creator' => $announcement->creator->name,
                ];
            }),
        ]);
    }

    /**
     * Get plugin name for data management
     */
    protected function getPluginName(): string
    {
        return 'announcements';
    }

    /**
     * Get plugin seeder class for data management
     */
    protected function getPluginSeederClass(): ?string
    {
        $possibleSeeders = [
            "Plugins\\Announcements\\Seeds\\AnnouncementsPluginSeeder",
            "Plugins\\Announcements\\Seeds\\SampleDataSeeder",
        ];

        foreach ($possibleSeeders as $seederClass) {
            if (class_exists($seederClass)) {
                return $seederClass;
            }
        }

        return null;
    }

    /**
     * Get plugin tables for data management
     */
    protected function getPluginTables(): array
    {
        return [
            'announcement_reads',
            'announcements',
        ];
    }
}
