<?php

use Illuminate\Support\Facades\Route;
use Plugins\Navigation\Controllers\NavigationController;

// Navigation Management Routes
Route::prefix('navigation')->name('navigation.')->group(function () {
    Route::get('/', [NavigationController::class, 'index'])->name('index');
    Route::get('/tree', [NavigationController::class, 'tree'])->name('tree'); // No permission check - used for rendering navigation
    Route::post('/', [NavigationController::class, 'store'])->name('store');
    Route::put('/{navigationMenu}', [NavigationController::class, 'update'])->name('update');
    Route::delete('/{navigationMenu}', [NavigationController::class, 'destroy'])->name('destroy');
    Route::post('/update-order', [NavigationController::class, 'updateOrder'])->name('update-order');
    Route::post('/reset', [NavigationController::class, 'reset'])->name('reset');
});
