<?php

namespace Plugins\Navigation\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NavigationMenu;

class NavigationMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding navigation menu items...');

        // Ensure all existing items are active and visible
        NavigationMenu::query()->update([
            'is_active' => true,
            'visible' => true
        ]);

        // Create default navigation items if they don't exist
        $defaultItems = [
            [
                'name' => 'dashboard',
                'label' => 'Dashboard',
                'icon' => 'fas fa-home',
                'url' => '/',
                'permissions' => [],
                'sort_order' => 1,
                'is_active' => true,
                'visible' => true,
            ],
            [
                'name' => 'navigation',
                'label' => 'Navigation',
                'icon' => 'fas fa-sitemap',
                'url' => '/navigation',
                'permissions' => ['view_navigation'],
                'sort_order' => 10,
                'is_active' => true,
                'visible' => true,
            ],
        ];

        foreach ($defaultItems as $itemData) {
            NavigationMenu::firstOrCreate(
                ['name' => $itemData['name']],
                $itemData
            );
            $this->command->info("Created/Updated navigation item: {$itemData['name']}");
        }

        $this->command->info('Navigation menu items seeded successfully!');
    }
}
