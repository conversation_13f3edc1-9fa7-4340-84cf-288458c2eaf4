<?php

namespace Plugins\Navigation\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class NavigationPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding navigation permissions...');

        // Define navigation permissions
        $permissions = [
            [
                'name' => 'manage_navigation',
                'display_name' => 'Manage Navigation',
                'description' => 'Create, edit, and delete navigation menu items',
                'plugin' => 'navigation',
            ],
            [
                'name' => 'view_navigation',
                'display_name' => 'View Navigation',
                'description' => 'View navigation management interface',
                'plugin' => 'navigation',
            ],
        ];

        // Create permissions
        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'display_name' => $permissionData['display_name'],
                    'description' => $permissionData['description'],
                    'plugin' => $permissionData['plugin'],
                ]
            );

            $this->command->info("Created/Updated permission: {$permission->name}");
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        $this->command->info('Navigation permissions seeded successfully!');
    }

    /**
     * Assign permissions to default roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Get permissions
        $manageNavigationPermission = Permission::where('name', 'manage_navigation')->first();
        $viewNavigationPermission = Permission::where('name', 'view_navigation')->first();

        if (!$manageNavigationPermission || !$viewNavigationPermission) {
            $this->command->warn('Navigation permissions not found. Skipping role assignment.');
            return;
        }

        // Assign to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->permissions()->syncWithoutDetaching([
                $manageNavigationPermission->id,
                $viewNavigationPermission->id,
            ]);
            $this->command->info("Assigned navigation permissions to admin role");
        }

        // Assign view permission to editor role
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $editorRole->permissions()->syncWithoutDetaching([
                $viewNavigationPermission->id,
            ]);
            $this->command->info("Assigned view navigation permission to editor role");
        }
    }
}
