Product Requirements Document (PRD)
📌 Project Name
Business

🧠 Purpose
“Business” is a modular, plugin-based Laravel application intended to be scalable and extensible. It allows feature sets to be separated into plugins that manage their own MVC components, routes, database migrations, seeds, and configurations. The plugin system supports dependency management, enabling and disabling of plugins, and graceful fallback behaviors.

🏗️ Tech Stack
Backend: Laravel (latest stable)

Frontend: Tailwind CSS (CDN + JIT), Font Awesome

Database: SQLite

Icons/Favicon: https://taqnyat.sa/assets/images/icon.ico

🔌 Core Features
1. 🔌 Plugin System
Description:
A flexible architecture that allows developers to create isolated plugins. Each plugin can be enabled or disabled independently (with dependency checks).

Plugin Structure:
Each plugin is a folder inside /plugins/ and contains:

/Controllers

/Models

/Views

/Migrations

/Seeds

routes.php

config.json

Example config.json:
json
Copy
Edit
{
  "name": "users",
  "version": "1.0.0",
  "enabled": true,
  "dependencies": [],
  "description": "Handles user authentication and access control"
}
Enabling/Disabling:
A plugin can be enabled only if all of its dependencies are enabled.

Disabling a plugin will automatically disable any plugins that depend on it.

Dependencies are declared in each plugin's config.json.

2. ⚙️ Plugin Manager
Features:
Lists all available plugins.

Shows status: Enabled / Disabled.

Supports enabling/disabling plugins with dependency validation.

Warns or prevents action if a dependency is missing or broken.

3. 👤 Built-in Plugin: users
Purpose:
Handles authentication and authorization.

Pages:
/login

/unauthorized (401)

Behaviors:
Required by any plugin needing access control, user session handling, etc.

If disabled, any dependent plugin will be disabled automatically.

Cannot be enabled if required dependencies are missing.

🚦 User Roles
For now, the app assumes a developer/admin-level user managing the plugins from the backend. No multi-role system is required initially.

📈 Non-Functional Requirements
Performance: Lightweight and responsive, thanks to Tailwind JIT.

Scalability: Plugins can be added without altering core.

Security: Users plugin manages login, session, and 401 error handling.

Maintainability: Clean separation of concerns through plugin folders.

🔍 Future Considerations (Nice to have)
Plugin marketplace or installer.

Versioning and auto-update check per plugin.

GUI for creating new plugin scaffolds.

Role-based access to plugin manager.

📁 Folder Structure (High-level)
bash
Copy
Edit
/app
/plugins
  /users
    /Controllers
    /Models
    /Views
    /Migrations
    /Seeds
    routes.php
    config.json
/public
/resources
/routes
/database
