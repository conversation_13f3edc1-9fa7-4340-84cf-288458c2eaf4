#!/bin/bash

echo "=== Fixing Backup 502 Error ==="
echo ""

echo "1. Checking if fixes are in place..."

# Check if the BackupController fixes are present
if grep -q "private function createBackup(bool \$includeFiles = true, ?string \$description = ''):" plugins/settings/Controllers/BackupController.php; then
    echo "   ✓ BackupController method signature is fixed"
else
    echo "   ✗ BackupController method signature needs fixing"
fi

if grep -q "\$description = \$request->input('description', '') ?? '';" plugins/settings/Controllers/BackupController.php; then
    echo "   ✓ Input handling is fixed"
else
    echo "   ✗ Input handling needs fixing"
fi

if grep -q "use Plugins\\\\Business\\\\Controllers\\\\ImportController;" routes/web.php; then
    echo "   ✓ Routes file namespace is fixed"
else
    echo "   ✗ Routes file namespace needs fixing"
fi

echo ""
echo "2. Clearing PHP OPcache (if enabled)..."
# Try to clear OPcache if it's enabled
php -r "if (function_exists('opcache_reset')) { opcache_reset(); echo 'OPcache cleared'; } else { echo 'OPcache not enabled'; }"
echo ""

echo "3. Checking PHP syntax..."
php -l plugins/settings/Controllers/BackupController.php
echo ""

echo "4. Checking routes syntax..."
php -l routes/web.php
echo ""

echo "5. Instructions to fix the 502 error:"
echo ""
echo "   The backup system has been fixed, but you need to restart Herd to clear any cached files:"
echo ""
echo "   Option 1 - Restart Herd completely:"
echo "   • Open Herd application"
echo "   • Click on the Herd menu"
echo "   • Select 'Restart Services' or 'Quit' then reopen Herd"
echo ""
echo "   Option 2 - Restart just the site:"
echo "   • Run: herd restart business"
echo ""
echo "   Option 3 - Clear Laravel caches (if you can access artisan):"
echo "   • php artisan cache:clear"
echo "   • php artisan config:clear"
echo "   • php artisan route:clear"
echo "   • php artisan view:clear"
echo ""

echo "6. After restarting, test the backup:"
echo "   • Go to: http://business.test/settings/backups"
echo "   • Click 'Create Backup'"
echo "   • Leave description empty (this was causing the error)"
echo "   • Click 'Create'"
echo "   • The backup should now work without the 502 error"
echo ""

echo "7. If you still get a 502 error:"
echo "   • Check the Laravel log: tail -f storage/logs/laravel.log"
echo "   • Look for any new error messages"
echo "   • The error should no longer be about null description parameters"
echo ""

echo "=== Summary of Fixes Applied ==="
echo ""
echo "✓ Fixed routes/web.php syntax error (ImportController namespace)"
echo "✓ Fixed BackupController null description handling"
echo "✓ Made description parameter nullable: ?string \$description = ''"
echo "✓ Added null coalescing in input: \$description ?? ''"
echo "✓ Added null protection in method: \$description = \$description ?? ''"
echo ""
echo "The backup system now includes comprehensive functionality:"
echo "• Complete database backup with SQL dump"
echo "• All application files and dependencies (vendor, node_modules)"
echo "• Environment configurations with sensitive data masking"
echo "• Comprehensive restore process with dependency installation"
echo "• Frontend asset building during restore"
echo ""
echo "=== Fix Complete ==="
