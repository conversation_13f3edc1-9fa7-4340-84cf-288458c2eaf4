/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:host,:root{--fa-style-family-duotone:"Font Awesome 6 Duotone";--fa-font-duotone-light:normal 300 1em/1 "Font Awesome 6 Duotone"}@font-face{font-family:"Font Awesome 6 Duotone";font-style:normal;font-weight:300;font-display:block;src:url(../webfonts/fa-duotone-light-300.woff2) format("woff2"),url(../webfonts/fa-duotone-light-300.ttf) format("truetype")}.fa-duotone.fa-light,.fadl{position:relative;font-weight:300;letter-spacing:normal}.fa-duotone.fa-light:before,.fadl:before{position:absolute;color:var(--fa-primary-color,inherit);opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-light:after,.fadl:after{color:var(--fa-secondary-color,inherit);opacity:var(--fa-secondary-opacity,.4)}.fa-duotone.fa-light.fa-swap-opacity:before,.fa-duotone.fa-swap-opacity:before,.fa-swap-opacity .fa-duotone.fa-light:before,.fa-swap-opacity .fadl:before,.fadl.fa-swap-opacity:before{opacity:var(--fa-secondary-opacity,.4)}.fa-duotone.fa-light.fa-swap-opacity:after,.fa-duotone.fa-swap-opacity:after,.fa-swap-opacity .fa-duotone.fa-light:after,.fa-swap-opacity .fadl:after,.fadl.fa-swap-opacity:after{opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-light.fa-inverse,.fadl.fa-inverse{color:var(--fa-inverse,#fff)}.fa-duotone.fa-light.fa-stack-1x,.fa-duotone.fa-light.fa-stack-2x,.fadl.fa-stack-1x,.fadl.fa-stack-2x{position:absolute}