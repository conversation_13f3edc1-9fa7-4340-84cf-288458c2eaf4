<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;
use ZipArchive;

class BackupSystemTest extends TestCase
{
    use RefreshDatabase;

    private $backupPath;
    private $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->backupPath = storage_path('app/backups');
        
        // Create test user with admin permissions
        $this->testUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        
        // Assign admin role (assuming role system exists)
        try {
            $this->testUser->assignRole('admin');
        } catch (\Exception $e) {
            // If role system not available, skip
        }
        
        // Ensure backup directory exists
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    protected function tearDown(): void
    {
        // Clean up test backups
        if (is_dir($this->backupPath)) {
            $files = glob($this->backupPath . '/backup-*.zip');
            foreach ($files as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }
        }
        
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_a_backup_with_files()
    {
        $this->actingAs($this->testUser);

        $response = $this->postJson('/admin/settings/backups', [
            'include_files' => true,
            'description' => 'Test backup with files'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Backup created successfully'
                ]);

        $backupData = $response->json('backup');
        $this->assertNotEmpty($backupData['filename']);
        $this->assertTrue($backupData['includes_files']);
        $this->assertEquals('Test backup with files', $backupData['description']);

        // Verify backup file exists
        $backupFile = $this->backupPath . '/' . $backupData['filename'];
        $this->assertFileExists($backupFile);

        // Verify backup contains expected files
        $this->verifyBackupContents($backupFile, true);
    }

    /** @test */
    public function it_can_create_a_database_only_backup()
    {
        $this->actingAs($this->testUser);

        $response = $this->postJson('/admin/settings/backups', [
            'include_files' => false,
            'description' => 'Database only backup'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Backup created successfully'
                ]);

        $backupData = $response->json('backup');
        $this->assertFalse($backupData['includes_files']);

        // Verify backup file exists
        $backupFile = $this->backupPath . '/' . $backupData['filename'];
        $this->assertFileExists($backupFile);

        // Verify backup contains only database and metadata
        $this->verifyBackupContents($backupFile, false);
    }

    /** @test */
    public function it_can_list_backups()
    {
        $this->actingAs($this->testUser);

        // Create a test backup first
        $this->postJson('/admin/settings/backups', [
            'include_files' => true,
            'description' => 'Test backup for listing'
        ]);

        $response = $this->getJson('/admin/settings/backups');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'backups' => [
                        '*' => [
                            'filename',
                            'size',
                            'created_at',
                            'info'
                        ]
                    ],
                    'stats' => [
                        'total_backups',
                        'total_size',
                        'total_size_formatted'
                    ]
                ]);

        $backups = $response->json('backups');
        $this->assertGreaterThan(0, count($backups));
    }

    /** @test */
    public function it_requires_authentication_for_backup_operations()
    {
        $response = $this->postJson('/admin/settings/backups', [
            'include_files' => true
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_backup_creation_parameters()
    {
        $this->actingAs($this->testUser);

        $response = $this->postJson('/admin/settings/backups', [
            'include_files' => 'invalid',
            'description' => str_repeat('a', 300) // Too long
        ]);

        $response->assertStatus(422);
    }

    /**
     * Verify backup file contents
     */
    private function verifyBackupContents(string $backupFile, bool $shouldIncludeFiles): void
    {
        $zip = new ZipArchive();
        $result = $zip->open($backupFile);
        
        $this->assertTrue($result === TRUE, 'Should be able to open backup zip file');

        // Check for required files
        $this->assertNotFalse($zip->locateName('database.sql'), 'Backup should contain database.sql');
        $this->assertNotFalse($zip->locateName('metadata.json'), 'Backup should contain metadata.json');

        // Verify metadata
        $metadataContent = $zip->getFromName('metadata.json');
        $this->assertNotFalse($metadataContent, 'Should be able to read metadata.json');
        
        $metadata = json_decode($metadataContent, true);
        $this->assertIsArray($metadata, 'Metadata should be valid JSON');
        $this->assertEquals($shouldIncludeFiles, $metadata['includes_files']);

        if ($shouldIncludeFiles) {
            // Check for application files
            $this->assertNotFalse($zip->locateName('composer.json'), 'Backup should contain composer.json');
            $this->assertNotFalse($zip->locateName('package.json'), 'Backup should contain package.json');
            $this->assertNotFalse($zip->locateName('.env'), 'Backup should contain .env');
            
            // Check for vendor directory (if it exists)
            if (is_dir(base_path('vendor'))) {
                $this->assertTrue($this->zipContainsDirectory($zip, 'vendor'), 'Backup should contain vendor directory');
            }
            
            // Check for node_modules directory (if it exists)
            if (is_dir(base_path('node_modules'))) {
                $this->assertTrue($this->zipContainsDirectory($zip, 'node_modules'), 'Backup should contain node_modules directory');
            }
        }

        $zip->close();
    }

    /**
     * Check if zip contains a directory
     */
    private function zipContainsDirectory(ZipArchive $zip, string $directory): bool
    {
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);
            if (str_starts_with($filename, $directory . '/')) {
                return true;
            }
        }
        return false;
    }
}
