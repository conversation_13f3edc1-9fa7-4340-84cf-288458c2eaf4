<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Login - {{ config('app.name', 'Business') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Tailwind CSS CDN with JIT -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Smooth transitions */
        * {
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }

        /* Background gradient animation */
        .gradient-bg {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans antialiased">
    <div class="min-h-screen flex">
        <!-- Left Side - Information Panel -->
        <div class="hidden lg:flex lg:w-1/2 gradient-bg relative overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
            <div class="relative z-10 flex flex-col justify-center px-12 py-12 text-white">
                <div class="max-w-md">
                    <!-- Logo -->
                    <div class="flex items-center mb-8">
                        <i class="fas fa-cube text-4xl mr-4"></i>
                        <h1 class="text-3xl font-bold">{{ config('app.name') }}</h1>
                    </div>

                    <!-- Welcome Message -->
                    <h2 class="text-4xl font-bold mb-6">Welcome Back!</h2>
                    <p class="text-xl mb-8 text-white text-opacity-90">
                        Access your Business Plugin Management System and take control of your modular applications.
                    </p>

                    <!-- Features List -->
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-puzzle-piece text-2xl mr-4 text-white text-opacity-80"></i>
                            <div>
                                <h3 class="font-semibold">Plugin Management</h3>
                                <p class="text-sm text-white text-opacity-75">Enable, disable, and configure plugins with ease</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users text-2xl mr-4 text-white text-opacity-80"></i>
                            <div>
                                <h3 class="font-semibold">User Management</h3>
                                <p class="text-sm text-white text-opacity-75">Comprehensive role-based access control</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-2xl mr-4 text-white text-opacity-80"></i>
                            <div>
                                <h3 class="font-semibold">Secure & Reliable</h3>
                                <p class="text-sm text-white text-opacity-75">Built with security and performance in mind</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-20 xl:px-24">
            <div class="mx-auto w-full max-w-sm lg:w-96">
                <div>
                    <!-- Mobile Logo -->
                    <div class="lg:hidden flex items-center justify-center mb-8">
                        <i class="fas fa-cube text-primary-600 text-3xl mr-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">{{ config('app.name') }}</h1>
                    </div>

                    <h2 class="text-3xl font-bold text-gray-900">Sign in to your account</h2>
                    <p class="mt-2 text-sm text-gray-600">
                        Enter your credentials to access the dashboard
                    </p>
                </div>

                <div class="mt-8">
                    <form class="space-y-6" method="POST" action="{{ route('login') }}">
                        @csrf

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">
                                Email address
                            </label>
                            <div class="mt-1 relative">
                                <input id="email" name="email" type="email" autocomplete="email" required 
                                       value="{{ old('email') }}"
                                       class="appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('email') border-red-300 @enderror">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                            </div>
                            @error('email')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                Password
                            </label>
                            <div class="mt-1 relative">
                                <input id="password" name="password" type="password" autocomplete="current-password" required
                                       class="appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('password') border-red-300 @enderror">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                            </div>
                            @error('password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Remember Me -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input id="remember" name="remember" type="checkbox" 
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <label for="remember" class="ml-2 block text-sm text-gray-900">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit" 
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                    <i class="fas fa-sign-in-alt text-primary-500 group-hover:text-primary-400"></i>
                                </span>
                                Sign in
                            </button>
                        </div>
                    </form>

                    <!-- Demo Credentials -->
                    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Demo Credentials (click to auto-fill):</h3>
                        <div class="text-xs text-gray-600 space-y-1">
                            <div class="cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors duration-150"
                                 onclick="fillCredentials('<EMAIL>', 'password')"
                                 title="Click to auto-fill admin credentials">
                                <strong>Admin:</strong> <EMAIL> / password
                            </div>
                            <div class="cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors duration-150"
                                 onclick="fillCredentials('<EMAIL>', 'password')"
                                 title="Click to auto-fill editor credentials">
                                <strong>Editor:</strong> <EMAIL> / password
                            </div>
                            <div class="cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors duration-150"
                                 onclick="fillCredentials('<EMAIL>', 'password')"
                                 title="Click to auto-fill user credentials">
                                <strong>User:</strong> <EMAIL> / password
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillCredentials(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;

            // Add a subtle animation to indicate the fields were filled
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');

            emailField.classList.add('ring-2', 'ring-green-500');
            passwordField.classList.add('ring-2', 'ring-green-500');

            setTimeout(() => {
                emailField.classList.remove('ring-2', 'ring-green-500');
                passwordField.classList.remove('ring-2', 'ring-green-500');
            }, 1000);
        }
    </script>
</body>
</html>
