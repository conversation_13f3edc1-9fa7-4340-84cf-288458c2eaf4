@php
    $hasChildren = isset($item['filtered_children']) && count($item['filtered_children']) > 0;
    $isActive = false;
    
    // Determine if this item is active
    if (isset($item['route']) && $item['route']) {
        $isActive = request()->routeIs($item['route'] . '*');
    } elseif (isset($item['url']) && $item['url']) {
        $isActive = request()->is(ltrim($item['url'], '/') . '*');
    }
    
    // Check if any children are active
    $hasActiveChild = false;
    if ($hasChildren) {
        foreach ($item['filtered_children'] as $child) {
            if (isset($child['route']) && $child['route'] && request()->routeIs($child['route'] . '*')) {
                $hasActiveChild = true;
                break;
            } elseif (isset($child['url']) && $child['url'] && request()->is(ltrim($child['url'], '/') . '*')) {
                $hasActiveChild = true;
                break;
            }
        }
    }
    
    $isExpanded = $isActive || $hasActiveChild;
@endphp

@if($item['type'] === 'separator')
    <!-- Separator -->
    <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
    @if($item['label'])
        <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            {{ $item['label'] }}
        </h3>
    @endif
@else
    @if($hasChildren)
        <!-- Parent item with children -->
        <div class="space-y-1">
            <button type="button"
                    onclick="toggleSubmenu('{{ $item['name'] }}')"
                    class="w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md {{ $isActive || $hasActiveChild ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white' }} transition duration-150 ease-in-out">
                <div class="flex items-center">
                    @if($item['icon'])
                        <i class="{{ $item['icon'] }} mr-3 {{ $isActive || $hasActiveChild ? 'text-primary-500 dark:text-primary-400' : 'text-gray-400 dark:text-gray-300' }} group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
                    @endif
                    {{ $item['label'] }}
                </div>
                <i id="{{ $item['name'] }}-menu-icon" class="fas fa-chevron-right transition-transform duration-200 {{ $isExpanded ? 'transform rotate-90' : '' }}"></i>
            </button>

            <!-- Submenu -->
            <div id="{{ $item['name'] }}-submenu" class="ml-6 space-y-1 {{ $isExpanded ? '' : 'hidden' }}">
                @foreach($item['filtered_children'] as $child)
                    @include('partials.navigation-item', ['item' => $child])
                @endforeach
            </div>
        </div>
    @else
        <!-- Regular link item -->
        @php
            $href = '#';
            if (isset($item['route']) && $item['route']) {
                $href = route($item['route']);
            } elseif (isset($item['url']) && $item['url']) {
                $href = url($item['url']);
            }
            
            $target = $item['target'] ?? '_self';
        @endphp
        
        <a href="{{ $href }}" 
           target="{{ $target }}"
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ $isActive ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white' }} transition duration-150 ease-in-out">
            @if($item['icon'])
                <i class="{{ $item['icon'] }} mr-3 {{ $isActive ? 'text-primary-500 dark:text-primary-400' : 'text-gray-400 dark:text-gray-300' }} group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
            @endif
            {{ $item['label'] }}
        </a>
    @endif
@endif
