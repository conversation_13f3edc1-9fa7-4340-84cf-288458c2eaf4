<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add blog permissions
        $blogPermissions = [
            [
                'name' => 'manage_blog',
                'display_name' => 'Manage Blog',
                'description' => 'Full access to blog management including all blog operations',
                'type' => 'system'
            ],
            [
                'name' => 'create_posts',
                'display_name' => 'Create Posts',
                'description' => 'Create new blog posts',
                'type' => 'system'
            ],
            [
                'name' => 'edit_posts',
                'display_name' => 'Edit Posts',
                'description' => 'Edit existing blog posts',
                'type' => 'system'
            ],
            [
                'name' => 'delete_posts',
                'display_name' => 'Delete Posts',
                'description' => 'Delete blog posts',
                'type' => 'system'
            ],
            [
                'name' => 'view_blog',
                'display_name' => 'View Blog',
                'description' => 'View blog posts and blog interface',
                'type' => 'system'
            ]
        ];

        foreach ($blogPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove blog permissions
        $blogPermissions = ['manage_blog', 'create_posts', 'edit_posts', 'delete_posts', 'view_blog'];

        Permission::whereIn('name', $blogPermissions)->delete();
    }
};
