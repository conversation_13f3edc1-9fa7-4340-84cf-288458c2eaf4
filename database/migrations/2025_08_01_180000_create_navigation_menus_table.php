<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('navigation_menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type')->default('link'); // link, separator
            $table->string('label');
            $table->string('icon')->nullable();
            $table->string('url')->nullable();
            $table->string('route')->nullable();
            $table->string('plugin')->nullable();
            $table->json('permissions')->nullable(); // Array of required permissions
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('visible')->default(true);
            $table->boolean('is_system')->default(false); // System menus cannot be deleted
            $table->string('target')->default('_self'); // _self, _blank, etc.
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();

            $table->foreign('parent_id')->references('id')->on('navigation_menus')->onDelete('cascade');
            $table->index(['parent_id', 'sort_order']);
            $table->index(['plugin', 'is_active']);
            $table->index('is_system');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('navigation_menus');
    }
};
