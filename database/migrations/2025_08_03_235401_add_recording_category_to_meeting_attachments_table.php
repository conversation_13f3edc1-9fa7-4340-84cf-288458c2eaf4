<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with the new constraint
        // First, create a temporary table with the new constraint
        DB::statement('CREATE TABLE "meeting_attachments_temp" (
            "id" integer primary key autoincrement not null,
            "meeting_id" integer not null,
            "filename" varchar not null,
            "original_filename" varchar not null,
            "file_path" varchar not null,
            "mime_type" varchar not null,
            "file_size" integer not null,
            "attachment_type" varchar check ("attachment_type" in (\'document\', \'image\', \'audio\', \'video\', \'other\')) not null default \'document\',
            "category" varchar check ("category" in (\'pre_meeting\', \'post_meeting\', \'minutes\', \'recording\', \'other\')) not null default \'other\',
            "description" text,
            "is_public" tinyint(1) not null default \'0\',
            "uploaded_by" integer not null,
            "download_count" integer not null default \'0\',
            "created_at" datetime,
            "updated_at" datetime,
            foreign key("meeting_id") references "meetings"("id") on delete cascade,
            foreign key("uploaded_by") references "users"("id") on delete cascade
        )');

        // Copy data from the original table
        DB::statement('INSERT INTO meeting_attachments_temp SELECT * FROM meeting_attachments');

        // Drop the original table
        DB::statement('DROP TABLE meeting_attachments');

        // Rename the temporary table
        DB::statement('ALTER TABLE meeting_attachments_temp RENAME TO meeting_attachments');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the table without the 'recording' category
        DB::statement('CREATE TABLE "meeting_attachments_temp" (
            "id" integer primary key autoincrement not null,
            "meeting_id" integer not null,
            "filename" varchar not null,
            "original_filename" varchar not null,
            "file_path" varchar not null,
            "mime_type" varchar not null,
            "file_size" integer not null,
            "attachment_type" varchar check ("attachment_type" in (\'document\', \'image\', \'audio\', \'video\', \'other\')) not null default \'document\',
            "category" varchar check ("category" in (\'pre_meeting\', \'post_meeting\', \'minutes\', \'other\')) not null default \'other\',
            "description" text,
            "is_public" tinyint(1) not null default \'0\',
            "uploaded_by" integer not null,
            "download_count" integer not null default \'0\',
            "created_at" datetime,
            "updated_at" datetime,
            foreign key("meeting_id") references "meetings"("id") on delete cascade,
            foreign key("uploaded_by") references "users"("id") on delete cascade
        )');

        // Copy data from the original table (excluding 'recording' category)
        DB::statement('INSERT INTO meeting_attachments_temp SELECT * FROM meeting_attachments WHERE category != \'recording\'');

        // Drop the original table
        DB::statement('DROP TABLE meeting_attachments');

        // Rename the temporary table
        DB::statement('ALTER TABLE meeting_attachments_temp RENAME TO meeting_attachments');
    }
};
