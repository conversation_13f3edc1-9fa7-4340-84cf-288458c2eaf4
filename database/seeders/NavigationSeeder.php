<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NavigationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create the navigation_menus table if it doesn't exist
        if (!DB::getSchemaBuilder()->hasTable('navigation_menus')) {
            DB::statement('
                CREATE TABLE navigation_menus (
                    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    name VARCHA<PERSON>(255) NOT NULL,
                    type VARCHAR(255) DEFAULT "link",
                    label VARCHAR(255) NOT NULL,
                    icon VARCHAR(255) NULL,
                    url VARCHAR(255) NULL,
                    route VARCHAR(255) NULL,
                    plugin VA<PERSON>HA<PERSON>(255) NULL,
                    permissions JSON NULL,
                    parent_id BIGINT UNSIGNED NULL,
                    sort_order INT DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    visible BOOLEAN DEFAULT TRUE,
                    is_system BOOLEAN DEFAULT FALSE,
                    target VARCHAR(255) DEFAULT "_self",
                    metadata JSON NULL,
                    created_at TIMESTAMP NULL,
                    updated_at TIMESTAMP NULL,
                    INDEX idx_parent_sort (parent_id, sort_order),
                    INDEX idx_plugin_active (plugin, is_active),
                    INDEX idx_system (is_system)
                )
            ');
        }

        // Clear existing navigation
        DB::table('navigation_menus')->truncate();

        $sortOrder = 1;

        // Dashboard
        DB::table('navigation_menus')->insert([
            'name' => 'dashboard',
            'label' => 'Dashboard',
            'icon' => 'fas fa-home',
            'url' => '/',
            'permissions' => json_encode([]),
            'sort_order' => $sortOrder++,
            'is_system' => true,
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Plugin Management
        DB::table('navigation_menus')->insert([
            'name' => 'plugins',
            'label' => 'Plugins',
            'icon' => 'fas fa-puzzle-piece',
            'url' => '/plugins',
            'permissions' => json_encode(['manage_plugins']),
            'sort_order' => $sortOrder++,
            'is_system' => true,
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Navigation Management
        DB::table('navigation_menus')->insert([
            'name' => 'navigation',
            'label' => 'Navigation',
            'icon' => 'fas fa-sitemap',
            'url' => '/navigation',
            'permissions' => json_encode(['manage_plugins']),
            'sort_order' => $sortOrder++,
            'is_system' => true,
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Users Management (parent)
        $usersMenuId = DB::table('navigation_menus')->insertGetId([
            'name' => 'users',
            'label' => 'Users',
            'icon' => 'fas fa-users',
            'url' => '#',
            'permissions' => json_encode(['manage_users', 'manage_roles', 'manage_permissions']),
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'users',
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Users sub-items
        DB::table('navigation_menus')->insert([
            'name' => 'users-list',
            'label' => 'User Management',
            'icon' => 'fas fa-user',
            'url' => '/users',
            'parent_id' => $usersMenuId,
            'permissions' => json_encode(['manage_users']),
            'sort_order' => 1,
            'is_system' => false,
            'plugin' => 'users',
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('navigation_menus')->insert([
            'name' => 'roles-list',
            'label' => 'Role Management',
            'icon' => 'fas fa-user-shield',
            'url' => '/roles',
            'parent_id' => $usersMenuId,
            'permissions' => json_encode(['manage_roles']),
            'sort_order' => 2,
            'is_system' => false,
            'plugin' => 'users',
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('navigation_menus')->insert([
            'name' => 'permissions-list',
            'label' => 'Permission Management',
            'icon' => 'fas fa-key',
            'url' => '/permissions',
            'parent_id' => $usersMenuId,
            'permissions' => json_encode(['manage_permissions']),
            'sort_order' => 3,
            'is_system' => false,
            'plugin' => 'users',
            'is_active' => true,
            'visible' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        echo "Navigation menu seeded successfully.\n";
    }
}
