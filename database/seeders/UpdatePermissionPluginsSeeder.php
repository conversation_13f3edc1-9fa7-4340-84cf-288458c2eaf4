<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;

class UpdatePermissionPluginsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Map permissions to their respective plugins
        $permissionPluginMap = [
            // Users plugin permissions
            'manage_users' => 'users',
            'manage_roles' => 'users',
            'manage_permissions' => 'users',
            
            // Business plugin permissions
            'manage_businesses' => 'business',
            'view_businesses' => 'business',
            'create_businesses' => 'business',
            'edit_businesses' => 'business',
            'delete_businesses' => 'business',
            'manage_contacts' => 'business',
            'view_contacts' => 'business',
            'create_contacts' => 'business',
            'edit_contacts' => 'business',
            'delete_contacts' => 'business',
            
            // Announcements plugin permissions
            'manage_announcements' => 'announcements',
            'view_announcements' => 'announcements',
            'create_announcements' => 'announcements',
            'edit_announcements' => 'announcements',
            'delete_announcements' => 'announcements',
            
            // Settings plugin permissions
            'manage_settings' => 'settings',
            
            // System permissions (no plugin assignment)
            'manage_plugins' => null,
            'view_dashboard' => null,
        ];

        foreach ($permissionPluginMap as $permissionName => $pluginName) {
            $permission = Permission::where('name', $permissionName)->first();
            
            if ($permission) {
                $permission->update(['plugin' => $pluginName]);
                $this->command->info("Updated permission '{$permissionName}' -> plugin: " . ($pluginName ?: 'System'));
            }
        }

        $this->command->info('Permission plugin assignments updated successfully.');
    }
}
