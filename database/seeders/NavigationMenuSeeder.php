<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NavigationMenu;

class NavigationMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing navigation
        NavigationMenu::truncate();

        $sortOrder = 1;

        // Dashboard
        NavigationMenu::create([
            'name' => 'dashboard',
            'label' => 'Dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'route' => 'dashboard',
            'permissions' => ['view_dashboard'],
            'sort_order' => $sortOrder++,
            'is_system' => true,
        ]);

        // Users Management
        $usersMenu = NavigationMenu::create([
            'name' => 'users',
            'label' => 'Users',
            'icon' => 'fas fa-users',
            'permissions' => ['manage_users', 'manage_roles', 'manage_permissions'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        // Users submenu
        NavigationMenu::create([
            'name' => 'users-list',
            'label' => 'All Users',
            'icon' => 'fas fa-user',
            'route' => 'users.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_users'],
            'sort_order' => 1,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        NavigationMenu::create([
            'name' => 'roles-list',
            'label' => 'Roles',
            'icon' => 'fas fa-user-tag',
            'route' => 'roles.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_roles'],
            'sort_order' => 2,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        NavigationMenu::create([
            'name' => 'permissions-list',
            'label' => 'Permissions',
            'icon' => 'fas fa-key',
            'route' => 'permissions.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_permissions'],
            'sort_order' => 3,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        // Business Management
        $businessMenu = NavigationMenu::create([
            'name' => 'business',
            'label' => 'Business',
            'icon' => 'fas fa-building',
            'permissions' => ['manage_businesses', 'view_businesses'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'business',
        ]);

        NavigationMenu::create([
            'name' => 'businesses-list',
            'label' => 'All Businesses',
            'icon' => 'fas fa-building',
            'route' => 'business.index',
            'parent_id' => $businessMenu->id,
            'permissions' => ['manage_businesses', 'view_businesses'],
            'sort_order' => 1,
            'is_system' => false,
            'plugin' => 'business',
        ]);

        NavigationMenu::create([
            'name' => 'contacts-list',
            'label' => 'Contacts',
            'icon' => 'fas fa-address-book',
            'route' => 'business.contacts.index',
            'parent_id' => $businessMenu->id,
            'permissions' => ['manage_contacts', 'view_contacts'],
            'sort_order' => 2,
            'is_system' => false,
            'plugin' => 'business',
        ]);

        // Announcements
        NavigationMenu::create([
            'name' => 'announcements',
            'label' => 'Announcements',
            'icon' => 'fas fa-bullhorn',
            'route' => 'announcements.index',
            'permissions' => ['manage_announcements', 'view_announcements'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'announcements',
        ]);

        // Settings
        NavigationMenu::create([
            'name' => 'settings',
            'label' => 'Settings',
            'icon' => 'fas fa-cog',
            'route' => 'settings.index',
            'permissions' => ['manage_settings'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'settings',
        ]);

        // Navigation Management (Admin only)
        NavigationMenu::create([
            'name' => 'navigation',
            'label' => 'Navigation',
            'icon' => 'fas fa-sitemap',
            'route' => 'navigation.index',
            'permissions' => ['manage_plugins'],
            'sort_order' => $sortOrder++,
            'is_system' => true,
        ]);

        // Plugin Management
        NavigationMenu::create([
            'name' => 'plugins',
            'label' => 'Plugins',
            'icon' => 'fas fa-puzzle-piece',
            'route' => 'plugins.index',
            'permissions' => ['manage_plugins'],
            'sort_order' => $sortOrder++,
            'is_system' => true,
        ]);

        $this->command->info('Default navigation structure created successfully.');
    }
}
