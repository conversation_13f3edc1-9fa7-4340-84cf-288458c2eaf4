<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Plugins\Products\Models\Product;
use Plugins\Products\Models\ProductPricingItem;

class ProductPricingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find WhatsApp Business API product
        $whatsappProduct = Product::where('name', 'WhatsApp Business API')->first();

        if (!$whatsappProduct) {
            $this->command->info('WhatsApp Business API product not found. Creating it...');
            $whatsappProduct = Product::create([
                'name' => 'WhatsApp Business API',
                'slug' => 'whatsapp-business-api',
                'description' => 'Official WhatsApp Business API for enterprise messaging',
                'icon' => 'fab fa-whatsapp',
                'target_audience' => 'Businesses, Enterprises, Customer Service Teams',
                'use_cases' => 'Customer support, Marketing campaigns, Notifications, Two-way communication',
                'terms_and_conditions' => 'Standard WhatsApp Business API terms apply. Usage subject to WhatsApp policies.',
                'quotation_notes' => 'Pricing based on message volume and type. Setup includes API integration and testing.',
                'implementation_notes' => 'Implementation includes API setup, webhook configuration, and basic training.',
                'default_contract_duration_months' => 12,
                'support_details' => '24/7 technical support, dedicated account manager for enterprise clients',
                'warranty_details' => '99.9% uptime guarantee, message delivery warranty',
                'is_active' => true,
                'created_by' => 1,
            ]);
        }

        // Clear existing pricing items for this product
        $whatsappProduct->pricingItems()->delete();

        // Create comprehensive pricing structure
        $pricingItems = [
            [
                'name' => 'Marketing Messages',
                'description' => 'Promotional and marketing messages sent to customers',
                'price' => 0.18,
                'pricing_model' => 'per_unit',
                'unit_type' => 'message',
                'unit_label' => 'per message',
                'category' => 'messaging',
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'quotation_description' => 'Marketing messages for promotional campaigns and announcements',
                'include_in_quotations' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Utility Messages',
                'description' => 'Transactional messages like order confirmations, shipping updates',
                'price' => 0.05,
                'pricing_model' => 'per_unit',
                'unit_type' => 'message',
                'unit_label' => 'per message',
                'category' => 'messaging',
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'quotation_description' => 'Utility messages for transactional communications',
                'include_in_quotations' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Authentication Messages',
                'description' => 'OTP and verification messages for user authentication',
                'price' => 0.05,
                'pricing_model' => 'per_unit',
                'unit_type' => 'message',
                'unit_label' => 'per message',
                'category' => 'messaging',
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'quotation_description' => 'Authentication messages for OTP and verification',
                'include_in_quotations' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Monthly Subscription',
                'description' => 'Base monthly subscription for WhatsApp Business API access',
                'price' => 600.00,
                'pricing_model' => 'fixed',
                'category' => 'subscription',
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'features' => "API access\nWebhook support\nBasic analytics\nStandard support",
                'quotation_description' => 'Monthly subscription for WhatsApp Business API platform access',
                'include_in_quotations' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Setup & Integration',
                'description' => 'One-time setup and integration service',
                'price' => 1500.00,
                'pricing_model' => 'fixed',
                'category' => 'setup',
                'currency' => 'USD',
                'billing_cycle' => 'one-time',
                'features' => "API configuration\nWebhook setup\nTesting & validation\nBasic training\nDocumentation",
                'quotation_description' => 'Professional setup and integration of WhatsApp Business API',
                'include_in_quotations' => true,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Premium Support',
                'description' => 'Enhanced support with dedicated account manager',
                'price' => 500.00,
                'pricing_model' => 'fixed',
                'category' => 'support',
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'features' => "Dedicated account manager\nPriority support\nCustom integrations\nAdvanced analytics\nMonthly reviews",
                'quotation_description' => 'Premium support package with dedicated account management',
                'include_in_quotations' => false,
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Training & Consulting',
                'description' => 'Professional training and consulting services',
                'price' => 200.00,
                'pricing_model' => 'per_unit',
                'unit_type' => 'hour',
                'unit_label' => 'per hour',
                'category' => 'training',
                'currency' => 'USD',
                'billing_cycle' => 'one-time',
                'quotation_description' => 'Professional training and consulting services',
                'include_in_quotations' => false,
                'is_active' => true,
                'sort_order' => 7,
            ],
        ];

        foreach ($pricingItems as $item) {
            $whatsappProduct->pricingItems()->create($item);
        }

        $this->command->info('WhatsApp Business API pricing structure created successfully!');
        $this->command->info('Created ' . count($pricingItems) . ' pricing items.');
    }
}
