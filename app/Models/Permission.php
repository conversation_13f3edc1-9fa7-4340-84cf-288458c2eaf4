<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'plugin',
    ];

    /**
     * Get the roles that have this permission
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    /**
     * Get the total number of users who have this permission (through roles)
     */
    public function getTotalUsersCountAttribute(): int
    {
        $userCount = 0;

        foreach ($this->roles as $role) {
            // Count users with direct role assignment
            $userCount += $role->users()->count();

            // Count users with many-to-many role assignment
            $userCount += $role->usersMany()->count();
        }

        return $userCount;
    }

    /**
     * Get the plugin display name
     */
    public function getPluginDisplayNameAttribute(): string
    {
        if (!$this->plugin) {
            return 'System';
        }

        // Try to get plugin display name from plugin manager
        try {
            $pluginManager = app(\App\Services\PluginManager::class);
            $plugin = $pluginManager->getPlugin($this->plugin);

            if ($plugin && isset($plugin->config['display_name'])) {
                return $plugin->config['display_name'];
            }

            return ucfirst($this->plugin);
        } catch (\Exception $e) {
            return ucfirst($this->plugin);
        }
    }
}
