<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
    ];

    /**
     * Get the users that belong to this role
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the users that belong to this role (many-to-many)
     */
    public function usersMany()
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    /**
     * Get the permissions for this role
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class)->withTimestamps();
    }

    /**
     * Check if role has a specific permission
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->permissions()->where('name', $permissionName)->exists();
    }

    /**
     * Give permission to role
     */
    public function givePermissionTo(Permission $permission)
    {
        return $this->permissions()->attach($permission);
    }

    /**
     * Revoke permission from role
     */
    public function revokePermissionTo(Permission $permission)
    {
        return $this->permissions()->detach($permission);
    }
}
