<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'theme',
        'language',
        'sidebar_collapsed',
        'sidebar_position',
        'email_notifications',
        'browser_notifications',
        'sound_notifications',
        'date_format',
        'time_format',
        'timezone',
        'additional_preferences',
    ];

    protected $casts = [
        'sidebar_collapsed' => 'boolean',
        'email_notifications' => 'boolean',
        'browser_notifications' => 'boolean',
        'sound_notifications' => 'boolean',
        'additional_preferences' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user these preferences belong to
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get default preferences
     */
    public static function getDefaults(): array
    {
        return [
            'theme' => 'light',
            'language' => 'en',
            'sidebar_collapsed' => false,
            'sidebar_position' => 'left',
            'email_notifications' => true,
            'browser_notifications' => true,
            'sound_notifications' => false,
            'date_format' => 'Y-m-d',
            'time_format' => '24',
            'timezone' => 'UTC',
            'additional_preferences' => [],
        ];
    }

    /**
     * Get or create preferences for a user
     */
    public static function getForUser(User $user): self
    {
        try {
            return static::firstOrCreate(
                ['user_id' => $user->id],
                static::getDefaults()
            );
        } catch (\Exception $e) {
            // If table doesn't exist, return a new instance with defaults
            $instance = new static();
            $instance->fill(static::getDefaults());
            $instance->user_id = $user->id;
            return $instance;
        }
    }

    /**
     * Update a specific preference
     */
    public function updatePreference(string $key, $value): bool
    {
        if (in_array($key, $this->fillable)) {
            return $this->update([$key => $value]);
        }
        
        // Handle additional preferences
        $additional = $this->additional_preferences ?? [];
        $additional[$key] = $value;
        
        return $this->update(['additional_preferences' => $additional]);
    }

    /**
     * Get a specific preference value
     */
    public function getPreference(string $key, $default = null)
    {
        if (in_array($key, $this->fillable) && isset($this->attributes[$key])) {
            return $this->attributes[$key];
        }
        
        // Check additional preferences
        $additional = $this->additional_preferences ?? [];
        return $additional[$key] ?? $default;
    }

    /**
     * Check if dark mode is enabled
     */
    public function isDarkMode(): bool
    {
        if ($this->theme === 'dark') {
            return true;
        }
        
        if ($this->theme === 'system') {
            // For system theme, we'll default to light mode
            // In a real implementation, you might want to detect system preference
            return false;
        }
        
        return false;
    }

    /**
     * Get theme class for HTML
     */
    public function getThemeClass(): string
    {
        return $this->isDarkMode() ? 'dark' : '';
    }
}
