<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait PluginDataManagement
{
    use DatabaseCompatibility;
    /**
     * Seed sample data for the plugin
     */
    public function seedSampleData(Request $request)
    {
        // Check if user has plugin management permissions
        if (!$this->hasPluginManagementPermission()) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $pluginName = $this->getPluginName();
            $seederClass = $this->getPluginSeederClass();

            if (!$seederClass) {
                return response()->json([
                    'success' => false,
                    'message' => 'No seeder class found for this plugin.'
                ], 404);
            }

            // Run the sample data seeder
            Artisan::call('db:seed', [
                '--class' => $seederClass,
                '--force' => true
            ]);

            Log::info("Sample data seeded for plugin: {$pluginName}");

            return response()->json([
                'success' => true,
                'message' => 'Sample data seeded successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to seed sample data for plugin: " . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to seed sample data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all plugin data
     */
    public function clearAllData(Request $request)
    {
        // Check if user has plugin management permissions
        if (!$this->hasPluginManagementPermission()) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $pluginName = $this->getPluginName();
            $tables = $this->getPluginTables();

            if (empty($tables)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No tables defined for this plugin.'
                ], 404);
            }

            // Clear all plugin data in transaction with database-agnostic foreign key handling
            DB::transaction(function () use ($tables) {
                $this->safeDeleteFromTables($tables);
            });

            Log::info("All data cleared for plugin: {$pluginName} by user: " . auth()->user()->email);

            return response()->json([
                'success' => true,
                'message' => 'All plugin data cleared successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to clear plugin data for {$this->getPluginName()}: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the plugin name (should be implemented by each plugin controller)
     */
    protected function getPluginName(): string
    {
        // Extract plugin name from namespace
        $reflection = new \ReflectionClass($this);
        $namespace = $reflection->getNamespaceName();
        
        if (preg_match('/Plugins\\\\([^\\\\]+)/', $namespace, $matches)) {
            return $matches[1];
        }
        
        return 'Unknown';
    }

    /**
     * Get the plugin seeder class (should be implemented by each plugin controller)
     */
    protected function getPluginSeederClass(): ?string
    {
        $pluginName = $this->getPluginName();
        
        // Try common seeder class patterns
        $possibleSeeders = [
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}SampleDataSeeder",
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}PluginSeeder",
            "Plugins\\{$pluginName}\\Seeds\\SampleDataSeeder",
        ];
        
        foreach ($possibleSeeders as $seederClass) {
            if (class_exists($seederClass)) {
                return $seederClass;
            }
        }
        
        return null;
    }

    /**
     * Get plugin tables (should be implemented by each plugin controller)
     */
    protected function getPluginTables(): array
    {
        // Default implementation - should be overridden by each plugin
        return [];
    }

    /**
     * Check if user has plugin management permissions
     */
    protected function hasPluginManagementPermission(): bool
    {
        $user = auth()->user();
        
        // Check if user is admin
        if ($user->role && $user->role->name === 'admin') {
            return true;
        }
        
        // Check for specific plugin permissions
        $pluginName = strtolower($this->getPluginName());
        $permissions = [
            "manage_{$pluginName}",
            'manage_plugins',
            'admin_access'
        ];
        
        foreach ($permissions as $permission) {
            if ($user->hasPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }
}
