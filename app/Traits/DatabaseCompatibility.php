<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait DatabaseCompatibility
{
    /**
     * Disable foreign key checks in a database-agnostic way
     */
    protected function disableForeignKeyChecks(): void
    {
        $driver = DB::getDriverName();
        
        try {
            switch ($driver) {
                case 'mysql':
                    DB::statement('SET FOREIGN_KEY_CHECKS=0;');
                    break;
                case 'sqlite':
                    DB::statement('PRAGMA foreign_keys = OFF;');
                    break;
                case 'pgsql':
                    // PostgreSQL doesn't have a global way to disable foreign key checks
                    // We'll handle this by deleting in the correct order
                    break;
                case 'sqlsrv':
                    // SQL Server uses different syntax
                    DB::statement('EXEC sp_msforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all";');
                    break;
                default:
                    // For unknown drivers, we'll rely on proper deletion order
                    Log::warning("Unknown database driver '{$driver}' - foreign key checks not disabled");
                    break;
            }
        } catch (\Exception $e) {
            Log::error("Failed to disable foreign key checks for driver '{$driver}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Enable foreign key checks in a database-agnostic way
     */
    protected function enableForeignKeyChecks(): void
    {
        $driver = DB::getDriverName();
        
        try {
            switch ($driver) {
                case 'mysql':
                    DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                    break;
                case 'sqlite':
                    DB::statement('PRAGMA foreign_keys = ON;');
                    break;
                case 'pgsql':
                    // PostgreSQL doesn't need re-enabling
                    break;
                case 'sqlsrv':
                    // SQL Server re-enable constraints
                    DB::statement('EXEC sp_msforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all";');
                    break;
                default:
                    // For unknown drivers, no action needed
                    break;
            }
        } catch (\Exception $e) {
            Log::error("Failed to enable foreign key checks for driver '{$driver}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Execute a database operation with foreign key checks disabled
     * This is a safe wrapper that ensures foreign key checks are re-enabled even if an exception occurs
     */
    protected function withoutForeignKeyChecks(callable $callback): mixed
    {
        $driver = DB::getDriverName();
        
        if ($driver === 'pgsql') {
            // PostgreSQL: Execute callback directly (no FK disable/enable)
            return $callback();
        }
        
        $this->disableForeignKeyChecks();
        
        try {
            $result = $callback();
            $this->enableForeignKeyChecks();
            return $result;
        } catch (\Exception $e) {
            $this->enableForeignKeyChecks();
            throw $e;
        }
    }

    /**
     * Get the current database driver name
     */
    protected function getDatabaseDriver(): string
    {
        return DB::getDriverName();
    }

    /**
     * Check if the current database supports disabling foreign key checks
     */
    protected function supportsForeignKeyDisabling(): bool
    {
        $driver = DB::getDriverName();
        return in_array($driver, ['mysql', 'sqlite', 'sqlsrv']);
    }

    /**
     * Execute a truncate operation in a database-agnostic way
     * Some databases don't support TRUNCATE with foreign key constraints
     */
    protected function safeTruncateTable(string $table): void
    {
        if (!DB::getSchemaBuilder()->hasTable($table)) {
            return;
        }

        $driver = DB::getDriverName();
        
        try {
            if ($this->supportsForeignKeyDisabling()) {
                $this->withoutForeignKeyChecks(function () use ($table) {
                    DB::table($table)->truncate();
                });
            } else {
                // For databases that don't support FK disabling, use DELETE
                DB::table($table)->delete();
            }
        } catch (\Exception $e) {
            // Fallback to DELETE if TRUNCATE fails
            Log::warning("TRUNCATE failed for table '{$table}', falling back to DELETE: " . $e->getMessage());
            DB::table($table)->delete();
        }
    }

    /**
     * Delete all records from multiple tables in a safe, database-agnostic way
     */
    protected function safeDeleteFromTables(array $tables): void
    {
        $driver = DB::getDriverName();
        
        if ($driver === 'pgsql') {
            // PostgreSQL: Delete in correct order without disabling FK checks
            foreach ($tables as $table) {
                if (DB::getSchemaBuilder()->hasTable($table)) {
                    DB::table($table)->delete();
                }
            }
        } else {
            // Other databases: Use FK disable/enable wrapper
            $this->withoutForeignKeyChecks(function () use ($tables) {
                foreach ($tables as $table) {
                    if (DB::getSchemaBuilder()->hasTable($table)) {
                        DB::table($table)->delete();
                    }
                }
            });
        }
    }
}
