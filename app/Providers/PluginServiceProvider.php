<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Route;
use App\Services\PluginManager;

class PluginServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register PluginManager as singleton
        $this->app->singleton(PluginManager::class, function ($app) {
            return new PluginManager();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $pluginManager = $this->app->make(PluginManager::class);

        // Register enabled plugins
        $this->registerPlugins($pluginManager);
    }

    /**
     * Register all enabled plugins
     */
    private function registerPlugins(PluginManager $pluginManager): void
    {
        $enabledPlugins = $pluginManager->getEnabledPlugins();

        foreach ($enabledPlugins as $plugin) {
            $this->registerPluginRoutes($plugin);
            $this->registerPluginViews($plugin);
            $this->registerPluginMigrations($plugin);
            $this->loadPluginClasses($plugin);
        }
    }

    /**
     * Register plugin routes
     */
    private function registerPluginRoutes($plugin): void
    {
        $routesPath = $plugin->getRoutesPath();

        if (File::exists($routesPath)) {
            Route::middleware(['web', 'auth'])->group($routesPath);
        }
    }

    /**
     * Register plugin views
     */
    private function registerPluginViews($plugin): void
    {
        $viewsPath = $plugin->getViewsPath();
        
        if (is_dir($viewsPath)) {
            View::addNamespace("plugins.{$plugin->name}", $viewsPath);
        }
    }

    /**
     * Register plugin migrations
     */
    private function registerPluginMigrations($plugin): void
    {
        $migrationsPath = $plugin->getMigrationsPath();
        
        if (is_dir($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }
    }

    /**
     * Load plugin classes (Controllers, Models, etc.)
     */
    private function loadPluginClasses($plugin): void
    {
        // Auto-load plugin controllers
        $controllersPath = $plugin->path . '/Controllers';
        if (is_dir($controllersPath)) {
            $this->autoloadPluginClasses($controllersPath, "Plugins\\{$plugin->name}\\Controllers");
        }

        // Auto-load plugin models
        $modelsPath = $plugin->path . '/Models';
        if (is_dir($modelsPath)) {
            $this->autoloadPluginClasses($modelsPath, "Plugins\\{$plugin->name}\\Models");
        }
    }

    /**
     * Auto-load plugin classes
     */
    private function autoloadPluginClasses(string $path, string $namespace): void
    {
        $files = File::glob($path . '/*.php');
        
        foreach ($files as $file) {
            $className = pathinfo($file, PATHINFO_FILENAME);
            $fullClassName = $namespace . '\\' . $className;
            
            // Register class alias if it doesn't exist
            if (!class_exists($fullClassName)) {
                require_once $file;
            }
        }
    }
}
