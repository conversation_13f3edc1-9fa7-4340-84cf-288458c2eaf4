<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use App\Services\PluginManager;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Load plugin routes
        $this->loadPluginRoutes();

        // Share the navigation menu and enabled plugins with all views
        View::composer('*', function ($view) {
            // Share navigation menu from NavigationMenu model
            try {
                if (class_exists('\App\Models\NavigationMenu')) {
                    // Get enabled plugins
                    $pluginManager = app(\App\Services\PluginManager::class);
                    $enabledPlugins = array_keys($pluginManager->getEnabledPlugins());

                    // Simplified navigation tree generation
                    $user = auth()->user();
                    $rootItems = \App\Models\NavigationMenu::whereNull('parent_id')
                        ->where('is_active', true)
                        ->orderBy('sort_order')
                        ->get();

                    $navigationTree = [];
                    foreach ($rootItems as $item) {
                        $hasPermission = $item->userHasPermission($user);
                        $isVisible = $item->visible ?? true;

                        // Check if plugin is enabled (skip check for core items without plugin)
                        $pluginEnabled = empty($item->plugin) || in_array($item->plugin, $enabledPlugins);

                        if ($hasPermission && $isVisible && $pluginEnabled) {
                            $itemArray = $item->toArray();

                            // Get children
                            $children = \App\Models\NavigationMenu::where('parent_id', $item->id)
                                ->where('is_active', true)
                                ->orderBy('sort_order')
                                ->get();

                            $filteredChildren = [];
                            foreach ($children as $child) {
                                $childHasPermission = $child->userHasPermission($user);
                                $childIsVisible = $child->visible ?? true;
                                $childPluginEnabled = empty($child->plugin) || in_array($child->plugin, $enabledPlugins);

                                if ($childHasPermission && $childIsVisible && $childPluginEnabled) {
                                    $filteredChildren[] = $child->toArray();
                                }
                            }

                            $itemArray['filtered_children'] = $filteredChildren;
                            $navigationTree[] = $itemArray;
                        }
                    }

                    $view->with('navigationTree', $navigationTree);
                } else {
                    $view->with('navigationTree', []);
                }
            } catch (\Exception $e) {
                $view->with('navigationTree', []);
            }

            // Share enabled plugins with all views
            try {
                $pluginManager = app(PluginManager::class);
                $enabledPlugins = $pluginManager->getEnabledPlugins();
                $view->with('enabledPlugins', $enabledPlugins);
            } catch (\Exception $e) {
                $view->with('enabledPlugins', []);
            }
        });
    }

    /**
     * Load routes from enabled plugins
     */
    protected function loadPluginRoutes(): void
    {
        try {
            $pluginManager = app(PluginManager::class);
            $enabledPlugins = $pluginManager->getEnabledPlugins();

            foreach ($enabledPlugins as $plugin) {
                $routesPath = $plugin->path . '/routes.php';
                if (File::exists($routesPath)) {
                    require $routesPath;
                }
            }
        } catch (\Exception $e) {
            // Silently fail if plugin manager is not available during bootstrap
        }
    }
}
