<?php

namespace App\Services;

use App\Models\Plugin;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class PluginManager
{
    private string $pluginsPath;
    private array $plugins = [];
    private array $enabledPlugins = [];

    public function __construct()
    {
        $this->pluginsPath = base_path('plugins');
        $this->loadPlugins();
    }

    /**
     * Load all plugins from the plugins directory
     */
    public function loadPlugins(): void
    {
        $this->plugins = [];
        $this->enabledPlugins = [];

        if (!is_dir($this->pluginsPath)) {
            File::makeDirectory($this->pluginsPath, 0755, true);
            return;
        }

        $pluginDirectories = File::directories($this->pluginsPath);

        foreach ($pluginDirectories as $pluginPath) {
            $configPath = $pluginPath . '/config.json';
            
            if (File::exists($configPath)) {
                try {
                    $config = json_decode(File::get($configPath), true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $plugin = new Plugin($config, $pluginPath);
                        $this->plugins[$plugin->name] = $plugin;
                        
                        if ($plugin->enabled) {
                            $this->enabledPlugins[$plugin->name] = $plugin;
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to load plugin from {$pluginPath}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Get all plugins
     */
    public function getAllPlugins(): array
    {
        return $this->plugins;
    }

    /**
     * Get enabled plugins
     */
    public function getEnabledPlugins(): array
    {
        return $this->enabledPlugins;
    }

    /**
     * Get a specific plugin by name
     */
    public function getPlugin(string $name): ?Plugin
    {
        return $this->plugins[$name] ?? null;
    }

    /**
     * Check if a plugin exists
     */
    public function pluginExists(string $name): bool
    {
        return isset($this->plugins[$name]);
    }

    /**
     * Check if a plugin is enabled
     */
    public function isPluginEnabled(string $name): bool
    {
        return isset($this->enabledPlugins[$name]);
    }

    /**
     * Enable a plugin with dependency validation
     */
    public function enablePlugin(string $name): array
    {
        if (!$this->pluginExists($name)) {
            return ['success' => false, 'message' => "Plugin '{$name}' not found."];
        }

        $plugin = $this->getPlugin($name);

        if ($plugin->enabled) {
            return ['success' => false, 'message' => "Plugin '{$name}' is already enabled."];
        }

        // Check dependencies
        $missingDependencies = $this->getMissingDependencies($plugin);
        if (!empty($missingDependencies)) {
            return [
                'success' => false, 
                'message' => "Cannot enable '{$name}'. Missing dependencies: " . implode(', ', $missingDependencies)
            ];
        }

        // Enable the plugin
        $plugin->enabled = true;
        $this->enabledPlugins[$name] = $plugin;

        // Update config file
        $this->updatePluginConfig($plugin);

        // Run plugin migrations
        $this->runPluginMigrations($plugin);

        // Run plugin seeders
        $this->runPluginSeeders($plugin);

        return ['success' => true, 'message' => "Plugin '{$name}' enabled successfully."];
    }

    /**
     * Disable a plugin and its dependents
     */
    public function disablePlugin(string $name): array
    {
        if (!$this->pluginExists($name)) {
            return ['success' => false, 'message' => "Plugin '{$name}' not found."];
        }

        $plugin = $this->getPlugin($name);

        if (!$plugin->enabled) {
            return ['success' => false, 'message' => "Plugin '{$name}' is already disabled."];
        }

        // Check if this is a system plugin
        if ($plugin->isSystemPlugin()) {
            return ['success' => false, 'message' => "Cannot disable system plugin '{$name}'. System plugins are required for core functionality."];
        }

        // Find plugins that depend on this one
        $dependentPlugins = $this->getDependentPlugins($name);
        
        $disabledPlugins = [];
        
        // Disable dependent plugins first
        foreach ($dependentPlugins as $dependentName) {
            $dependentPlugin = $this->getPlugin($dependentName);
            if ($dependentPlugin && $dependentPlugin->enabled) {
                $dependentPlugin->enabled = false;
                unset($this->enabledPlugins[$dependentName]);
                $this->updatePluginConfig($dependentPlugin);
                $disabledPlugins[] = $dependentName;
            }
        }

        // Disable the main plugin
        $plugin->enabled = false;
        unset($this->enabledPlugins[$name]);
        $this->updatePluginConfig($plugin);

        $message = "Plugin '{$name}' disabled successfully.";
        if (!empty($disabledPlugins)) {
            $message .= " Also disabled dependent plugins: " . implode(', ', $disabledPlugins);
        }

        return ['success' => true, 'message' => $message];
    }

    /**
     * Get missing dependencies for a plugin
     */
    private function getMissingDependencies(Plugin $plugin): array
    {
        $missing = [];
        
        foreach ($plugin->dependencies as $dependency) {
            if (!$this->isPluginEnabled($dependency)) {
                $missing[] = $dependency;
            }
        }

        return $missing;
    }

    /**
     * Get plugins that depend on the given plugin
     */
    private function getDependentPlugins(string $pluginName): array
    {
        $dependents = [];

        foreach ($this->plugins as $plugin) {
            if (in_array($pluginName, $plugin->dependencies)) {
                $dependents[] = $plugin->name;
            }
        }

        return $dependents;
    }

    /**
     * Update plugin configuration file
     */
    private function updatePluginConfig(Plugin $plugin): void
    {
        $config = $plugin->config;
        $config['enabled'] = $plugin->enabled;

        $configPath = $plugin->path . '/config.json';
        File::put($configPath, json_encode($config, JSON_PRETTY_PRINT));
    }

    /**
     * Get plugin statistics
     */
    public function getStats(): array
    {
        $categories = $this->getCategorizedPlugins();

        return [
            'total' => count($this->plugins),
            'enabled' => count($this->enabledPlugins),
            'disabled' => count($this->plugins) - count($this->enabledPlugins),
            'system' => count($categories['System'] ?? []),
            'third_party' => count($categories['Third-party'] ?? []),
            'categories' => array_keys($categories),
        ];
    }

    /**
     * Get plugins organized by categories
     */
    public function getCategorizedPlugins(): array
    {
        $categorized = [];

        foreach ($this->plugins as $plugin) {
            $category = $plugin->getCategory();
            if (!isset($categorized[$category])) {
                $categorized[$category] = [];
            }
            $categorized[$category][] = $plugin;
        }

        // Sort categories: System first, then alphabetically
        uksort($categorized, function($a, $b) {
            if ($a === 'System') return -1;
            if ($b === 'System') return 1;
            return strcmp($a, $b);
        });

        return $categorized;
    }

    /**
     * Get plugins by specific category
     */
    public function getPluginsByCategory(string $category): array
    {
        return array_filter($this->plugins, function($plugin) use ($category) {
            return $plugin->getCategory() === $category;
        });
    }

    /**
     * Validate plugin dependencies (check for circular dependencies)
     */
    public function validateDependencies(): array
    {
        $errors = [];

        foreach ($this->plugins as $plugin) {
            // Check for circular dependencies
            if ($this->hasCircularDependency($plugin->name, [])) {
                $errors[] = "Circular dependency detected for plugin: {$plugin->name}";
            }

            // Check for missing dependencies
            foreach ($plugin->dependencies as $dependency) {
                if (!$this->pluginExists($dependency)) {
                    $errors[] = "Plugin '{$plugin->name}' depends on missing plugin: {$dependency}";
                }
            }

            // Check for invalid plugin structure
            if (!$plugin->isValid()) {
                $errors[] = "Plugin '{$plugin->name}' has invalid structure (missing required files)";
            }
        }

        return $errors;
    }

    /**
     * Check for circular dependencies
     */
    private function hasCircularDependency(string $pluginName, array $visited): bool
    {
        if (in_array($pluginName, $visited)) {
            return true;
        }

        $plugin = $this->getPlugin($pluginName);
        if (!$plugin) {
            return false;
        }

        $visited[] = $pluginName;

        foreach ($plugin->dependencies as $dependency) {
            if ($this->hasCircularDependency($dependency, $visited)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate a specific plugin thoroughly
     */
    public function validatePlugin(string $name): array
    {
        $errors = [];
        $warnings = [];

        if (!$this->pluginExists($name)) {
            $errors[] = "Plugin '{$name}' does not exist";
            return ['errors' => $errors, 'warnings' => $warnings];
        }

        $plugin = $this->getPlugin($name);

        // Check basic structure
        if (!$plugin->isValid()) {
            $errors[] = "Plugin is missing required files (config.json or routes.php)";
        }

        // Check dependencies
        foreach ($plugin->dependencies as $dependency) {
            if (!$this->pluginExists($dependency)) {
                $errors[] = "Missing dependency: {$dependency}";
            } elseif (!$this->isPluginEnabled($dependency)) {
                $warnings[] = "Dependency '{$dependency}' is not enabled";
            }
        }

        // Check for circular dependencies
        if ($this->hasCircularDependency($name, [])) {
            $errors[] = "Circular dependency detected";
        }

        // Check file permissions
        if (!is_readable($plugin->path)) {
            $errors[] = "Plugin directory is not readable";
        }

        // Check config.json validity
        $configPath = $plugin->path . '/config.json';
        if (file_exists($configPath)) {
            $configContent = file_get_contents($configPath);
            json_decode($configContent);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = "Invalid JSON in config.json: " . json_last_error_msg();
            }
        }

        // Check routes.php syntax
        $routesPath = $plugin->getRoutesPath();
        if (file_exists($routesPath)) {
            $routesContent = file_get_contents($routesPath);
            if (strpos($routesContent, '<?php') !== 0) {
                $warnings[] = "routes.php should start with <?php tag";
            }
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Get dependency tree for a plugin
     */
    public function getDependencyTree(string $name): array
    {
        if (!$this->pluginExists($name)) {
            return [];
        }

        $tree = [];
        $plugin = $this->getPlugin($name);

        foreach ($plugin->dependencies as $dependency) {
            $tree[$dependency] = $this->getDependencyTree($dependency);
        }

        return $tree;
    }

    /**
     * Get plugins that would be affected by disabling a plugin
     */
    public function getAffectedPlugins(string $name): array
    {
        $affected = [];
        $dependents = $this->getDependentPlugins($name);

        foreach ($dependents as $dependent) {
            if ($this->isPluginEnabled($dependent)) {
                $affected[] = $dependent;
                // Recursively get dependents of dependents
                $affected = array_merge($affected, $this->getAffectedPlugins($dependent));
            }
        }

        return array_unique($affected);
    }

    /**
     * Check if enabling a plugin would break any dependencies
     */
    public function canEnablePlugin(string $name): array
    {
        $result = ['can_enable' => true, 'issues' => []];

        if (!$this->pluginExists($name)) {
            $result['can_enable'] = false;
            $result['issues'][] = "Plugin does not exist";
            return $result;
        }

        $plugin = $this->getPlugin($name);

        if (!$plugin->isValid()) {
            $result['can_enable'] = false;
            $result['issues'][] = "Plugin has invalid structure";
        }

        $missingDeps = $this->getMissingDependencies($plugin);
        if (!empty($missingDeps)) {
            $result['can_enable'] = false;
            $result['issues'][] = "Missing dependencies: " . implode(', ', $missingDeps);
        }

        if ($this->hasCircularDependency($name, [])) {
            $result['can_enable'] = false;
            $result['issues'][] = "Would create circular dependency";
        }

        return $result;
    }

    /**
     * Run plugin migrations
     */
    private function runPluginMigrations(Plugin $plugin): void
    {
        if ($plugin->hasMigrations()) {
            try {
                Artisan::call('migrate', [
                    '--path' => 'plugins/' . $plugin->name . '/Migrations',
                    '--force' => true
                ]);
                Log::info("Ran migrations for plugin: {$plugin->name}");
            } catch (\Exception $e) {
                Log::error("Failed to run migrations for plugin {$plugin->name}: " . $e->getMessage());
            }
        }
    }

    /**
     * Run plugin seeders
     */
    private function runPluginSeeders(Plugin $plugin): void
    {
        if ($plugin->hasSeeds()) {
            $seedsPath = $plugin->getSeedsPath();
            $seedFiles = glob($seedsPath . '/*.php');

            foreach ($seedFiles as $seedFile) {
                $className = pathinfo($seedFile, PATHINFO_FILENAME);
                $fullClassName = "Plugins\\{$plugin->name}\\Seeds\\{$className}";

                try {
                    // Check if class doesn't exist before requiring the file
                    if (!class_exists($fullClassName)) {
                        require_once $seedFile;
                    }

                    // Check if class exists and run it
                    if (class_exists($fullClassName)) {
                        Artisan::call('db:seed', [
                            '--class' => $fullClassName,
                            '--force' => true
                        ]);
                        Log::info("Ran seeder {$fullClassName} for plugin: {$plugin->name}");
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to run seeder {$fullClassName} for plugin {$plugin->name}: " . $e->getMessage());
                }
            }
        }
    }
}
