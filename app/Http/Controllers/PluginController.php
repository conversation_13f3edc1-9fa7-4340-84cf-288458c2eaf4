<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use App\Traits\DatabaseCompatibility;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class PluginController extends Controller
{
    use DatabaseCompatibility;

    private PluginManager $pluginManager;

    public function __construct(PluginManager $pluginManager)
    {
        $this->pluginManager = $pluginManager;

        // Restrict access to users with manage_plugins permission
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_plugins')) {
                abort(403, 'Access denied. You do not have permission to manage plugins.');
            }

            return $next($request);
        });
    }

    /**
     * Display the plugin manager interface
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getAllPlugins();
        $categorizedPlugins = $this->pluginManager->getCategorizedPlugins();
        $stats = $this->pluginManager->getStats();
        $dependencyErrors = $this->pluginManager->validateDependencies();

        return view('plugins.index', compact('plugins', 'categorizedPlugins', 'stats', 'dependencyErrors'));
    }

    /**
     * Enable a plugin
     */
    public function enable(Request $request, string $name): RedirectResponse
    {
        $result = $this->pluginManager->enablePlugin($name);

        if ($result['success']) {
            return redirect()->route('plugins.index')->with('success', $result['message']);
        }

        return redirect()->route('plugins.index')->with('error', $result['message']);
    }

    /**
     * Disable a plugin
     */
    public function disable(Request $request, string $name): RedirectResponse
    {
        $result = $this->pluginManager->disablePlugin($name);

        if ($result['success']) {
            return redirect()->route('plugins.index')->with('success', $result['message']);
        }

        return redirect()->route('plugins.index')->with('error', $result['message']);
    }

    /**
     * Show plugin details
     */
    public function show(string $name): View
    {
        $plugin = $this->pluginManager->getPlugin($name);

        if (!$plugin) {
            abort(404, 'Plugin not found');
        }

        // Get plugin statistics and data management info
        $stats = $this->getPluginStats($name);
        $hasSeeder = !is_null($this->getPluginSeederClass($name));
        $seederClass = $this->getPluginSeederClass($name);
        $tables = $this->getPluginTables($name);
        $totalDataCount = array_sum($stats);

        return view('plugins.show', compact('plugin', 'stats', 'hasSeeder', 'seederClass', 'tables', 'totalDataCount'));
    }

    /**
     * Refresh plugin list
     */
    public function refresh(): RedirectResponse
    {
        $this->pluginManager->loadPlugins();
        
        return redirect()->route('plugins.index')->with('success', 'Plugin list refreshed successfully.');
    }

    /**
     * Get plugin data as JSON (for API)
     */
    public function api(): \Illuminate\Http\JsonResponse
    {
        $plugins = [];
        foreach ($this->pluginManager->getAllPlugins() as $plugin) {
            $plugins[] = $plugin->toArray();
        }

        return response()->json([
            'plugins' => $plugins,
            'stats' => $this->pluginManager->getStats(),
            'dependency_errors' => $this->pluginManager->validateDependencies(),
        ]);
    }

    /**
     * Validate a specific plugin
     */
    public function validatePlugin(string $name): \Illuminate\Http\JsonResponse
    {
        $validation = $this->pluginManager->validatePlugin($name);
        $canEnable = $this->pluginManager->canEnablePlugin($name);
        $dependencyTree = $this->pluginManager->getDependencyTree($name);
        $affectedPlugins = $this->pluginManager->getAffectedPlugins($name);

        return response()->json([
            'plugin' => $name,
            'validation' => $validation,
            'can_enable' => $canEnable,
            'dependency_tree' => $dependencyTree,
            'affected_plugins' => $affectedPlugins,
        ]);
    }

    /**
     * Uninstall a plugin completely
     */
    public function uninstall(Request $request, string $name): RedirectResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return redirect()->route('plugins.index')->with('error', 'Plugin not found.');
            }

            // Prevent uninstalling system plugins
            if ($plugin->isSystemPlugin()) {
                return redirect()->route('plugins.index')->with('error', 'Cannot uninstall system plugins.');
            }

            // Check if plugin is enabled and disable it first
            if ($plugin->enabled) {
                $disableResult = $this->pluginManager->disablePlugin($name);
                if (!$disableResult['success']) {
                    return redirect()->route('plugins.index')->with('error', 'Failed to disable plugin before uninstall: ' . $disableResult['message']);
                }
            }

            // Remove plugin permissions
            $this->removePluginPermissions($name);

            // Clear plugin data if requested
            if ($request->boolean('clear_data', false)) {
                $tables = $this->getPluginTables($name);
                if (!empty($tables)) {
                    $this->safeDeleteFromTables($tables);
                }
            }

            // Remove plugin files
            $this->removePluginFiles($plugin->path);

            Log::info("Plugin '{$name}' uninstalled successfully", [
                'user' => auth()->user()->email,
                'clear_data' => $request->boolean('clear_data', false),
            ]);

            return redirect()->route('plugins.index')->with('success', "Plugin '{$name}' has been uninstalled successfully.");
        } catch (\Exception $e) {
            Log::error("Failed to uninstall plugin '{$name}': " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to uninstall plugin: ' . $e->getMessage());
        }
    }

    /**
     * Export a plugin as a downloadable archive
     */
    public function export(string $name)
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return redirect()->route('plugins.index')->with('error', 'Plugin not found.');
            }

            $zipPath = $this->createPluginArchive($plugin);

            Log::info("Plugin '{$name}' exported successfully", [
                'user' => auth()->user()->email,
                'file_size' => filesize($zipPath),
            ]);

            return response()->download($zipPath, "{$name}-plugin-v{$plugin->version}.zip")->deleteFileAfterSend();
        } catch (\Exception $e) {
            Log::error("Failed to export plugin '{$name}': " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to export plugin: ' . $e->getMessage());
        }
    }

    /**
     * Show the install plugin form
     */
    public function showInstall(): View
    {
        return view('plugins.install');
    }

    /**
     * Install a new plugin from uploaded archive
     */
    public function install(Request $request): RedirectResponse
    {
        $request->validate([
            'plugin_file' => 'required|file|mimes:zip|max:51200', // 50MB max
        ]);

        try {
            $uploadedFile = $request->file('plugin_file');
            $tempPath = $this->extractPluginArchive($uploadedFile);

            // Validate plugin structure
            $configPath = $tempPath . '/config.json';
            if (!file_exists($configPath)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: config.json not found.');
            }

            $config = json_decode(file_get_contents($configPath), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: config.json is malformed.');
            }

            $pluginName = $config['name'] ?? null;
            if (!$pluginName) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: plugin name not specified.');
            }

            // Check if plugin already exists
            if ($this->pluginManager->pluginExists($pluginName)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', "Plugin '{$pluginName}' already exists.");
            }

            // Move plugin to plugins directory
            $pluginPath = base_path("plugins/{$pluginName}");
            if (!rename($tempPath, $pluginPath)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Failed to install plugin files.');
            }

            // Reload plugins to include the new one
            $this->pluginManager->loadPlugins();

            Log::info("Plugin '{$pluginName}' installed successfully", [
                'user' => auth()->user()->email,
                'version' => $config['version'] ?? 'unknown',
            ]);

            return redirect()->route('plugins.show', $pluginName)->with('success', "Plugin '{$pluginName}' has been installed successfully.");
        } catch (\Exception $e) {
            Log::error("Failed to install plugin: " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to install plugin: ' . $e->getMessage());
        }
    }

    /**
     * Check plugin dependencies
     */
    public function checkDependencies(): \Illuminate\Http\JsonResponse
    {
        $errors = $this->pluginManager->validateDependencies();

        return response()->json([
            'has_errors' => !empty($errors),
            'errors' => $errors,
            'total_errors' => count($errors),
        ]);
    }

    /**
     * Seed sample data for a plugin
     */
    public function seedSampleData(Request $request, string $name): JsonResponse
    {
        try {
            $seederClass = $this->getPluginSeederClass($name);

            if (!$seederClass) {
                return response()->json([
                    'success' => false,
                    'message' => 'No sample data seeder found for this plugin.'
                ], 404);
            }

            // Run the sample data seeder
            Artisan::call('db:seed', [
                '--class' => $seederClass,
                '--force' => true
            ]);

            Log::info("Sample data seeded for plugin '{$name}' by user: " . auth()->user()->email);

            return response()->json([
                'success' => true,
                'message' => 'Sample data seeded successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to seed sample data for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to seed sample data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all data for a plugin
     */
    public function clearAllData(Request $request, string $name): JsonResponse
    {
        try {
            $tables = $this->getPluginTables($name);

            if (empty($tables)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No tables defined for this plugin.'
                ], 404);
            }

            // Clear all plugin data in transaction with database-agnostic foreign key handling
            DB::transaction(function () use ($tables) {
                $this->safeDeleteFromTables($tables);
            });

            Log::info("All data cleared for plugin '{$name}' by user: " . auth()->user()->email);

            return response()->json([
                'success' => true,
                'message' => 'All plugin data cleared successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to clear plugin data for '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get plugin statistics
     */
    public function getPluginStats(string $name): array
    {
        switch (strtolower($name)) {
            case 'products':
                return [
                    'products' => $this->safeTableCount('products'),
                    'pricing_items' => $this->safeTableCount('product_pricing_items'),
                    'releases' => $this->safeTableCount('product_releases'),
                    'documents' => $this->safeTableCount('product_documents'),
                ];

            case 'business':
                return [
                    'businesses' => $this->safeTableCount('businesses'),
                    'tags' => $this->safeTableCount('tags'),
                    'business_users' => $this->safeTableCount('business_users'),
                    'business_products' => $this->safeTableCount('business_products'),
                ];

            case 'announcements':
                return [
                    'announcements' => $this->safeTableCount('announcements'),
                    'announcement_reads' => $this->safeTableCount('announcement_reads'),
                ];

            case 'settings':
                return [
                    'backups' => $this->getBackupCount(),
                    'system_logs' => $this->getLogCount(),
                ];

            case 'todo':
                return [
                    'todos' => $this->safeTableCount('todos'),
                ];

            default:
                return ['data_count' => 0];
        }
    }

    /**
     * Get plugin seeder class
     */
    private function getPluginSeederClass(string $pluginName): ?string
    {
        $possibleSeeders = [
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}SampleDataSeeder",
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}PluginSeeder",
            "Plugins\\{$pluginName}\\Seeds\\SampleDataSeeder",
        ];

        foreach ($possibleSeeders as $seederClass) {
            if (class_exists($seederClass)) {
                return $seederClass;
            }
        }

        return null;
    }

    /**
     * Get plugin tables
     */
    private function getPluginTables(string $pluginName): array
    {
        switch (strtolower($pluginName)) {
            case 'products':
                return [
                    'product_pricing_items',
                    'product_releases',
                    'product_documents',
                    'products'
                ];

            case 'business':
                return [
                    'business_activities',
                    'business_products',
                    'business_tags',
                    'business_users',
                    'businesses',
                    'tags'
                ];

            case 'announcements':
                return [
                    'announcement_reads',
                    'announcements'
                ];

            case 'settings':
                return [
                    // Settings plugin doesn't have database tables to clear
                    // It manages system backups and configuration
                ];

            case 'todo':
                return [
                    'todos'
                ];

            default:
                return [];
        }
    }

    /**
     * Get backup count for settings plugin
     */
    private function getBackupCount(): int
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return 0;
            }
            return count(glob($backupPath . '/*.zip'));
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get log count for settings plugin
     */
    private function getLogCount(): int
    {
        try {
            $logPath = storage_path('logs');
            if (!is_dir($logPath)) {
                return 0;
            }
            return count(glob($logPath . '/*.log'));
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Remove plugin permissions from database
     */
    private function removePluginPermissions(string $pluginName): void
    {
        try {
            $permissions = \App\Models\Permission::where('plugin', $pluginName)->get();

            foreach ($permissions as $permission) {
                // Detach from all roles first
                $permission->roles()->detach();
                // Delete the permission
                $permission->delete();
            }

            Log::info("Removed {$permissions->count()} permissions for plugin '{$pluginName}'");
        } catch (\Exception $e) {
            Log::error("Failed to remove permissions for plugin '{$pluginName}': " . $e->getMessage());
        }
    }

    /**
     * Remove plugin files and directories
     */
    private function removePluginFiles(string $pluginPath): void
    {
        if (is_dir($pluginPath)) {
            $this->deleteDirectory($pluginPath);
        }
    }

    /**
     * Recursively delete a directory and its contents
     */
    private function deleteDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;

            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Create a ZIP archive of a plugin
     */
    private function createPluginArchive(\App\Models\Plugin $plugin): string
    {
        $zip = new \ZipArchive();
        $zipPath = storage_path("app/temp/{$plugin->name}-plugin-v{$plugin->version}.zip");

        // Ensure temp directory exists
        $tempDir = dirname($zipPath);
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception('Cannot create ZIP archive');
        }

        $this->addDirectoryToZip($zip, $plugin->path, $plugin->name);
        $zip->close();

        return $zipPath;
    }

    /**
     * Add directory contents to ZIP archive
     */
    private function addDirectoryToZip(\ZipArchive $zip, string $sourcePath, string $baseName): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($sourcePath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $baseName . '/' . substr($filePath, strlen($sourcePath) + 1);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } else {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Extract plugin archive to temporary directory
     */
    private function extractPluginArchive(\Illuminate\Http\UploadedFile $file): string
    {
        $tempDir = storage_path('app/temp/plugin-install-' . uniqid());

        if (!mkdir($tempDir, 0755, true)) {
            throw new \Exception('Cannot create temporary directory');
        }

        $zip = new \ZipArchive();
        if ($zip->open($file->getRealPath()) !== TRUE) {
            $this->cleanupTempDirectory($tempDir);
            throw new \Exception('Cannot open ZIP archive');
        }

        // Extract to temp directory
        $zip->extractTo($tempDir);
        $zip->close();

        // Find the plugin directory (should be the only directory in the archive)
        $contents = scandir($tempDir);
        $pluginDir = null;

        foreach ($contents as $item) {
            if ($item !== '.' && $item !== '..' && is_dir($tempDir . '/' . $item)) {
                $pluginDir = $tempDir . '/' . $item;
                break;
            }
        }

        if (!$pluginDir) {
            $this->cleanupTempDirectory($tempDir);
            throw new \Exception('Invalid plugin archive structure');
        }

        return $pluginDir;
    }

    /**
     * Clean up temporary directory
     */
    private function cleanupTempDirectory(string $dir): void
    {
        if (is_dir($dir)) {
            $this->deleteDirectory($dir);
        }
    }

    /**
     * Sync plugin permissions to database
     */
    public function syncPermissions(string $name): JsonResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' not found."
                ], 404);
            }

            if (empty($plugin->permissions)) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' has no permissions defined in config.json."
                ], 400);
            }

            $createdCount = 0;
            $updatedCount = 0;
            $assignedCount = 0;

            // Get admin role
            $adminRole = \App\Models\Role::where('name', 'admin')->first();

            foreach ($plugin->permissions as $permissionName) {
                // Generate display name and description
                $displayName = $this->generateDisplayName($permissionName);
                $description = $this->generateDescription($permissionName, $plugin->name);
                $category = $this->generateCategory($plugin->name);

                // Create or update permission
                $permission = \App\Models\Permission::updateOrCreate(
                    ['name' => $permissionName],
                    [
                        'display_name' => $displayName,
                        'description' => $description,
                        'category' => $category,
                        'plugin' => $plugin->name,
                    ]
                );

                if ($permission->wasRecentlyCreated) {
                    $createdCount++;
                } else {
                    $updatedCount++;
                }

                // Assign to admin role if exists and not already assigned
                if ($adminRole && !$adminRole->permissions->contains($permission)) {
                    $adminRole->permissions()->attach($permission);
                    $assignedCount++;
                }
            }

            $message = "Permissions synced successfully! ";
            $details = [];

            if ($createdCount > 0) {
                $details[] = "Created {$createdCount} new permission(s)";
            }
            if ($updatedCount > 0) {
                $details[] = "Updated {$updatedCount} existing permission(s)";
            }
            if ($assignedCount > 0) {
                $details[] = "Assigned {$assignedCount} permission(s) to admin role";
            }

            if (!empty($details)) {
                $message .= implode(', ', $details) . '.';
            }

            Log::info("Permissions synced for plugin '{$name}'", [
                'plugin' => $name,
                'created' => $createdCount,
                'updated' => $updatedCount,
                'assigned' => $assignedCount,
                'user' => auth()->user()->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'stats' => [
                    'created' => $createdCount,
                    'updated' => $updatedCount,
                    'assigned' => $assignedCount
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to sync permissions for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync permissions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate display name from permission name
     */
    private function generateDisplayName(string $permissionName): string
    {
        // Convert snake_case to Title Case
        return ucwords(str_replace('_', ' ', $permissionName));
    }

    /**
     * Generate description from permission name and plugin
     */
    private function generateDescription(string $permissionName, string $pluginName): string
    {
        $action = '';
        $resource = ucfirst($pluginName);

        if (str_contains($permissionName, 'manage')) {
            $action = 'Create, edit, delete, and manage';
        } elseif (str_contains($permissionName, 'view')) {
            $action = 'View and access';
        } elseif (str_contains($permissionName, 'create')) {
            $action = 'Create new';
        } elseif (str_contains($permissionName, 'edit')) {
            $action = 'Edit and update';
        } elseif (str_contains($permissionName, 'delete')) {
            $action = 'Delete and remove';
        } else {
            $action = 'Access';
        }

        return "{$action} {$resource} functionality";
    }

    /**
     * Generate category from plugin name
     */
    private function generateCategory(string $pluginName): string
    {
        return ucfirst($pluginName) . ' Management';
    }

    /**
     * Sync plugin navigation to database
     */
    public function syncNavigation(string $name): JsonResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' not found."
                ], 404);
            }

            $navConfig = $plugin->config['navigation'] ?? null;

            if (!$navConfig || empty($navConfig)) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' has no navigation configuration defined in config.json."
                ], 400);
            }

            // Validate required navigation fields
            if (empty($navConfig['label'])) {
                return response()->json([
                    'success' => false,
                    'message' => "Navigation configuration is missing required 'label' field."
                ], 400);
            }

            $createdCount = 0;
            $updatedCount = 0;
            $createdItems = [];

            // Create/update main navigation item
            $mainMenuName = $plugin->name . '-main';

            $mainNavigationData = [
                'name' => $mainMenuName,
                'label' => $navConfig['label'],
                'icon' => $navConfig['icon'] ?? 'fas fa-circle',
                'route' => !empty($navConfig['route']) ? $navConfig['route'] : null,
                'url' => $navConfig['url'] ?? null,
                'permissions' => $navConfig['permissions'] ?? [],
                'plugin' => $plugin->name,
                'parent_id' => null,
                'is_system' => $plugin->isSystemPlugin(),
                'is_active' => true,
                'visible' => true,
                'target' => $navConfig['target'] ?? '_self',
                'type' => $navConfig['type'] ?? (empty($navConfig['route']) ? 'parent' : 'link'),
            ];

            // Find existing main navigation item
            $existingMainItem = \App\Models\NavigationMenu::where('plugin', $plugin->name)
                ->where(function($query) use ($mainMenuName, $navConfig) {
                    $query->where('name', $mainMenuName)
                          ->orWhere('label', $navConfig['label']);
                })
                ->whereNull('parent_id')
                ->first();

            if ($existingMainItem) {
                // Update existing main navigation item
                $existingMainItem->update($mainNavigationData);
                $updatedCount++;
                $mainNavigationItem = $existingMainItem;
            } else {
                // Create new main navigation item
                $mainNavigationData['sort_order'] = \App\Models\NavigationMenu::whereNull('parent_id')->max('sort_order') + 1;
                $mainNavigationItem = \App\Models\NavigationMenu::create($mainNavigationData);
                $createdCount++;
            }

            $createdItems[] = [
                'id' => $mainNavigationItem->id,
                'name' => $mainNavigationItem->name,
                'label' => $mainNavigationItem->label,
                'route' => $mainNavigationItem->route,
                'parent_id' => null,
                'type' => 'main'
            ];

            // Handle subnav items if they exist
            if (isset($navConfig['subnav']) && !empty($navConfig['subnav'])) {
                foreach ($navConfig['subnav'] as $index => $subItem) {
                    // Validate required sub-navigation fields
                    if (empty($subItem['label'])) {
                        return response()->json([
                            'success' => false,
                            'message' => "Sub-navigation item " . ($index + 1) . " is missing required 'label' field."
                        ], 400);
                    }

                    // Generate sub-navigation menu name
                    $subMenuName = $plugin->name . '-' . \Illuminate\Support\Str::slug($subItem['label']);

                    // Prepare sub-navigation data
                    $subNavigationData = [
                        'name' => $subMenuName,
                        'label' => $subItem['label'],
                        'icon' => $subItem['icon'] ?? 'fas fa-circle',
                        'route' => $subItem['route'] ?? null,
                        'url' => $subItem['url'] ?? null,
                        'permissions' => $subItem['permissions'] ?? [],
                        'plugin' => $plugin->name,
                        'parent_id' => $mainNavigationItem->id,
                        'is_system' => $plugin->isSystemPlugin(),
                        'is_active' => true,
                        'visible' => true,
                        'target' => $subItem['target'] ?? '_self',
                        'type' => $subItem['type'] ?? 'link',
                    ];

                    // Find existing sub-navigation item
                    $existingSubItem = \App\Models\NavigationMenu::where('plugin', $plugin->name)
                        ->where('parent_id', $mainNavigationItem->id)
                        ->where(function($query) use ($subMenuName, $subItem) {
                            $query->where('name', $subMenuName)
                                  ->orWhere('route', $subItem['route'] ?? '')
                                  ->orWhere('label', $subItem['label']);
                        })
                        ->first();

                    if ($existingSubItem) {
                        // Update existing sub-navigation item
                        $existingSubItem->update($subNavigationData);
                        $updatedCount++;
                        $subNavigationItem = $existingSubItem;
                    } else {
                        // Create new sub-navigation item
                        $subNavigationData['sort_order'] = \App\Models\NavigationMenu::where('parent_id', $mainNavigationItem->id)->max('sort_order') + 1;
                        $subNavigationItem = \App\Models\NavigationMenu::create($subNavigationData);
                        $createdCount++;
                    }

                    $createdItems[] = [
                        'id' => $subNavigationItem->id,
                        'name' => $subNavigationItem->name,
                        'label' => $subNavigationItem->label,
                        'route' => $subNavigationItem->route,
                        'parent_id' => $subNavigationItem->parent_id,
                        'type' => 'sub'
                    ];
                }
            }

            Log::info("Navigation synced for plugin '{$name}'", [
                'plugin' => $name,
                'created' => $createdCount,
                'updated' => $updatedCount,
                'total_items' => count($createdItems),
                'has_subnav' => isset($navConfig['subnav']),
                'subnav_count' => isset($navConfig['subnav']) ? count($navConfig['subnav']) : 0,
                'user' => auth()->user()->email ?? 'unknown'
            ]);

            $message = "Navigation menu synced successfully! ";
            $details = [];

            if ($createdCount > 0) {
                $details[] = "Created {$createdCount} new menu item(s)";
            }
            if ($updatedCount > 0) {
                $details[] = "Updated {$updatedCount} existing menu item(s)";
            }

            if (!empty($details)) {
                $message .= implode(', ', $details) . '.';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'stats' => [
                    'created' => $createdCount,
                    'updated' => $updatedCount,
                    'total' => count($createdItems),
                    'has_subnav' => isset($navConfig['subnav']),
                    'subnav_count' => isset($navConfig['subnav']) ? count($navConfig['subnav']) : 0
                ],
                'navigation_items' => $createdItems
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to sync navigation for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync navigation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Run plugin migrations
     */
    public function runMigrations(string $name): JsonResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' not found."
                ], 404);
            }

            $migrationPath = $plugin->path . '/Migrations';

            if (!is_dir($migrationPath)) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' has no migrations directory."
                ], 400);
            }

            $migrationFiles = glob($migrationPath . '/*.php');

            if (empty($migrationFiles)) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' has no migration files."
                ], 400);
            }

            // Run migrations using Artisan command
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate', [
                '--path' => 'plugins/' . $name . '/Migrations',
                '--force' => true
            ]);

            $output = \Illuminate\Support\Facades\Artisan::output();

            if ($exitCode === 0) {
                // Count how many migrations were actually run
                $pendingCount = 0;
                foreach ($migrationFiles as $file) {
                    $migrationName = pathinfo(basename($file), PATHINFO_FILENAME);
                    if (!\Illuminate\Support\Facades\DB::table('migrations')->where('migration', $migrationName)->exists()) {
                        $pendingCount++;
                    }
                }

                $message = "Migrations executed successfully! ";
                if ($pendingCount === 0) {
                    $message .= "All migrations were already up to date.";
                } else {
                    $message .= "Processed " . count($migrationFiles) . " migration file(s).";
                }

                Log::info("Migrations run for plugin '{$name}'", [
                    'plugin' => $name,
                    'migration_files' => count($migrationFiles),
                    'exit_code' => $exitCode,
                    'user' => auth()->user()->email ?? 'unknown'
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'output' => $output,
                    'migration_count' => count($migrationFiles)
                ]);
            } else {
                Log::error("Migration failed for plugin '{$name}'", [
                    'plugin' => $name,
                    'exit_code' => $exitCode,
                    'output' => $output
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Migration failed. Check the logs for details.',
                    'output' => $output
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error("Failed to run migrations for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to run migrations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rollback plugin migrations
     */
    public function rollbackMigrations(string $name): JsonResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' not found."
                ], 404);
            }

            $migrationPath = $plugin->path . '/Migrations';

            if (!is_dir($migrationPath)) {
                return response()->json([
                    'success' => false,
                    'message' => "Plugin '{$name}' has no migrations directory."
                ], 400);
            }

            // Check if there are any migrations to rollback
            $migrationFiles = glob($migrationPath . '/*.php');
            $migratedCount = 0;

            foreach ($migrationFiles as $file) {
                $migrationName = pathinfo(basename($file), PATHINFO_FILENAME);
                if (\Illuminate\Support\Facades\DB::table('migrations')->where('migration', $migrationName)->exists()) {
                    $migratedCount++;
                }
            }

            if ($migratedCount === 0) {
                return response()->json([
                    'success' => false,
                    'message' => "No migrations to rollback for plugin '{$name}'."
                ], 400);
            }

            // Run rollback using Artisan command
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:rollback', [
                '--path' => 'plugins/' . $name . '/Migrations',
                '--force' => true
            ]);

            $output = \Illuminate\Support\Facades\Artisan::output();

            if ($exitCode === 0) {
                Log::info("Migrations rolled back for plugin '{$name}'", [
                    'plugin' => $name,
                    'exit_code' => $exitCode,
                    'user' => auth()->user()->email ?? 'unknown'
                ]);

                return response()->json([
                    'success' => true,
                    'message' => "Migrations rolled back successfully for plugin '{$name}'.",
                    'output' => $output
                ]);
            } else {
                Log::error("Migration rollback failed for plugin '{$name}'", [
                    'plugin' => $name,
                    'exit_code' => $exitCode,
                    'output' => $output
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Migration rollback failed. Check the logs for details.',
                    'output' => $output
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error("Failed to rollback migrations for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to rollback migrations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Safely count records in a table, returning 0 if table doesn't exist
     */
    private function safeTableCount(string $tableName): int
    {
        try {
            return DB::table($tableName)->count();
        } catch (\Exception $e) {
            // Table doesn't exist or other database error
            return 0;
        }
    }
}
