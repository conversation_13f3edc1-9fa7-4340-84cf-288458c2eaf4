<?php

namespace App\Http\Controllers;

use App\Models\UserPreference;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserPreferenceController extends Controller
{
    /**
     * Get user preferences
     */
    public function show(): JsonResponse
    {
        $preferences = auth()->user()->getPreferences();
        
        return response()->json([
            'success' => true,
            'preferences' => $preferences
        ]);
    }

    /**
     * Update user preferences
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'theme' => 'sometimes|in:light,dark,system',
            'language' => 'sometimes|string|max:10',
            'sidebar_collapsed' => 'sometimes|boolean',
            'sidebar_position' => 'sometimes|in:left,right',
            'email_notifications' => 'sometimes|boolean',
            'browser_notifications' => 'sometimes|boolean',
            'sound_notifications' => 'sometimes|boolean',
            'date_format' => 'sometimes|in:Y-m-d,m/d/Y,d/m/Y,d-m-Y',
            'time_format' => 'sometimes|in:24,12',
            'timezone' => 'sometimes|string|max:50',
        ]);

        $preferences = auth()->user()->getPreferences();
        $preferences->update($request->only([
            'theme',
            'language',
            'sidebar_collapsed',
            'sidebar_position',
            'email_notifications',
            'browser_notifications',
            'sound_notifications',
            'date_format',
            'time_format',
            'timezone',
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Preferences updated successfully.',
            'preferences' => $preferences->fresh()
        ]);
    }

    /**
     * Update a specific preference
     */
    public function updatePreference(Request $request): JsonResponse
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $preferences = auth()->user()->getPreferences();
        $success = $preferences->updatePreference($request->key, $request->value);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Preference updated successfully.',
                'preferences' => $preferences->fresh()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to update preference.'
        ], 400);
    }

    /**
     * Toggle dark mode
     */
    public function toggleDarkMode(): JsonResponse
    {
        try {
            $preferences = auth()->user()->getPreferences();
            $newTheme = $preferences->theme === 'dark' ? 'light' : 'dark';

            // Try to update the preferences
            if ($preferences->exists) {
                $preferences->update(['theme' => $newTheme]);
            } else {
                // If preferences don't exist in database, store in session
                session(['user_theme' => $newTheme]);
            }

            return response()->json([
                'success' => true,
                'theme' => $newTheme,
                'isDarkMode' => $newTheme === 'dark',
                'message' => 'Theme updated successfully.'
            ]);
        } catch (\Exception $e) {
            // Fallback to session storage if database is not available
            $currentTheme = session('user_theme', 'light');
            $newTheme = $currentTheme === 'dark' ? 'light' : 'dark';
            session(['user_theme' => $newTheme]);

            return response()->json([
                'success' => true,
                'theme' => $newTheme,
                'isDarkMode' => $newTheme === 'dark',
                'message' => 'Theme updated successfully (stored in session).'
            ]);
        }
    }

    /**
     * Reset preferences to defaults
     */
    public function reset(): JsonResponse
    {
        $preferences = auth()->user()->getPreferences();
        $defaults = UserPreference::getDefaults();
        
        $preferences->update($defaults);

        return response()->json([
            'success' => true,
            'message' => 'Preferences reset to defaults.',
            'preferences' => $preferences->fresh()
        ]);
    }
}
