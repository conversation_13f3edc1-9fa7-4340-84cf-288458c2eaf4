#!/bin/bash

echo "=== Clearing Cache and Restarting Services ==="
echo ""

echo "1. Clearing Laravel caches..."

# Clear Laravel caches
if [ -f "artisan" ]; then
    echo "   Clearing application cache..."
    php artisan cache:clear 2>/dev/null || echo "   Cache clear failed (this is normal if app has errors)"
    
    echo "   Clearing configuration cache..."
    php artisan config:clear 2>/dev/null || echo "   Config clear failed (this is normal if app has errors)"
    
    echo "   Clearing route cache..."
    php artisan route:clear 2>/dev/null || echo "   Route clear failed (this is normal if app has errors)"
    
    echo "   Clearing view cache..."
    php artisan view:clear 2>/dev/null || echo "   View clear failed (this is normal if app has errors)"
    
    echo "   Clearing compiled views..."
    rm -rf storage/framework/views/*.php 2>/dev/null || echo "   No compiled views to clear"
    
    echo "   Clearing compiled services..."
    rm -f bootstrap/cache/services.php 2>/dev/null || echo "   No services cache to clear"
    
    echo "   Clearing compiled packages..."
    rm -f bootstrap/cache/packages.php 2>/dev/null || echo "   No packages cache to clear"
else
    echo "   ⚠ artisan not found, skipping Laravel cache clearing"
fi

echo ""
echo "2. Clearing PHP OPcache..."
# Try to clear OPcache
php -r "if (function_exists('opcache_reset')) { opcache_reset(); echo '   ✓ OPcache cleared'; } else { echo '   ⚠ OPcache not enabled or not available'; }"
echo ""

echo ""
echo "3. Clearing system caches..."
# Clear any system-level caches
if command -v herd &> /dev/null; then
    echo "   Herd detected - you should restart Herd services"
    echo "   Run: herd restart"
elif command -v valet &> /dev/null; then
    echo "   Valet detected - you should restart Valet services"
    echo "   Run: valet restart"
else
    echo "   No Herd/Valet detected"
fi

echo ""
echo "4. Verifying fixes are in place..."

# Check BackupController fixes
if grep -q "private function createBackup(bool \$includeFiles = true, ?string \$description = ''):" plugins/settings/Controllers/BackupController.php; then
    echo "   ✓ BackupController method signature is fixed"
else
    echo "   ✗ BackupController method signature NOT fixed"
fi

if grep -q "\$description = \$request->input('description', '') ?? '';" plugins/settings/Controllers/BackupController.php; then
    echo "   ✓ Input handling is fixed"
else
    echo "   ✗ Input handling NOT fixed"
fi

if grep -q "use Plugins\\\\Business\\\\Controllers\\\\ImportController;" routes/web.php; then
    echo "   ✓ Routes file namespace is fixed"
else
    echo "   ✗ Routes file namespace NOT fixed"
fi

echo ""
echo "5. Testing PHP syntax..."
echo "   Checking BackupController syntax..."
php -l plugins/settings/Controllers/BackupController.php

echo "   Checking routes syntax..."
php -l routes/web.php

echo ""
echo "=== IMPORTANT: Manual Steps Required ==="
echo ""
echo "The fixes are in place, but you MUST restart your web server to clear cached files:"
echo ""
echo "🔄 RESTART HERD/VALET:"
echo "   Option 1: Open Herd app → Menu → Restart Services"
echo "   Option 2: Run: herd restart"
echo "   Option 3: Run: valet restart"
echo ""
echo "🧪 AFTER RESTARTING, TEST THE BACKUP:"
echo "   1. Go to: http://business.test/settings/backups"
echo "   2. Click 'Create Backup'"
echo "   3. Leave description empty (this was causing the error)"
echo "   4. Click 'Create'"
echo "   5. Should work without 502 error"
echo ""
echo "🔍 IF STILL GETTING 502 ERROR:"
echo "   1. Check: tail -f storage/logs/laravel.log"
echo "   2. Look for new error messages"
echo "   3. The null description error should be gone"
echo ""
echo "=== Cache Clear Complete ==="
